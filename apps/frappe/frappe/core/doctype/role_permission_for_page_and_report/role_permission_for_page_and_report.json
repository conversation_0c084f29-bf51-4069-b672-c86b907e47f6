{"actions": [], "allow_copy": 1, "creation": "2017-02-13 17:33:25.157332", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["set_role_for", "page", "report", "column_break_4", "enable_prepared_report", "roles_permission", "roles_html", "roles"], "fields": [{"fieldname": "set_role_for", "fieldtype": "Select", "in_list_view": 1, "label": "Set Role For", "options": "\nPage\nReport", "reqd": 1}, {"depends_on": "eval:doc.set_role_for == 'Page'", "fieldname": "page", "fieldtype": "Link", "label": "Page", "options": "Page"}, {"depends_on": "eval:doc.set_role_for == 'Report'", "fieldname": "report", "fieldtype": "Link", "label": "Report", "options": "Report"}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"fieldname": "roles_permission", "fieldtype": "Section Break", "label": "Allow Roles"}, {"fieldname": "roles_html", "fieldtype": "HTML", "label": "Roles Html"}, {"fieldname": "roles", "fieldtype": "Table", "hidden": 1, "label": "Roles", "options": "Has Role", "read_only": 1}, {"default": "0", "depends_on": "report", "fieldname": "enable_prepared_report", "fieldtype": "Check", "label": "Enable Prepared Report"}], "hide_toolbar": 1, "issingle": 1, "links": [], "modified": "2022-11-23 12:39:05.750386", "modified_by": "Administrator", "module": "Core", "name": "Role Permission for Page and Report", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "role": "System Manager", "share": 1, "write": 1}], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "states": []}