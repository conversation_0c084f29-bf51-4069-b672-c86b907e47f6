{"actions": [{"action": "frappe.core.doctype.scheduled_job_type.scheduled_job_type.execute_event", "action_type": "Server Action", "label": "Execute"}], "creation": "2019-09-23 14:34:09.205368", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["stopped", "method", "server_script", "scheduler_event", "frequency", "cron_format", "create_log", "status_section", "last_execution", "column_break_9", "next_execution"], "fields": [{"fieldname": "method", "fieldtype": "Data", "in_list_view": 1, "label": "Method", "read_only": 1, "reqd": 1}, {"default": "0", "fieldname": "stopped", "fieldtype": "Check", "label": "Stopped"}, {"default": "0", "depends_on": "eval:doc.frequency==='All'", "fieldname": "create_log", "fieldtype": "Check", "label": "Create Log"}, {"fieldname": "last_execution", "fieldtype": "Datetime", "label": "Last Execution", "read_only": 1}, {"allow_in_quick_entry": 1, "depends_on": "eval:doc.frequency==='Cron'", "description": "<pre>*  *  *  *  *\n┬  ┬  ┬  ┬  ┬\n│  │  │  │  │\n│  │  │  │  └ day of week (0 - 6) (0 is Sunday)\n│  │  │  └───── month (1 - 12)\n│  │  └────────── day of month (1 - 31)\n│  └─────────────── hour (0 - 23)\n└──────────────────── minute (0 - 59)\n\n---\n\n* - Any value\n/ - Step values\n</pre>\n", "fieldname": "cron_format", "fieldtype": "Data", "label": "Cron Format", "read_only": 1}, {"fieldname": "frequency", "fieldtype": "Select", "in_list_view": 1, "in_standard_filter": 1, "label": "Frequency", "options": "All\nHourly\nHourly Long\nDaily\nDaily Long\nWeekly\nWeekly Long\nMonthly\nMonthly Long\nCron\nYearly\nAnnual", "read_only": 1, "reqd": 1}, {"fieldname": "server_script", "fieldtype": "Link", "label": "<PERSON>", "options": "<PERSON>", "read_only": 1, "search_index": 1}, {"fieldname": "next_execution", "fieldtype": "Datetime", "is_virtual": 1, "label": "Next Execution", "read_only": 1}, {"fieldname": "status_section", "fieldtype": "Section Break", "label": "Status"}, {"fieldname": "column_break_9", "fieldtype": "Column Break"}, {"fieldname": "scheduler_event", "fieldtype": "Link", "label": "Scheduler Event", "options": "Scheduler Event", "read_only": 1}], "in_create": 1, "links": [{"link_doctype": "Scheduled Job Log", "link_fieldname": "scheduled_job_type"}], "modified": "2025-01-13 10:39:39.975031", "modified_by": "Administrator", "module": "Core", "name": "Scheduled Job Type", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "method", "track_changes": 1}