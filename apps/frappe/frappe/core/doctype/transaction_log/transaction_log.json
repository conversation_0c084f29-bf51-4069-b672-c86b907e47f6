{"actions": [], "creation": "2018-02-06 11:48:51.270524", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["row_index", "section_break_2", "reference_doctype", "document_name", "column_break_5", "timestamp", "checksum_version", "section_break_8", "previous_hash", "transaction_hash", "chaining_hash", "data", "amended_from"], "fields": [{"fieldname": "row_index", "fieldtype": "Data", "label": "Row Index", "read_only": 1}, {"fieldname": "section_break_2", "fieldtype": "Section Break"}, {"fieldname": "reference_doctype", "fieldtype": "Data", "label": "Reference Document Type", "read_only": 1}, {"fieldname": "document_name", "fieldtype": "Data", "label": "Document Name", "read_only": 1}, {"fieldname": "column_break_5", "fieldtype": "Column Break"}, {"fieldname": "timestamp", "fieldtype": "Datetime", "label": "Timestamp", "read_only": 1}, {"fieldname": "checksum_version", "fieldtype": "Data", "label": "Checksum Version", "read_only": 1}, {"fieldname": "section_break_8", "fieldtype": "Section Break"}, {"fieldname": "previous_hash", "fieldtype": "Small Text", "hidden": 1, "label": "Previous Hash", "read_only": 1}, {"fieldname": "transaction_hash", "fieldtype": "Small Text", "label": "Transaction Hash", "read_only": 1}, {"fieldname": "chaining_hash", "fieldtype": "Small Text", "hidden": 1, "label": "Chaining Hash", "read_only": 1}, {"fieldname": "data", "fieldtype": "Long Text", "hidden": 1, "label": "Data", "read_only": 1}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "Transaction Log", "print_hide": 1, "read_only": 1}], "in_create": 1, "links": [], "modified": "2022-08-03 12:20:54.684305", "modified_by": "Administrator", "module": "Core", "name": "Transaction Log", "owner": "Administrator", "permissions": [{"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Administrator", "share": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}