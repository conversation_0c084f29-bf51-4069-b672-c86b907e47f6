{"creation": "2021-11-23 12:38:52.807353", "docstatus": 0, "doctype": "Form Tour", "first_document": 0, "idx": 0, "include_name_field": 1, "is_standard": 1, "modified": "2021-11-25 17:03:01.646360", "modified_by": "Administrator", "module": "Core", "name": "Doctype", "owner": "Administrator", "reference_doctype": "DocType", "save_on_complete": 1, "steps": [{"description": "Select a Module to which this DocType would belong", "field": "", "fieldname": "module", "fieldtype": "Link", "has_next_condition": 0, "is_table_field": 0, "label": "<PERSON><PERSON><PERSON>", "parent_field": "", "position": "Right", "title": "<PERSON><PERSON><PERSON>"}, {"description": "Check this to make the DocType as Custom", "field": "", "fieldname": "custom", "fieldtype": "Check", "has_next_condition": 1, "is_table_field": 0, "label": "Custom?", "next_step_condition": "eval: doc.custom", "parent_field": "", "position": "Left", "title": "Custom "}, {"description": "A Field (or a docfield) defines a property of a DocType. You can define the column name, label, datatype and more for DocFields. For instance, a ToDo doctype has fields description, status and priority. These ultimately become columns in the database table tabToDo.", "field": "", "fieldname": "fields", "fieldtype": "Table", "has_next_condition": 0, "is_table_field": 0, "label": "Fields", "parent_field": "", "position": "Top", "title": "Fields"}], "title": "Doctype"}