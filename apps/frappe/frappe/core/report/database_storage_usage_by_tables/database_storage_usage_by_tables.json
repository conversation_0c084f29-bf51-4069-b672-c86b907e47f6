{"add_total_row": 1, "columns": [], "creation": "2022-10-19 02:25:24.326791", "disable_prepared_report": 0, "disabled": 0, "docstatus": 0, "doctype": "Report", "filters": [], "idx": 0, "is_standard": "Yes", "modified": "2022-10-19 02:59:00.365307", "modified_by": "Administrator", "module": "Core", "name": "Database Storage Usage By Tables", "owner": "Administrator", "prepared_report": 0, "query": "", "ref_doctype": "<PERSON><PERSON><PERSON>", "report_name": "Database Storage Usage By Tables", "report_type": "Script Report", "roles": [{"role": "System Manager"}]}