{"allow_comments": 0, "allow_delete": 0, "allow_edit": 1, "allow_incomplete": 0, "allow_multiple": 0, "allow_print": 0, "apply_document_permissions": 0, "breadcrumbs": "[{\"title\": _(\"My Account\"), \"route\": \"me\"}]", "client_script": "frappe.web_form.after_load = () => {\n    if (window.location.pathname.endsWith(\"/new\") && frappe.session.user) {\n        let current_path = window.location.href;\n        window.location.href = current_path.replace(\"/new\", \"/\" + frappe.session.user);\n    }\n}", "creation": "2016-09-19 05:16:59.242754", "doc_type": "User", "docstatus": 0, "doctype": "Web Form", "idx": 0, "introduction_text": "", "is_standard": 1, "list_columns": [], "login_required": 1, "max_attachment_size": 0, "modified": "2023-01-18 10:26:26.766414", "modified_by": "Administrator", "module": "Core", "name": "edit-profile", "owner": "Administrator", "published": 1, "route": "update-profile", "show_attachments": 0, "show_list": 0, "show_sidebar": 0, "success_message": "Profile updated successfully.", "success_url": "/me", "title": "Update Profile", "web_form_fields": [{"allow_read_on_all_link_options": 0, "fieldname": "first_name", "fieldtype": "Data", "hidden": 0, "label": "First Name", "max_length": 0, "max_value": 0, "read_only": 0, "reqd": 1, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "middle_name", "fieldtype": "Data", "hidden": 0, "label": "Middle Name (Optional)", "max_length": 0, "max_value": 0, "read_only": 0, "reqd": 0, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "last_name", "fieldtype": "Data", "hidden": 0, "label": "Last Name", "max_length": 0, "max_value": 0, "read_only": 0, "reqd": 0, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "", "fieldtype": "Column Break", "hidden": 0, "max_length": 0, "max_value": 0, "read_only": 0, "reqd": 0, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "description": "", "fieldname": "user_image", "fieldtype": "Attach Image", "hidden": 0, "label": "Profile Picture", "max_length": 0, "max_value": 0, "read_only": 0, "reqd": 0, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldtype": "Section Break", "hidden": 0, "label": "More Information", "max_length": 0, "max_value": 0, "read_only": 0, "reqd": 0, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "phone", "fieldtype": "Data", "hidden": 0, "label": "Phone", "max_length": 0, "max_value": 0, "read_only": 0, "reqd": 0, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "mobile_no", "fieldtype": "Data", "hidden": 0, "label": "Mobile Number", "max_length": 0, "max_value": 0, "read_only": 0, "reqd": 0, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "", "fieldtype": "Column Break", "hidden": 0, "max_length": 0, "max_value": 0, "read_only": 0, "reqd": 0, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "description": "", "fieldname": "language", "fieldtype": "Link", "hidden": 0, "label": "Language", "max_length": 0, "max_value": 0, "options": "Language", "read_only": 0, "reqd": 0, "show_in_filter": 0}]}