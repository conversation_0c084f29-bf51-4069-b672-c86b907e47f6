{"charts": [], "content": "[{\"id\":\"5nnLaQeoFa\",\"type\":\"header\",\"data\":{\"text\":\"<span style=\\\"font-size: 18px; letter-spacing: 0.18px;\\\"><b>Get started</b><br></span>\",\"col\":12}},{\"id\":\"HXRmktXYHy\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"DocType\",\"col\":3}},{\"id\":\"pYALX3MwBW\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Customize Form\",\"col\":3}},{\"id\":\"XC78DuYB65\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Report\",\"col\":3}},{\"id\":\"XPm50Ppq3J\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Client Script\",\"col\":3}},{\"id\":\"yoU6nWiT83\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Server Script\",\"col\":3}},{\"id\":\"5UgFESBY0N\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Print Format Builder\",\"col\":3}},{\"id\":\"0gE0s-S70E\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"System Settings\",\"col\":3}},{\"id\":\"62hseENHbd\",\"type\":\"spacer\",\"data\":{\"col\":12}},{\"id\":\"tOCrOgLW1G\",\"type\":\"header\",\"data\":{\"text\":\"<span style=\\\"font-size: 18px; letter-spacing: 0.18px;\\\"><b>Components to build your app</b></span>\",\"col\":12}},{\"id\":\"cJ6CVsa8qW\",\"type\":\"card\",\"data\":{\"card_name\":\"Models\",\"col\":4}},{\"id\":\"MmEJpjEdGR\",\"type\":\"card\",\"data\":{\"card_name\":\"Views\",\"col\":4}},{\"id\":\"2ZdtgxQZqq\",\"type\":\"card\",\"data\":{\"card_name\":\"Customization\",\"col\":4}},{\"id\":\"NPFolijIcb\",\"type\":\"card\",\"data\":{\"card_name\":\"Scripting\",\"col\":4}},{\"id\":\"BIHjudL0T_\",\"type\":\"card\",\"data\":{\"card_name\":\"Modules\",\"col\":4}},{\"id\":\"iK3JQ9RXJE\",\"type\":\"card\",\"data\":{\"card_name\":\"Packages\",\"col\":4}},{\"id\":\"TiO9FCUUeC\",\"type\":\"card\",\"data\":{\"card_name\":\"System Logs\",\"col\":4}}]", "creation": "2021-01-02 10:51:16.579957", "custom_blocks": [], "docstatus": 0, "doctype": "Workspace", "for_user": "", "hide_custom": 0, "icon": "tool", "idx": 1, "is_hidden": 0, "label": "Build", "links": [{"description": "Customize properties, naming, fields and more for standard doctypes", "hidden": 0, "is_query_report": 0, "label": "Customization", "link_count": 4, "link_type": "DocType", "onboard": 0, "type": "Card Break"}, {"hidden": 0, "is_query_report": 0, "label": "Customize Form", "link_count": 0, "link_to": "Customize Form", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Custom Field", "link_count": 0, "link_to": "Custom Field", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Custom Translation", "link_count": 0, "link_to": "Translation", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "<PERSON><PERSON><PERSON>", "link_count": 0, "link_to": "<PERSON><PERSON><PERSON>", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"description": "Group your custom doctypes under modules", "hidden": 0, "is_query_report": 0, "label": "<PERSON><PERSON><PERSON>", "link_count": 2, "link_type": "DocType", "onboard": 0, "type": "Card Break"}, {"hidden": 0, "is_query_report": 0, "label": "<PERSON><PERSON><PERSON>", "link_count": 0, "link_to": "<PERSON><PERSON><PERSON>", "link_type": "DocType", "onboard": 0, "only_for": "", "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Module Onboarding", "link_count": 0, "link_to": "Module Onboarding", "link_type": "DocType", "onboard": 0, "only_for": "", "type": "Link"}, {"description": "Monitor logs for errors, background jobs, communications, and user activity", "hidden": 0, "is_query_report": 0, "label": "System Logs", "link_count": 5, "link_type": "DocType", "onboard": 0, "type": "Card Break"}, {"hidden": 0, "is_query_report": 0, "label": "Background Jobs", "link_count": 0, "link_to": "RQ Job", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Scheduled Jobs Logs", "link_count": 0, "link_to": "Scheduled Job Log", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "<PERSON><PERSON><PERSON>", "link_count": 0, "link_to": "<PERSON><PERSON><PERSON>", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Communication Logs", "link_count": 0, "link_to": "Communication", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Activity Log", "link_count": 0, "link_to": "Activity Log", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"description": "Packages are lightweight apps (collection of Module Defs) that can be created, imported, or released right from the UI", "hidden": 0, "is_query_report": 0, "label": "Packages", "link_count": 2, "link_type": "DocType", "onboard": 0, "type": "Card Break"}, {"hidden": 0, "is_query_report": 0, "label": "Package", "link_count": 0, "link_to": "Package", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Package Import", "link_count": 0, "link_to": "Package Import", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"description": "Automate processes and extend standard functionality using scripts and background jobs", "hidden": 0, "is_query_report": 0, "label": "Scripting", "link_count": 3, "link_type": "DocType", "onboard": 0, "type": "Card Break"}, {"hidden": 0, "is_query_report": 0, "label": "<PERSON>", "link_count": 0, "link_to": "<PERSON>", "link_type": "DocType", "onboard": 0, "only_for": "", "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "<PERSON><PERSON>", "link_count": 0, "link_to": "<PERSON><PERSON>", "link_type": "DocType", "onboard": 0, "only_for": "", "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Scheduled Job Type", "link_count": 0, "link_to": "Scheduled Job Type", "link_type": "DocType", "onboard": 0, "only_for": "", "type": "Link"}, {"description": "Build your own reports, print formats, and dashboards. Create personalized workspaces for easier navigation", "hidden": 0, "is_query_report": 0, "label": "Views", "link_count": 5, "link_type": "DocType", "onboard": 0, "type": "Card Break"}, {"hidden": 0, "is_query_report": 0, "label": "Report", "link_count": 0, "link_to": "Report", "link_type": "DocType", "onboard": 0, "only_for": "", "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Print Format", "link_count": 0, "link_to": "Print Format", "link_type": "DocType", "onboard": 0, "only_for": "", "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Workspace", "link_count": 0, "link_to": "Workspace", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Dashboard", "link_count": 0, "link_to": "Dashboard", "link_type": "DocType", "onboard": 0, "only_for": "", "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Dashboard Chart", "link_count": 0, "link_to": "Dashboard Chart", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"description": "Create new forms and views with doctypes. Set up multi-level workflows for approval", "hidden": 0, "is_query_report": 0, "label": "Models", "link_count": 2, "link_type": "DocType", "onboard": 0, "type": "Card Break"}, {"hidden": 0, "is_query_report": 0, "label": "DocType", "link_count": 0, "link_to": "DocType", "link_type": "DocType", "onboard": 0, "only_for": "", "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Workflow", "link_count": 0, "link_to": "Workflow", "link_type": "DocType", "onboard": 0, "only_for": "", "type": "Link"}], "modified": "2024-01-24 12:27:44.769958", "modified_by": "Administrator", "module": "Core", "name": "Build", "number_cards": [], "owner": "Administrator", "parent_page": "", "public": 1, "quick_lists": [], "restrict_to_domain": "", "roles": [], "sequence_id": 27.0, "shortcuts": [{"color": "Grey", "doc_view": "List", "label": "Print Format Builder", "link_to": "print-format-builder", "type": "Page"}, {"color": "Grey", "doc_view": "List", "label": "System Settings", "link_to": "System Settings", "type": "DocType", "url": "https://frappe.school/courses/frappe-framework-course?utm_source=in_app"}, {"color": "Grey", "doc_view": "List", "label": "<PERSON><PERSON>", "link_to": "<PERSON><PERSON>", "type": "DocType"}, {"doc_view": "", "label": "DocType", "link_to": "DocType", "type": "DocType"}, {"doc_view": "", "label": "Customize Form", "link_to": "Customize Form", "type": "DocType"}, {"color": "Grey", "doc_view": "List", "label": "<PERSON>", "link_to": "<PERSON>", "type": "DocType"}, {"doc_view": "", "label": "Report", "link_to": "Report", "type": "DocType"}], "title": "Build"}