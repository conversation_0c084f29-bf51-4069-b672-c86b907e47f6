{"charts": [], "content": "[{\"id\":\"YpGCeLfign\",\"type\":\"header\",\"data\":{\"text\":\"<span class=\\\"h4\\\"><b>Your Shortcuts</b></span>\",\"col\":12}},{\"id\":\"b7abeqw4NZ\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"User\",\"col\":3}},{\"id\":\"eghSJPhZRC\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Role\",\"col\":3}},{\"id\":\"uAzl_lT_C0\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Permission Manager\",\"col\":3}},{\"id\":\"EpBz2lplSt\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"User Profile\",\"col\":3}},{\"id\":\"vHWhzaFoAH\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"User Type\",\"col\":3}},{\"id\":\"oFB4l28FMU\",\"type\":\"spacer\",\"data\":{\"col\":12}},{\"id\":\"yJNNylguxk\",\"type\":\"header\",\"data\":{\"text\":\"<span class=\\\"h4\\\"><b>Reports &amp; Masters</b></span>\",\"col\":12}},{\"id\":\"NMpIkExl3i\",\"type\":\"card\",\"data\":{\"card_name\":\"Users\",\"col\":4}},{\"id\":\"VepG3durKm\",\"type\":\"card\",\"data\":{\"card_name\":\"Logs\",\"col\":4}},{\"id\":\"S9FeWt7xXE\",\"type\":\"card\",\"data\":{\"card_name\":\"Permissions\",\"col\":4}}]", "creation": "2020-03-02 15:12:16.754449", "custom_blocks": [], "docstatus": 0, "doctype": "Workspace", "for_user": "", "hide_custom": 0, "icon": "users", "idx": 0, "is_hidden": 0, "label": "Users", "links": [{"hidden": 0, "is_query_report": 0, "label": "Logs", "link_count": 0, "onboard": 0, "type": "Card Break"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Activity Log", "link_count": 0, "link_to": "Activity Log", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Access Log", "link_count": 0, "link_to": "Access Log", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Permissions", "link_count": 0, "onboard": 0, "type": "Card Break"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Role Permissions Manager", "link_count": 0, "link_to": "permission-manager", "link_type": "Page", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "User Permissions", "link_count": 0, "link_to": "User Permission", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Role Permission for Page and Report", "link_count": 0, "link_to": "Role Permission for Page and Report", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "User", "hidden": 0, "is_query_report": 1, "label": "Permitted Documents For User", "link_count": 0, "link_to": "Permitted Documents For User", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "DocShare", "hidden": 0, "is_query_report": 0, "label": "Document Share Report", "link_count": 0, "link_to": "Document Share Report", "link_type": "Report", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Users", "link_count": 4, "link_type": "DocType", "onboard": 0, "type": "Card Break"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "User", "link_count": 0, "link_to": "User", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Role", "link_count": 0, "link_to": "Role", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Role Profile", "link_count": 0, "link_to": "Role Profile", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Module Profile", "link_count": 0, "link_to": "Module Profile", "link_type": "DocType", "onboard": 0, "type": "Link"}], "modified": "2024-01-02 15:39:13.811700", "modified_by": "Administrator", "module": "Core", "name": "Users", "number_cards": [], "owner": "Administrator", "parent_page": "", "public": 1, "quick_lists": [], "restrict_to_domain": "", "roles": [], "sequence_id": 13.0, "shortcuts": [{"label": "User", "link_to": "User", "type": "DocType"}, {"label": "Role", "link_to": "Role", "type": "DocType"}, {"label": "Permission Manager", "link_to": "permission-manager", "type": "Page"}, {"label": "User Profile", "link_to": "user-profile", "type": "Page"}, {"doc_view": "", "label": "User Type", "link_to": "User Type", "type": "DocType"}], "title": "Users"}