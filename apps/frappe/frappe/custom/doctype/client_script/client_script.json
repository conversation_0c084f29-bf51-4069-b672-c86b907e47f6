{"actions": [], "allow_import": 1, "autoname": "Prompt", "creation": "2013-01-10 16:34:01", "description": "Adds a custom client script to a DocType", "doctype": "DocType", "document_type": "Document", "engine": "InnoDB", "field_order": ["dt", "view", "column_break_3", "module", "enabled", "section_break_6", "script", "sample"], "fields": [{"fieldname": "dt", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "DocType", "oldfieldname": "dt", "oldfieldtype": "Link", "options": "DocType", "reqd": 1, "set_only_once": 1}, {"fieldname": "script", "fieldtype": "Code", "label": "<PERSON><PERSON><PERSON>", "oldfieldname": "script", "oldfieldtype": "Code", "options": "JS"}, {"fieldname": "sample", "fieldtype": "HTML", "label": "<PERSON><PERSON>"}, {"default": "0", "fieldname": "enabled", "fieldtype": "Check", "label": "Enabled"}, {"default": "Form", "fieldname": "view", "fieldtype": "Select", "in_list_view": 1, "label": "Apply To", "options": "List\nForm", "set_only_once": 1}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "module", "fieldtype": "Link", "label": "Module (for export)", "options": "<PERSON><PERSON><PERSON>"}, {"fieldname": "section_break_6", "fieldtype": "Section Break"}], "icon": "fa fa-glass", "idx": 1, "index_web_pages_for_search": 1, "links": [], "modified": "2023-12-08 15:52:37.525003", "modified_by": "Administrator", "module": "Custom", "name": "<PERSON><PERSON>", "naming_rule": "Set by user", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Administrator", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}