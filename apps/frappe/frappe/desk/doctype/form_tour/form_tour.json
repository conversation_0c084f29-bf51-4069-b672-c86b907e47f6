{"actions": [], "autoname": "field:title", "creation": "2021-05-21 23:02:52.242721", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["title", "view_name", "workspace_name", "list_name", "report_name", "dashboard_name", "new_document_form", "page_name", "reference_doctype", "module", "column_break_6", "ui_tour", "track_steps", "is_standard", "save_on_complete", "first_document", "include_name_field", "page_route", "section_break_3", "steps"], "fields": [{"depends_on": "eval:(!doc.ui_tour || doc.ui_tour && [\"Workspaces\", \"Page\", \"Tree\"].indexOf(doc.view_name) == -1);", "fieldname": "reference_doctype", "fieldtype": "Link", "in_list_view": 1, "label": "Reference Document", "mandatory_depends_on": "eval:(!doc.ui_tour)", "options": "DocType"}, {"depends_on": "eval:(doc.ui_tour || doc.reference_doctype)", "fieldname": "steps", "fieldtype": "Table", "label": "Steps", "options": "Form Tour Step", "reqd": 1}, {"fieldname": "section_break_3", "fieldtype": "Section Break"}, {"fieldname": "title", "fieldtype": "Data", "label": "Title", "reqd": 1, "unique": 1}, {"default": "0", "depends_on": "eval:(!doc.ui_tour)", "fieldname": "save_on_complete", "fieldtype": "Check", "label": "Save on Completion"}, {"default": "0", "fieldname": "is_standard", "fieldtype": "Check", "label": "Is Standard"}, {"depends_on": "eval: doc.ui_tour && doc.is_standard", "fetch_from": "reference_doctype.module", "fieldname": "module", "fieldtype": "Link", "label": "<PERSON><PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON>", "read_only": 1}, {"fieldname": "column_break_6", "fieldtype": "Column Break"}, {"default": "0", "depends_on": "eval:(!doc.ui_tour)", "fieldname": "first_document", "fieldtype": "Check", "label": "Show First Document Tour"}, {"default": "0", "depends_on": "eval:(!doc.ui_tour && !doc.first_document)", "fieldname": "include_name_field", "fieldtype": "Check", "label": "Include Name Field"}, {"default": "0", "fieldname": "ui_tour", "fieldtype": "Check", "label": "UI Tour", "set_only_once": 1}, {"fieldname": "page_route", "fieldtype": "Small Text", "hidden": 1, "label": "Page Route"}, {"depends_on": "eval:(doc.ui_tour && doc.view_name == \"List\" && doc.list_name == \"Dashboard\")", "fetch_from": ".", "fieldname": "dashboard_name", "fieldtype": "Link", "label": "Select Dashboard", "options": "Dashboard"}, {"depends_on": "ui_tour", "fieldname": "view_name", "fieldtype": "Select", "label": "View", "mandatory_depends_on": "ui_tour", "options": "Workspaces\nList\nForm\nTree\nPage"}, {"depends_on": "eval:(doc.ui_tour && doc.view_name == \"Workspaces\")", "fetch_from": ".", "fieldname": "workspace_name", "fieldtype": "Link", "label": "Select Workspace", "options": "Workspace"}, {"depends_on": "eval:(doc.ui_tour && doc.view_name == \"Page\")", "fetch_from": ".", "fieldname": "page_name", "fieldtype": "Link", "label": "Select Page", "mandatory_depends_on": "eval:(doc.ui_tour && doc.view_name == \"Page\")", "options": "Page"}, {"default": "List", "depends_on": "eval:(doc.ui_tour && doc.view_name == \"List\")", "fetch_from": ".", "fieldname": "list_name", "fieldtype": "Select", "label": "Select List View", "mandatory_depends_on": "eval:(doc.ui_tour && doc.view_name == \"List\")", "options": "List\nReport\nDashboard\nKanban\nGantt\nCalendar\nFile\nImage\nInbox\nMap"}, {"depends_on": "eval:(doc.ui_tour && doc.view_name == \"List\" && doc.list_name == \"Report\")", "fetch_from": ".", "fieldname": "report_name", "fieldtype": "Link", "label": "Select Report", "options": "Report"}, {"default": "0", "depends_on": "ui_tour", "description": "The next tour will start from where the user left off.", "fieldname": "track_steps", "fieldtype": "Check", "label": "Track Steps"}, {"default": "0", "depends_on": "eval: (doc.ui_tour && doc.view_name == \"Form\")", "fieldname": "new_document_form", "fieldtype": "Check", "label": "New Document Form"}], "index_web_pages_for_search": 1, "links": [], "modified": "2023-08-28 20:24:42.594360", "modified_by": "Administrator", "module": "Desk", "name": "Form Tour", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"read": 1, "role": "Desk User"}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}