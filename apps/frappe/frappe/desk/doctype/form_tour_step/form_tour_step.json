{"actions": [], "creation": "2021-05-21 23:05:45.342114", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["ui_tour", "is_table_field", "section_break_2", "title", "parent_fieldname", "fieldname", "element_selector", "parent_element_selector", "description", "ondemand_description", "column_break_2", "position", "hide_buttons", "popover_element", "modal_trigger", "offset_x", "offset_y", "next_on_click", "label", "fieldtype", "has_next_condition", "next_step_condition", "next_form_tour", "section_break_13", "child_doctype"], "fields": [{"fieldname": "title", "fieldtype": "Data", "in_list_view": 1, "label": "Title", "reqd": 1}, {"columns": 4, "fieldname": "description", "fieldtype": "HTML Editor", "ignore_xss_filter": 1, "in_list_view": 1, "label": "Description", "reqd": 1}, {"depends_on": "eval: (!doc.ui_tour && (!doc.is_table_field || (doc.is_table_field && doc.parent_fieldname)))", "fieldname": "fieldname", "fieldtype": "Select", "label": "Fieldname", "mandatory_depends_on": "eval: (!doc.ui_tour)"}, {"depends_on": "eval:(!doc.ui_tour)", "fieldname": "label", "fieldtype": "Data", "in_list_view": 1, "label": "Label", "read_only": 1}, {"fieldname": "column_break_2", "fieldtype": "Column Break"}, {"default": "Bottom", "fieldname": "position", "fieldtype": "Select", "label": "Position", "options": "Left\nLeft Center\nLeft Bottom\nTop\nTop Center\nTop Right\nRight\nRight Center\nRight Bottom\nBottom\nBottom Center\nBottom Right\nMid Center"}, {"depends_on": "has_next_condition", "fieldname": "next_step_condition", "fieldtype": "Code", "label": "Next Step Condition", "oldfieldname": "condition", "options": "JS"}, {"default": "0", "depends_on": "eval:(!doc.ui_tour)", "fieldname": "has_next_condition", "fieldtype": "Check", "label": "Has Next Condition"}, {"default": "0", "depends_on": "eval:(!doc.ui_tour)", "fieldname": "fieldtype", "fieldtype": "Data", "label": "Fieldtype", "read_only": 1}, {"default": "0", "depends_on": "eval:(!doc.ui_tour)", "fieldname": "is_table_field", "fieldtype": "Check", "label": "Is Table Field"}, {"fieldname": "section_break_2", "fieldtype": "Section Break"}, {"fieldname": "section_break_13", "fieldtype": "Section Break", "hidden": 1, "label": "Hidden Fields"}, {"fieldname": "child_doctype", "fieldtype": "Data", "hidden": 1, "label": "Child Doctype", "read_only": 1}, {"depends_on": "eval: (!doc.ui_tour || doc.is_table_field)", "fieldname": "parent_fieldname", "fieldtype": "Select", "label": "Parent Field", "mandatory_depends_on": "is_table_field"}, {"default": "0", "fetch_from": "next_form_tour.ui_tour", "fieldname": "ui_tour", "fieldtype": "Check", "in_list_view": 1, "label": "UI Tour"}, {"depends_on": "eval:(doc.ui_tour)", "description": "CSS selector for the element you want to highlight.", "fieldname": "element_selector", "fieldtype": "Data", "label": "Element Selector", "mandatory_depends_on": "eval:(doc.ui_tour)"}, {"depends_on": "eval:(doc.ui_tour)", "description": "Mozilla doesn't support :has() so you can pass parent selector here as workaround", "fieldname": "parent_element_selector", "fieldtype": "Data", "label": "Parent Element Selector"}, {"depends_on": "eval:(doc.ui_tour)", "fieldname": "next_form_tour", "fieldtype": "Link", "label": "Next Form Tour", "options": "Form Tour"}, {"default": "0", "depends_on": "eval:(doc.ui_tour)", "description": "Hide Previous, Next and Close button on highlight dialog.", "fieldname": "hide_buttons", "fieldtype": "Check", "label": "<PERSON><PERSON>"}, {"default": "0", "depends_on": "eval:(doc.ui_tour)", "description": "Move to next step when clicked inside highlighted area.", "fieldname": "next_on_click", "fieldtype": "Check", "label": "Next on Click"}, {"default": "0", "depends_on": "eval:(doc.ui_tour)", "description": "when clicked on element it will focus popover if present.", "fieldname": "popover_element", "fieldtype": "Check", "label": "Popover Element"}, {"default": "0", "depends_on": "eval:(doc.ui_tour)", "fieldname": "offset_x", "fieldtype": "Int", "label": "Offset X"}, {"default": "0", "depends_on": "eval:(doc.ui_tour)", "fieldname": "offset_y", "fieldtype": "Int", "label": "Offset Y"}, {"default": "0", "depends_on": "eval:(doc.ui_tour)", "description": "Enable if on click\nopens modal.", "fieldname": "modal_trigger", "fieldtype": "Check", "label": "<PERSON><PERSON>"}, {"columns": 4, "depends_on": "eval: (doc.popover_element || doc.modal_trigger)", "fieldname": "ondemand_description", "fieldtype": "HTML Editor", "ignore_xss_filter": 1, "in_list_view": 1, "label": "Popover or Modal Description"}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2023-05-23 13:09:15.923043", "modified_by": "Administrator", "module": "Desk", "name": "Form Tour Step", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}