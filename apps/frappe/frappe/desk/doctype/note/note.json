{"actions": [], "autoname": "hash", "creation": "2013-05-24 13:41:00", "doctype": "DocType", "document_type": "Document", "engine": "InnoDB", "field_order": ["title", "public", "notify_on_login", "notify_on_every_login", "expire_notification_on", "content", "seen_by_section", "seen_by"], "fields": [{"fieldname": "title", "fieldtype": "Data", "in_list_view": 1, "label": "Title", "reqd": 1}, {"bold": 1, "default": "0", "fieldname": "public", "fieldtype": "Check", "label": "Public", "permlevel": 1, "print_hide": 1}, {"bold": 1, "default": "0", "depends_on": "public", "fieldname": "notify_on_login", "fieldtype": "Check", "label": "Notify users with a popup when they log in", "permlevel": 1}, {"bold": 1, "default": "0", "depends_on": "notify_on_login", "description": "If enabled, users will be notified every time they login. If not enabled, users will only be notified once.", "fieldname": "notify_on_every_login", "fieldtype": "Check", "label": "Notify Users On Every Login", "permlevel": 1}, {"depends_on": "eval:doc.notify_on_login && doc.public", "fieldname": "expire_notification_on", "fieldtype": "Date", "label": "Expire Notification On", "permlevel": 1, "search_index": 1}, {"bold": 1, "description": "Help: To link to another record in the system, use \"/app/note/[Note Name]\" as the Link URL. (don't use \"http://\")", "fieldname": "content", "fieldtype": "Text Editor", "in_global_search": 1, "label": "Content"}, {"collapsible": 1, "depends_on": "notify_on_login", "fieldname": "seen_by_section", "fieldtype": "Section Break", "label": "Seen By", "permlevel": 1}, {"fieldname": "seen_by", "fieldtype": "Table", "label": "Seen By Table", "options": "Note Seen By", "permlevel": 1}], "icon": "fa fa-file-text", "idx": 1, "links": [], "modified": "2023-12-08 15:52:37.525003", "modified_by": "Administrator", "module": "Desk", "name": "Note", "naming_rule": "Random", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"permlevel": 1, "read": 1, "role": "System Manager", "write": 1}, {"permlevel": 2, "read": 1, "role": "System Manager", "write": 1}, {"export": 1, "print": 1, "read": 1, "report": 1, "role": "Desk User"}, {"create": 1, "delete": 1, "email": 1, "if_owner": 1, "role": "Desk User", "share": 1, "write": 1}, {"permlevel": 1, "read": 1, "role": "Desk User"}], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "title", "track_changes": 1}