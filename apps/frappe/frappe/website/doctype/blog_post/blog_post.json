{"actions": [], "allow_guest_to_view": 1, "allow_import": 1, "creation": "2013-03-28 10:35:30", "doctype": "DocType", "document_type": "Setup", "engine": "InnoDB", "field_order": ["title", "blog_category", "blogger", "route", "read_time", "column_break_3", "published_on", "published", "featured", "hide_cta", "enable_email_notification", "disable_comments", "disable_likes", "section_break_5", "blog_intro", "content_type", "content", "content_md", "content_html", "email_sent", "meta_tags", "meta_title", "meta_description", "column_break_18", "meta_image", "section_break_20", "google_preview"], "fields": [{"fieldname": "title", "fieldtype": "Data", "in_global_search": 1, "label": "Title", "no_copy": 1, "reqd": 1}, {"fieldname": "published_on", "fieldtype": "Date", "label": "Published On"}, {"default": "0", "fieldname": "published", "fieldtype": "Check", "hidden": 1, "label": "Published"}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "blog_category", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Blog Category", "options": "Blog Category", "reqd": 1}, {"fieldname": "blogger", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Blogger", "options": "Blogger", "reqd": 1}, {"fieldname": "route", "fieldtype": "Data", "label": "Route", "unique": 1}, {"fieldname": "section_break_5", "fieldtype": "Section Break"}, {"description": "Description for listing page, in plain text, only a couple of lines. (max 200 characters)", "fieldname": "blog_intro", "fieldtype": "Small Text", "label": "Blog Intro"}, {"default": "<PERSON><PERSON>", "fieldname": "content_type", "fieldtype": "Select", "label": "Content Type", "options": "Markdown\nRich Text\nHTML", "reqd": 1}, {"depends_on": "eval:doc.content_type === 'Rich Text'", "fieldname": "content", "fieldtype": "Text Editor", "ignore_xss_filter": 1, "in_global_search": 1, "label": "Content"}, {"depends_on": "eval:doc.content_type === 'Markdown'", "fieldname": "content_md", "fieldtype": "Markdown Editor", "ignore_xss_filter": 1, "label": "Content (Markdown)"}, {"depends_on": "eval:doc.content_type === 'HTML'", "fieldname": "content_html", "fieldtype": "HTML Editor", "ignore_xss_filter": 1, "label": "Content (HTML)"}, {"default": "0", "fieldname": "email_sent", "fieldtype": "Check", "hidden": 1, "label": "<PERSON><PERSON>"}, {"default": "0", "fieldname": "disable_comments", "fieldtype": "Check", "label": "Disable Comments"}, {"fieldname": "meta_description", "fieldtype": "Small Text", "label": "Meta Description"}, {"fieldname": "column_break_18", "fieldtype": "Column Break"}, {"fieldname": "meta_image", "fieldtype": "Attach Image", "label": "Meta Image", "mandatory_depends_on": "eval:doc.featured"}, {"fieldname": "section_break_20", "fieldtype": "Section Break"}, {"description": "This is an example Google SERP Preview.", "fieldname": "google_preview", "fieldtype": "HTML", "label": "Google Snippet Preview", "read_only": 1}, {"fieldname": "meta_tags", "fieldtype": "Section Break", "label": "Meta Tags"}, {"description": "in minutes", "fieldname": "read_time", "fieldtype": "Int", "hidden": 1, "label": "Read Time", "read_only": 1}, {"default": "0", "fieldname": "featured", "fieldtype": "Check", "label": "Featured"}, {"default": "0", "fieldname": "hide_cta", "fieldtype": "Check", "hidden": 1, "label": "Hide CTA"}, {"fieldname": "meta_title", "fieldtype": "Data", "label": "Meta Title", "length": 60}, {"default": "1", "description": "Enable email notification for any comment or likes received on your Blog Post.", "fieldname": "enable_email_notification", "fieldtype": "Check", "label": "Enable Email Notification"}, {"default": "0", "fieldname": "disable_likes", "fieldtype": "Check", "label": "Disable Likes"}], "has_web_view": 1, "icon": "fa fa-quote-left", "idx": 1, "index_web_pages_for_search": 1, "is_published_field": "published", "links": [], "make_attachments_public": 1, "modified": "2023-02-17 11:31:32.223524", "modified_by": "Administrator", "module": "Website", "name": "Blog Post", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Website Manager", "share": 1, "write": 1}, {"create": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Blogger", "share": 1, "write": 1}], "route": "blog", "sort_field": "modified", "sort_order": "ASC", "states": [], "title_field": "title", "track_changes": 1}