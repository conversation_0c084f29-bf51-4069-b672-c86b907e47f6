{% extends "templates/web.html" %}

{% block meta_block %}
	{% include "templates/includes/meta_block.html" %}
{% endblock %}

{% block page_content %}
<div class="blog-container">
	<article class="blog-content" itemscope itemtype="http://schema.org/BlogPosting">
		<!-- begin blog content -->
		<div class="blog-header">
			<div>
				<a class="mr-2" href="/blog">{{ _('Blog') }}</a>
				<span class="text-muted">/</span>
				<a class="ml-2" href="/{{ category.route }}">{{ category.title }}</a>
			</div>
			<h1 itemprop="headline" class="blog-title">{{ title }}</h1>
			<p class="blog-intro">
				{{ blog_intro }}
			</p>
			<div class="text-muted">
				<time datetime="{{ published_on }}">{{ frappe.format_date(published_on) }}</time>
				{%- if read_time -%}
				&nbsp;&middot;
				<span>{{ read_time }} {{ _('min read') }} </span>
				{%- endif -%}
			</div>
		</div>
		<hr class="my-5">
		<div itemprop="articleBody" class="from-markdown">
			{{ content }}
		</div>
		<!-- end blog content -->
	</article>
	{%- if enable_cta -%}
		{{ web_block(
			"Section With Small CTA",
			values=cta,
			add_container=0,
			add_top_padding=0,
			add_bottom_padding=0,
			css_class="my-5"
		) }}
	{%- endif -%}
	<div class="blog-footer">
		<div class="blog-feedback">
			{% if not disable_likes %}
				{% include 'templates/includes/likes/likes.html' %}
			{% endif %}
		</div>
		{% if social_links %}
			<div>
				{% for link in social_links %}
					<a href="{{ link.link }}" class="text-muted ml-2 fa fa-{{ link.icon }}" target="_blank"></a>
				{% endfor %}
			</div>
		{% endif %}
		<div>
			{{ _('Published on') }} <time datetime="{{ published_on }}">{{ frappe.format_date(published_on) }}</time>
		</div>
	</div>

	{% if blogger_info %}
		<hr class="mt-2 mb-5">
		{% include "templates/includes/blog/blogger.html" %}
	{% endif %}

	{% if not disable_comments %}
		<div class="blog-comments">
			{% include 'templates/includes/comments/comments.html' %}
		</div>
	{% endif %}

</div>
<script>
	frappe.ready(() => {
		frappe.set_search_path("/blog");

		// scroll to comment or like section if url contain hash
		if (window.location.hash) {
			var hash = window.location.hash;

			if ($(hash).length) {
				$('html, body').animate({
					scrollTop: $(hash).offset().top - 100
				}, 900, 'swing');
			}
		}
	})
</script>
{% endblock %}
