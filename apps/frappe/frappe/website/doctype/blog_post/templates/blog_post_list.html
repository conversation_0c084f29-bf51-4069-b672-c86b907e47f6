{% extends "templates/web.html" %}
{% block title %}{{ blog_title or _("Blog") }}{% endblock %}
{% block hero %}{% endblock %}

{% block page_content %}

<div class="row py-8">
	<div class="col-md-8">
		<div class="hero">
			<div class="hero-content">
				<h1>{{ blog_title or _('Blog') }}</h1>
				<p>{{ blog_introduction or '' }}</p>
			</div>
		</div>

		{%- if browse_by_category -%}
		<div style="max-width: 20rem">
			<label for="category-select" class="sr-only">{{ _("Browse by category") }}</label>
			<select id="category-select" class="custom-select" onchange="window.location.pathname = this.value">
				<option value="" {{ not frappe.form_dict.category and "selected" or "" }} disabled>
					{{ _("Browse by category") }}
				</option>
				{%- if frappe.form_dict.category -%}
				<option value="blog">{{ _("Show all blogs") }}</option>
				{%- endif -%}
				{%- for category in blog_categories -%}
				<option value="{{ category.route }}" {{ frappe.form_dict.category == category.name and "selected" or "" }}>
					{{ _(category.title) }}
				</option>
				{%- endfor -%}
			</select>
		</div>
		{%- endif -%}
	</div>
</div>

<div class="blog-list-content">
	<div data-doctype="{{ doctype }}" data-txt="{{ (txt or '') | e }}">
		{% if not result -%}
		<div class="text-muted" style="min-height: 300px;">
			{{ no_result_message or _("Nothing to show") }}
		</div>
		{% else %}
		<div id="blog-list" class="blog-list result row">
			{% for item in result %}
			{{ item }}
			{% endfor %}
		</div>
		{% endif %}
		<button class="btn btn-light btn-more btn {% if not show_more -%} hidden {%- endif %}">{{ _("Load More") }}</button>
	</div>
</div>
{% endblock %}

{% block script %}
<script>
	frappe.ready(() => {
		let result_wrapper = $(".blog-list.result");
		let next_start = {{ next_start or 0 }};

		$(".blog-list-content .btn-more").on("click", function() {
			let $btn = $(this);
			let args = $.extend(frappe.utils.get_query_params(), {
				doctype: "Blog Post",
				category: {{ frappe.form_dict.category|tojson or "''"}},
				limit_start: next_start,
				pathname: location.pathname,
			});
			$btn.prop("disabled", true);
			frappe.call('frappe.www.list.get', args)
				.then(r => {
					var data = r.message;
					next_start = data.next_start;
					$.each(data.result, function(i, d) {
						$(d).appendTo(result_wrapper);
					});
					toggle_more(data.show_more);
				})
				.always(() => {
					$btn.prop("disabled", false);
				});
		});

		function toggle_more(show) {
			if (!show) {
				$(".btn-more").addClass("hide");
			}
		}
	});
</script>
{% endblock %}
