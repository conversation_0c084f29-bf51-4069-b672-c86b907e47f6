{% from "frappe/templates/includes/avatar_macro.html" import avatar %}

{%- set post = doc -%}
<div class="blog-card col-sm-12 {{ 'col-md-8' if post.featured else 'col-md-4' }}">
	<div class="card h-100">
		<div class="card-img-top">
			{% if post.cover_image %}
				<img src="{{ post.cover_image }}" alt="{{post.title}} - Cover Image">
			{% else %}
				<div class="default-cover">
					<span>{{ post.title }}</span>
				</div>
			{% endif %}
		</div>
		<div class="card-body">
			<div>
				<div class="text-muted small text-uppercase">
					{%- if post.featured -%}
						<span class="text-body">{{ _('Featured') }} &middot; </span>
					{%- endif -%}
					<span>{{ post.category.title }}</span>
				</div>
				{%- if post.featured -%}
					<h5 class="mt-1"><span class="text-dark">{{ post.title }}</span></h5>
				{%- else -%}
					<h5 class="mt-1"><span class="text-dark">{{ post.title }}</span></h5>
				{%- endif -%}
				<p class="post-description text-muted">{{ post.intro }}</p>
			</div>
			<div class="blog-card-footer">
				{{  avatar(full_name=post.full_name, image=post.avatar, size='avatar-medium') }}
				<div class="text-muted">
					<a href="/blog?blogger={{ post.blogger }}">{{ post.full_name }}</a>
					<div class="small">
						{{ frappe.format_date(post.published_on) }}
						{% if post.read_time %} &middot; {{ post.read_time }} {{ _('min read') }} {% endif %}
					</div>
				</div>
			</div>
		</div>
		<a class="stretched-link" href="/{{ post.route }}"></a>
	</div>
</div>
