{"actions": [], "creation": "2013-03-11 17:48:16", "description": "Settings to control blog categories and interactions like comments and likes", "doctype": "DocType", "engine": "InnoDB", "field_order": ["blog_title", "blog_introduction", "preview_image", "column_break", "enable_social_sharing", "allow_guest_to_comment", "browse_by_category", "show_cta_in_blog", "cta_section", "title", "subtitle", "column_break_11", "cta_label", "cta_url", "section_break_12", "like_limit", "column_break_14", "comment_limit"], "fields": [{"fieldname": "blog_title", "fieldtype": "Data", "label": "Blog Title"}, {"fieldname": "blog_introduction", "fieldtype": "Small Text", "label": "Blog Introduction"}, {"default": "0", "fieldname": "enable_social_sharing", "fieldtype": "Check", "label": "Enable Social Sharing"}, {"collapsible": 1, "fieldname": "column_break", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "show_cta_in_blog", "fieldtype": "Check", "label": "Show \"Call to Action\" in Blog"}, {"depends_on": "eval:doc.show_cta_in_blog", "fieldname": "cta_section", "fieldtype": "Section Break", "label": "Call to Action"}, {"fieldname": "title", "fieldtype": "Data", "label": "Title", "mandatory_depends_on": "eval:doc.show_cta_in_blog"}, {"fieldname": "subtitle", "fieldtype": "Data", "label": "Subtitle", "mandatory_depends_on": "eval:doc.show_cta_in_blog"}, {"fieldname": "cta_label", "fieldtype": "Data", "label": "CTA Label", "mandatory_depends_on": "eval:doc.show_cta_in_blog"}, {"fieldname": "cta_url", "fieldtype": "Data", "label": "CTA URL", "mandatory_depends_on": "eval:doc.show_cta_in_blog"}, {"fieldname": "column_break_11", "fieldtype": "Column Break"}, {"fieldname": "section_break_12", "fieldtype": "Section Break", "label": "Rate Limits"}, {"default": "5", "description": "Comment limit per hour", "fieldname": "comment_limit", "fieldtype": "Int", "label": "Comment limit"}, {"fieldname": "column_break_14", "fieldtype": "Column Break"}, {"default": "1", "fieldname": "allow_guest_to_comment", "fieldtype": "Check", "label": "Allow Guest to comment"}, {"default": "0", "fieldname": "browse_by_category", "fieldtype": "Check", "label": "Browse by category"}, {"default": "5", "description": "Like limit per hour", "fieldname": "like_limit", "fieldtype": "Int", "label": "Like limit"}, {"fieldname": "preview_image", "fieldtype": "Attach Image", "label": "Preview Image"}], "icon": "fa fa-cog", "idx": 1, "issingle": 1, "links": [], "modified": "2024-01-30 14:13:12.477755", "modified_by": "Administrator", "module": "Website", "name": "Blog Settings", "owner": "Administrator", "permissions": [{"create": 1, "email": 1, "print": 1, "read": 1, "role": "Website Manager", "share": 1, "write": 1}, {"email": 1, "print": 1, "read": 1, "role": "Blogger", "share": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}