{"actions": [], "allow_import": 1, "autoname": "field:short_name", "creation": "2013-03-25 16:00:51", "description": "User ID of a Blogger", "doctype": "DocType", "document_type": "Setup", "engine": "InnoDB", "field_order": ["disabled", "short_name", "full_name", "user", "bio", "avatar"], "fields": [{"default": "0", "fieldname": "disabled", "fieldtype": "Check", "label": "Disabled"}, {"description": "Will be used in url (usually first name).", "fieldname": "short_name", "fieldtype": "Data", "label": "Short Name", "reqd": 1, "unique": 1}, {"fieldname": "full_name", "fieldtype": "Data", "in_list_view": 1, "label": "Full Name", "reqd": 1}, {"fieldname": "user", "fieldtype": "Link", "label": "User", "options": "User"}, {"fieldname": "bio", "fieldtype": "Small Text", "label": "Bio"}, {"fieldname": "avatar", "fieldtype": "Attach Image", "label": "Avatar"}], "icon": "fa fa-user", "idx": 1, "image_field": "avatar", "links": [{"link_doctype": "Blog Post", "link_fieldname": "blogger"}], "make_attachments_public": 1, "max_attachments": 1, "modified": "2022-10-18 15:44:31.473178", "modified_by": "Administrator", "module": "Website", "name": "Blogger", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "import": 1, "print": 1, "read": 1, "report": 1, "role": "Website Manager", "share": 1, "write": 1}, {"email": 1, "print": 1, "read": 1, "role": "Blogger", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "full_name", "track_changes": 1}