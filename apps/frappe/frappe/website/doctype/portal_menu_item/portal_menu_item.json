{"actions": [], "creation": "2016-03-30 01:39:20.586927", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["title", "enabled", "route", "reference_doctype", "role", "target"], "fields": [{"columns": 2, "fieldname": "title", "fieldtype": "Data", "in_list_view": 1, "label": "Title", "reqd": 1}, {"columns": 1, "default": "0", "fieldname": "enabled", "fieldtype": "Check", "in_list_view": 1, "label": "Enabled"}, {"columns": 3, "fieldname": "route", "fieldtype": "Data", "in_list_view": 1, "label": "Route", "reqd": 1}, {"columns": 2, "fieldname": "reference_doctype", "fieldtype": "Link", "in_list_view": 1, "label": "Reference Document Type", "options": "DocType"}, {"columns": 2, "fieldname": "role", "fieldtype": "Link", "in_list_view": 1, "label": "Role", "options": "Role"}, {"fieldname": "target", "fieldtype": "Data", "label": "Target", "read_only": 1}], "istable": 1, "links": [], "modified": "2022-08-03 12:20:49.792747", "modified_by": "Administrator", "module": "Website", "name": "Portal Menu Item", "owner": "Administrator", "permissions": [], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "states": []}