# -*- coding: utf-8 -*-
"""
تخصيصات الفاتورة النقدية
POS Invoice Customizations
"""

import frappe
from frappe import _
from frappe.utils import flt, cstr, nowdate, now_datetime

def before_save_pos(doc, method):
    """
    معالجة البيانات قبل حفظ الفاتورة النقدية
    Process data before saving POS Invoice
    """
    # التحقق من البيانات الأساسية للفواتير النقدية
    validate_pos_basic_data(doc)

    # تحديث معلومات المخزون للفواتير النقدية
    update_pos_stock_info(doc)

    # تطبيق قواعد العمل المخصصة للفواتير النقدية
    apply_pos_business_rules(doc)

    # تحديث معلومات الدفع
    update_pos_payment_info(doc)

def validate_pos_basic_data(doc):
    """
    التحقق من البيانات الأساسية للفواتير النقدية
    Validate basic data for POS invoices
    """
    # التحقق من تفعيل الوضع النقدي
    if not doc.is_pos:
        doc.is_pos = 1

    # التحقق من وجود العميل (يمكن أن يكون عميل افتراضي)
    if not doc.customer:
        default_customer = get_default_pos_customer(doc.company)
        if default_customer:
            doc.customer = default_customer
        else:
            frappe.throw(_("يجب تحديد العميل أو إعداد عميل افتراضي لنقطة البيع"))

    # التحقق من وجود بنود في الفاتورة
    if not doc.items:
        frappe.throw(_("يجب إضافة بنود للفاتورة النقدية"))

    # التحقق من الكميات والأسعار
    for item in doc.items:
        if flt(item.qty) <= 0:
            frappe.throw(_("الكمية يجب أن تكون أكبر من صفر للصنف: {0}").format(item.item_code))

        if flt(item.rate) < 0:
            frappe.throw(_("السعر لا يمكن أن يكون سالباً للصنف: {0}").format(item.item_code))

    # التحقق من معلومات الدفع
    validate_pos_payment_data(doc)

def validate_pos_payment_data(doc):
    """
    التحقق من بيانات الدفع للفواتير النقدية
    Validate payment data for POS invoices
    """
    if not doc.payments:
        frappe.throw(_("يجب إضافة طريقة دفع واحدة على الأقل"))

    total_payment = sum([flt(p.amount) for p in doc.payments])

    if flt(total_payment) < flt(doc.grand_total):
        frappe.msgprint(
            _("تحذير: المبلغ المدفوع ({0}) أقل من المجموع الإجمالي ({1})")
            .format(total_payment, doc.grand_total),
            alert=True
        )

    # حساب مبلغ الباقي
    if flt(total_payment) > flt(doc.grand_total):
        doc.change_amount = flt(total_payment) - flt(doc.grand_total)
        doc.paid_amount = flt(doc.grand_total)
    else:
        doc.paid_amount = flt(total_payment)
        doc.change_amount = 0

def update_pos_stock_info(doc):
    """
    تحديث معلومات المخزون للبنود في الفواتير النقدية
    Update stock information for items in POS invoices
    """
    for item in doc.items:
        if item.item_code:
            # الحصول على الكمية المتاحة في المخزون
            actual_qty = get_actual_qty(item.item_code, item.warehouse or doc.set_warehouse)

            # التحقق من توفر الكمية المطلوبة
            if doc.update_stock and flt(item.qty) > flt(actual_qty):
                frappe.msgprint(
                    _("تحذير: الكمية المطلوبة ({0}) أكبر من الكمية المتاحة ({1}) للصنف {2}")
                    .format(item.qty, actual_qty, item.item_code),
                    alert=True
                )

def apply_pos_business_rules(doc):
    """
    تطبيق قواعد العمل المخصصة للفواتير النقدية
    Apply custom business rules for POS invoices
    """
    # تطبيق خصم تلقائي للعملاء المميزين
    apply_pos_vip_discount(doc)

    # تحديد مركز التكلفة التلقائي
    set_pos_default_cost_center(doc)

    # تطبيق قواعد التسعير السريعة
    apply_pos_quick_pricing(doc)

    # تحديث الوقت للفواتير النقدية
    update_pos_timing(doc)

def apply_pos_vip_discount(doc):
    """
    تطبيق خصم للعملاء المميزين في الفواتير النقدية
    Apply discount for VIP customers in POS invoices
    """
    if doc.customer:
        customer_doc = frappe.get_doc("Customer", doc.customer)

        # التحقق من كون العميل مميز
        if hasattr(customer_doc, 'customer_group') and customer_doc.customer_group == "VIP":
            # تطبيق خصم 10% للعملاء المميزين في الفواتير النقدية
            if not doc.additional_discount_percentage:
                doc.additional_discount_percentage = 10
                frappe.msgprint(_("تم تطبيق خصم 10% للعميل المميز في الفاتورة النقدية"), alert=True)

def set_pos_default_cost_center(doc):
    """
    تحديد مركز التكلفة التلقائي للفواتير النقدية
    Set default cost center for POS invoices
    """
    if not doc.cost_center and doc.company:
        # الحصول على مركز التكلفة الافتراضي للشركة
        default_cost_center = frappe.db.get_value("Company", doc.company, "cost_center")
        if default_cost_center:
            doc.cost_center = default_cost_center

            # تطبيق مركز التكلفة على جميع البنود
            for item in doc.items:
                if not item.cost_center:
                    item.cost_center = default_cost_center

def apply_pos_quick_pricing(doc):
    """
    تطبيق قواعد التسعير السريعة للفواتير النقدية
    Apply quick pricing rules for POS invoices
    """
    for item in doc.items:
        # تطبيق خصم كمية للطلبات الكبيرة في الفواتير النقدية
        apply_pos_quantity_discount(item)

def apply_pos_quantity_discount(item):
    """
    تطبيق خصم الكمية للفواتير النقدية
    Apply quantity discount for POS invoices
    """
    if flt(item.qty) >= 50:
        # خصم 15% للكميات أكبر من أو تساوي 50 في الفواتير النقدية
        discount_rate = 15
    elif flt(item.qty) >= 20:
        # خصم 10% للكميات أكبر من أو تساوي 20
        discount_rate = 10
    elif flt(item.qty) >= 10:
        # خصم 5% للكميات أكبر من أو تساوي 10
        discount_rate = 5
    else:
        discount_rate = 0

    if discount_rate > 0 and not item.discount_percentage:
        item.discount_percentage = discount_rate

def update_pos_timing(doc):
    """
    تحديث الوقت للفواتير النقدية
    Update timing for POS invoices
    """
    if not doc.posting_time:
        doc.posting_time = now_datetime().time()

def update_pos_payment_info(doc):
    """
    تحديث معلومات الدفع للفواتير النقدية
    Update payment information for POS invoices
    """
    # التأكد من وجود حساب نقدي/بنكي
    if not doc.cash_bank_account and doc.pos_profile:
        try:
            pos_profile = frappe.get_doc("POS Profile", doc.pos_profile)
            if pos_profile.payments:
                # استخدام أول طريقة دفع كافتراضية
                first_payment = pos_profile.payments[0]
                # التحقق من وجود الحساب في طريقة الدفع
                if hasattr(first_payment, 'default_account'):
                    doc.cash_bank_account = first_payment.default_account
                elif hasattr(first_payment, 'account'):
                    doc.cash_bank_account = first_payment.account
        except Exception as e:
            frappe.log_error(f"Error updating POS payment info: {str(e)}")
            pass

def validate_pos_invoice(doc, method):
    """
    التحقق من صحة الفاتورة النقدية
    Validate POS invoice
    """
    # التحقق من صلاحيات الفواتير النقدية
    validate_pos_permissions(doc)

    # التحقق من إعدادات نقطة البيع
    validate_pos_profile_settings(doc)

def validate_pos_permissions(doc):
    """
    التحقق من صلاحيات الفواتير النقدية
    Validate POS invoice permissions
    """
    # يمكن إضافة منطق التحقق من الصلاحيات هنا
    pass

def validate_pos_profile_settings(doc):
    """
    التحقق من إعدادات ملف نقطة البيع
    Validate POS profile settings
    """
    if doc.pos_profile:
        pos_profile = frappe.get_doc("POS Profile", doc.pos_profile)

        # التحقق من تفعيل تحديث المخزون
        if pos_profile.update_stock:
            doc.update_stock = 1

        # التحقق من المخزن الافتراضي
        if pos_profile.warehouse and not doc.set_warehouse:
            doc.set_warehouse = pos_profile.warehouse

def on_submit_pos_invoice(doc, method):
    """
    معالجة ما بعد تأكيد الفاتورة النقدية
    Process after POS invoice submission
    """
    # إرسال إشعارات الفاتورة النقدية
    send_pos_notifications(doc)

    # تحديث إحصائيات العميل
    update_pos_customer_stats(doc)

    # تحديث إحصائيات نقطة البيع
    update_pos_statistics(doc)

def send_pos_notifications(doc):
    """
    إرسال إشعارات الفاتورة النقدية
    Send POS invoice notifications
    """
    # يمكن إضافة منطق الإشعارات هنا
    pass

def update_pos_customer_stats(doc):
    """
    تحديث إحصائيات العميل للفواتير النقدية
    Update customer statistics for POS invoices
    """
    # يمكن إضافة منطق تحديث الإحصائيات هنا
    pass

def update_pos_statistics(doc):
    """
    تحديث إحصائيات نقطة البيع
    Update POS statistics
    """
    # يمكن إضافة منطق تحديث إحصائيات نقطة البيع هنا
    pass

def get_default_pos_customer(company):
    """
    الحصول على العميل الافتراضي لنقطة البيع
    Get default POS customer
    """
    try:
        # البحث عن عميل افتراضي للشركة
        default_customer = frappe.db.get_value("Customer", {
            "customer_name": "عميل نقدي",
            "disabled": 0
        }, "name")

        if not default_customer:
            # إنشاء عميل افتراضي إذا لم يكن موجوداً
            default_customer = create_default_pos_customer(company)

        return default_customer
    except:
        return None

def create_default_pos_customer(company):
    """
    إنشاء عميل افتراضي لنقطة البيع
    Create default POS customer
    """
    try:
        customer_doc = frappe.get_doc({
            "doctype": "Customer",
            "customer_name": "عميل نقدي",
            "customer_type": "Individual",
            "customer_group": "Individual",
            "territory": "All Territories"
        })
        customer_doc.insert(ignore_permissions=True)
        return customer_doc.name
    except:
        return None

def get_actual_qty(item_code, warehouse=None):
    """
    الحصول على الكمية الفعلية في المخزون
    Get actual quantity in stock
    """
    try:
        from erpnext.stock.utils import get_latest_stock_qty
        return get_latest_stock_qty(item_code, warehouse)
    except:
        return 0

@frappe.whitelist()
def get_pos_item_info(item_code, warehouse=None):
    """
    API للحصول على معلومات الصنف للفواتير النقدية
    API to get item information for POS invoices
    """
    if not item_code:
        return {}

    try:
        actual_qty = get_actual_qty(item_code, warehouse)

        # الحصول على معلومات إضافية عن الصنف
        item_doc = frappe.get_doc("Item", item_code)

        # الحصول على آخر سعر بيع
        last_rate = frappe.db.get_value("POS Invoice Item", {
            "item_code": item_code,
            "parent": ["!=", ""]
        }, "rate", order_by="creation desc")

        return {
            "actual_qty": actual_qty,
            "last_rate": last_rate or item_doc.standard_rate or 0,
            "stock_uom": item_doc.stock_uom,
            "item_name": item_doc.item_name,
            "item_group": item_doc.item_group,
            "has_batch_no": item_doc.has_batch_no,
            "has_serial_no": item_doc.has_serial_no,
            "is_stock_item": item_doc.is_stock_item
        }
    except Exception as e:
        frappe.log_error(f"Error getting POS item info for {item_code}: {str(e)}")
        return {}

@frappe.whitelist()
def get_pos_daily_summary():
    """
    الحصول على ملخص يومي لنقطة البيع
    Get daily POS summary
    """
    try:
        today = nowdate()

        # إجمالي المبيعات اليوم
        total_sales = frappe.db.sql("""
            SELECT SUM(grand_total) as total
            FROM `tabPOS Invoice`
            WHERE posting_date = %s AND docstatus = 1
        """, today)[0][0] or 0

        # عدد الفواتير اليوم
        invoice_count = frappe.db.count("POS Invoice", {
            "posting_date": today,
            "docstatus": 1
        })

        # إجمالي الكمية المباعة
        total_qty = frappe.db.sql("""
            SELECT SUM(qty) as total_qty
            FROM `tabPOS Invoice Item` pi
            INNER JOIN `tabPOS Invoice` p ON pi.parent = p.name
            WHERE p.posting_date = %s AND p.docstatus = 1
        """, today)[0][0] or 0

        # متوسط قيمة الفاتورة
        avg_invoice_value = total_sales / invoice_count if invoice_count > 0 else 0

        return {
            "total_sales": total_sales,
            "invoice_count": invoice_count,
            "total_qty": total_qty,
            "avg_invoice_value": avg_invoice_value,
            "date": today
        }
    except Exception as e:
        frappe.log_error(f"Error getting POS daily summary: {str(e)}")
        return {}

@frappe.whitelist()
def get_pos_payment_methods():
    """
    الحصول على طرق الدفع المتاحة لنقطة البيع
    Get available payment methods for POS
    """
    try:
        payment_methods = frappe.get_all("Mode of Payment",
            filters={"enabled": 1},
            fields=["name", "type"]
        )

        return payment_methods
    except Exception as e:
        frappe.log_error(f"Error getting POS payment methods: {str(e)}")
        return []

@frappe.whitelist()
def get_entries_preview(docname, doctype="POS Invoice", is_submitted=False):
    """
    الحصول على معاينة القيود المحاسبية والمخزنية
    Get preview of accounting and stock entries
    """
    try:
        # التحقق من صحة المدخلات
        if not docname or not doctype:
            return None

        # تحويل is_submitted إلى boolean إذا كان string
        if isinstance(is_submitted, str):
            is_submitted = is_submitted.lower() in ['true', '1', 'yes']

        # الحصول على الفاتورة
        doc = frappe.get_doc(doctype, docname)

        # إعداد البيانات الأساسية
        preview_data = {
            'invoice_number': doc.name,
            'posting_date': str(doc.posting_date) if doc.posting_date else '',
            'customer_name': doc.customer_name or doc.customer or '',
            # 'customer_name': doc.customer_name if hasattr(doc, "customer_name") else doc.customer
            'grand_total': doc.grand_total or 0,
            'status': get_status_text(doc.docstatus),
            'status_class': get_status_class(doc.docstatus),
            'accounting_entries': [],
            'stock_entries': []
        }

        if is_submitted and doc.docstatus == 1:
            # الحصول على القيود الفعلية للفاتورة المعتمدة
            preview_data['accounting_entries'] = get_actual_accounting_entries(docname)
            preview_data['stock_entries'] = get_actual_stock_entries(docname)
        else:
            # إنشاء معاينة للقيود قبل الاعتماد
            preview_data['accounting_entries'] = generate_preview_accounting_entries(doc)
            preview_data['stock_entries'] = generate_preview_stock_entries(doc)

        return preview_data

    except Exception as e:
        frappe.log_error(f"Error in get_entries_preview: {str(e)}")
        return None

def get_status_text(docstatus):
    """الحصول على نص الحالة"""
    status_map = {
        0: "مسودة",
        1: "معتمد",
        2: "ملغي"
    }
    return status_map.get(docstatus, "غير معروف")

def get_status_class(docstatus):
    """الحصول على فئة CSS للحالة"""
    class_map = {
        0: "status-draft",
        1: "status-submitted",
        2: "status-cancelled"
    }
    return class_map.get(docstatus, "status-draft")

def get_actual_accounting_entries(docname):
    """الحصول على القيود المحاسبية الفعلية"""
    try:
        entries = frappe.db.sql("""
            SELECT
                account,
                debit,
                credit,
                remarks,
                against
            FROM `tabGL Entry`
            WHERE voucher_no = %s
            ORDER BY account
        """, docname, as_dict=True)

        return entries
    except Exception as e:
        frappe.log_error(f"Error getting actual accounting entries: {str(e)}")
        return []

def get_actual_stock_entries(docname):
    """الحصول على القيود المخزنية الفعلية"""
    try:
        entries = frappe.db.sql("""
            SELECT
                item_code,
                warehouse,
                actual_qty,
                valuation_rate,
                stock_value_difference
            FROM `tabStock Ledger Entry`
            WHERE voucher_no = %s
            ORDER BY item_code, warehouse
        """, docname, as_dict=True)

        return entries
    except Exception as e:
        frappe.log_error(f"Error getting actual stock entries: {str(e)}")
        return []

def generate_preview_accounting_entries(doc):
    """إنشاء معاينة القيود المحاسبية قبل الاعتماد"""
    try:
        entries = []

        # التحقق من وجود البيانات الأساسية
        if not doc.grand_total or doc.grand_total <= 0:
            return []

        # حساب المدينون (العميل)
        customer_account = get_customer_account(doc.customer, doc.company)

        entries.append({
            'account': customer_account,
            'debit': doc.grand_total,
            'credit': 0,
            'remarks': f'فاتورة مبيعات نقدية - {doc.customer_name or doc.customer}',
            'against': 'حسابات المبيعات'
        })

        # حساب المبيعات
        if doc.items and len(doc.items) > 0:
            sales_accounts = {}
            for item in doc.items:
                income_account = getattr(item, 'income_account', None) or get_default_income_account(doc.company)
                amount = getattr(item, 'amount', 0) or 0

                if income_account not in sales_accounts:
                    sales_accounts[income_account] = 0
                sales_accounts[income_account] += amount

            for account, amount in sales_accounts.items():
                if amount > 0:
                    entries.append({
                        'account': account,
                        'debit': 0,
                        'credit': amount,
                        'remarks': f'مبيعات - {doc.name}',
                        'against': doc.customer or 'العميل'
                    })
        else:
            # إذا لم توجد بنود، استخدم الحساب الافتراضي
            income_account = get_default_income_account(doc.company)
            entries.append({
                'account': income_account,
                'debit': 0,
                'credit': doc.grand_total,
                'remarks': f'مبيعات - {doc.name}',
                'against': doc.customer or 'العميل'
            })

        # حساب الضرائب
        if hasattr(doc, 'taxes') and doc.taxes:
            for tax in doc.taxes:
                tax_amount = getattr(tax, 'tax_amount', 0) or 0
                if tax_amount > 0:
                    entries.append({
                        'account': getattr(tax, 'account_head', 'حساب ضريبة'),
                        'debit': 0,
                        'credit': tax_amount,
                        'remarks': f'ضريبة - {getattr(tax, "description", "ضريبة")}',
                        'against': doc.customer or 'العميل'
                    })

        # حساب تكلفة البضاعة المباعة (إذا كان تحديث المخزون مفعل)
        if getattr(doc, 'update_stock', False) and doc.items:
            cogs_entries = generate_cogs_entries(doc)
            entries.extend(cogs_entries)

        return entries

    except Exception as e:
        frappe.log_error(f"Error generating preview accounting entries: {str(e)}")
        return []

def generate_preview_stock_entries(doc):
    """إنشاء معاينة القيود المخزنية قبل الاعتماد"""
    try:
        entries = []

        # التحقق من تفعيل تحديث المخزون
        update_stock = getattr(doc, 'update_stock', False)

        if update_stock and doc.items:
            for item in doc.items:
                item_code = getattr(item, 'item_code', None)
                qty = getattr(item, 'qty', 0)
                warehouse = getattr(item, 'warehouse', None) or getattr(doc, 'set_warehouse', None)

                if item_code and qty and warehouse:
                    # الحصول على معدل التقييم
                    valuation_rate = get_item_valuation_rate(item_code, warehouse)

                    entries.append({
                        'item_code': item_code,
                        'warehouse': warehouse,
                        'actual_qty': -qty,  # سالب لأنها مبيعات
                        'valuation_rate': valuation_rate,
                        'stock_value_difference': -qty * valuation_rate
                    })

        return entries

    except Exception as e:
        frappe.log_error(f"Error generating preview stock entries: {str(e)}")
        return []

def get_customer_account(customer, company):
    """الحصول على حساب العميل"""
    try:
        customer_account = frappe.db.get_value("Customer", customer, "customer_primary_account")
        if not customer_account:
            # الحصول على حساب المدينون الافتراضي
            customer_account = frappe.db.get_value("Company", company, "default_receivable_account")
        return customer_account or "1310 - مدينون - نس"
    except:
        return "1310 - مدينون - نس"

def get_default_income_account(company):
    """الحصول على حساب الإيرادات الافتراضي"""
    try:
        income_account = frappe.db.get_value("Company", company, "default_income_account")
        return income_account or "4110 - مبيعات - نس"
    except:
        return "4110 - مبيعات - نس"

def get_item_valuation_rate(item_code, warehouse):
    """الحصول على معدل تقييم الصنف"""
    try:
        from erpnext.stock.utils import get_valuation_rate
        return get_valuation_rate(item_code, warehouse) or 0
    except:
        return 0

def generate_cogs_entries(doc):
    """إنشاء قيود تكلفة البضاعة المباعة"""
    try:
        entries = []
        total_cogs = 0

        for item in doc.items:
            if item.item_code and item.qty:
                valuation_rate = get_item_valuation_rate(item.item_code, item.warehouse)
                cogs_amount = item.qty * valuation_rate
                total_cogs += cogs_amount

        if total_cogs > 0:
            # حساب تكلفة البضاعة المباعة (مدين)
            cogs_account = get_default_cogs_account(doc.company)
            entries.append({
                'account': cogs_account,
                'debit': total_cogs,
                'credit': 0,
                'remarks': f'تكلفة البضاعة المباعة - {doc.name}',
                'against': 'المخزون'
            })

            # حساب المخزون (دائن)
            stock_account = get_default_stock_account(doc.company)
            entries.append({
                'account': stock_account,
                'debit': 0,
                'credit': total_cogs,
                'remarks': f'تخفيض المخزون - {doc.name}',
                'against': cogs_account
            })

        return entries

    except Exception as e:
        frappe.log_error(f"Error generating COGS entries: {str(e)}")
        return []

def get_default_cogs_account(company):
    """الحصول على حساب تكلفة البضاعة المباعة الافتراضي"""
    try:
        cogs_account = frappe.db.get_value("Company", company, "default_expense_account")
        return cogs_account or "5111 - تكلفة البضاعة المباعة - نس"
    except:
        return "5111 - تكلفة البضاعة المباعة - نس"

def get_default_stock_account(company):
    """الحصول على حساب المخزون الافتراضي"""
    try:
        stock_account = frappe.db.get_value("Company", company, "stock_adjustment_account")
        return stock_account or "1320 - مخزون - نس"
    except:
        return "1320 - مخزون - نس"


