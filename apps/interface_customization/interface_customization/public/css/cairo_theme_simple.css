/* ثيم Cairo المبسط - Simple Cairo Theme */

/* تحميل الخط مع تحسين الأداء */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');

/* تحسين تحميل الأيقونات */
/* Optimize icon loading */
.icon-preload {
    display: none;
}

/* استخدام فوري للموارد المحملة مسبقاً لتجنب تحذيرات الـ preload */
body::before {
    content: "";
    background-image: url('/assets/frappe/icons/timeless/icons.svg');
    position: absolute;
    width: 0;
    height: 0;
    opacity: 0;
    pointer-events: none;
}

/* استخدام فوري لملفات ERPNext */
body::after {
    content: "";
    background: url('/website_script.js') no-repeat;
    position: absolute;
    width: 0;
    height: 0;
    opacity: 0;
    pointer-events: none;
}

/* تحسين تحميل الخطوط */
@font-face {
    font-family: 'Cairo-Preload';
    src: url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');
    font-display: swap;
}

/* فئة لتحسين الـ preload */
.preload-optimizer {
    display: none !important;
    visibility: hidden !important;
    position: absolute !important;
    top: -9999px !important;
    left: -9999px !important;
}

/* الألوان الأساسية */
:root {
    --blue-50: #eff6ff;
    --blue-100: #dbeafe;
    --blue-200: #bfdbfe;
    --blue-300: #93c5fd;
    --blue-400: #60a5fa;
    --blue-500: #3b82f6;
    --blue-600: #2563eb;
    --blue-700: #1d4ed8;
    --blue-800: #1e40af;
    --blue-900: #1e3a8a;
    --violet-100: #ede9fe;
    --bg-purple: #f3e8ff;
}

/* تطبيق خط Cairo */
body,
.sidebar-item-label,
.h4,
.widget-title,
.page-title,
.ellipsis,
.control-label,
button,
h1, h2, h3, h4, h5, h6,
.section-head,
.form-control,
.btn,
.nav-link {
    font-family: "Cairo", sans-serif !important;
    font-weight: 700 !important;
}

/* الشريط العلوي */
.navbar {
    background: var(--blue-400) !important;
    height: 45px !important;
}

.navbar-home img {
    max-height: 40px !important;
}

/* الأزرار - الكود الأصلي */
/*
button.btn-primary,
.btn-login,
.btn-primary {
    background: var(--blue-700) !important;
    border-color: var(--blue-700) !important;
}

.btn-primary:hover {
    background: var(--blue-800) !important;
    border-color: var(--blue-800) !important;
}
*/

/* الأزرار المحسنة - الكود الجديد */
/* ترتيب الأزرار في صف واحد */
.form-footer,
.form-actions,
.page-actions,
.btn-group,
.form-page .page-head .standard-actions,
.page-head .standard-actions {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 8px !important;
    align-items: center !important;
    justify-content: flex-start !important;
    margin: 10px 0 !important;
}

/* تنسيق الأزرار الفردية */
button.btn-primary,
.btn-login,
.btn-primary {
    background: linear-gradient(135deg, var(--blue-700), var(--blue-800)) !important;
    border: none !important;
    border-radius: 6px !important;
    padding: 8px 16px !important;
    font-weight: 600 !important;
    font-size: 13px !important;
    transition: all 0.3s ease !important;
    margin: 2px !important;
    min-width: 100px !important;
    height: 36px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: white !important;
    box-shadow: 0 2px 6px rgba(29, 78, 216, 0.3) !important;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--blue-800), var(--blue-900)) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 3px 8px rgba(29, 78, 216, 0.4) !important;
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
    border: none !important;
    border-radius: 6px !important;
    padding: 8px 16px !important;
    font-weight: 600 !important;
    font-size: 13px !important;
    margin: 2px !important;
    min-width: 100px !important;
    height: 36px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: white !important;
}

.btn-secondary,
.btn-default {
    background: linear-gradient(135deg, #6c757d, #5a6268) !important;
    border: none !important;
    border-radius: 6px !important;
    padding: 8px 16px !important;
    font-weight: 600 !important;
    font-size: 13px !important;
    color: white !important;
    margin: 2px !important;
    min-width: 100px !important;
    height: 36px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107, #e0a800) !important;
    border: none !important;
    border-radius: 6px !important;
    padding: 8px 16px !important;
    font-weight: 600 !important;
    font-size: 13px !important;
    color: #212529 !important;
    margin: 2px !important;
    min-width: 100px !important;
    height: 36px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8, #138496) !important;
    border: none !important;
    border-radius: 6px !important;
    padding: 8px 16px !important;
    font-weight: 600 !important;
    font-size: 13px !important;
    color: white !important;
    margin: 2px !important;
    min-width: 100px !important;
    height: 36px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* تحسين شريط الأدوات العلوي */
.page-head .standard-actions {
    background: rgba(255, 255, 255, 0.95) !important;
    border-radius: 8px !important;
    padding: 8px 12px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    margin: 5px 0 !important;
}

/* شريط التنقل */
#navbar-breadcrumbs a {
    color: var(--blue-900) !important;
    font-family: Cairo !important;
    font-size: small !important;
}

/* النصوص */
span.h4 {
    color: #333 !important;
}

span.ellipsis,
span.sidebar-item-label {
    color: #5f5f5f !important;
}

h3.title-text {
    color: var(--blue-900) !important;
    font-size: small !important;
}

.control-label,
span.ellipsis,
span.sidebar-item-label,
button.btn-primary span,
.list-row-head,
span.dropdown-text,
span.button-label,
.dropdown-item {
    font-size: smaller !important;
}

/* حقول الإدخال */
.control-input,
.form-control {
    border: 2px solid var(--blue-300) !important;
    border-radius: 2px !important;
    font-family: "Cairo", sans-serif !important;
}

.control-input:focus,
.form-control:focus {
    border-color: var(--blue-700) !important;
    box-shadow: 0 0 0 0.2rem rgba(29, 78, 216, 0.25) !important;
}

/* صفحة تسجيل الدخول */
#page-login {
    background: var(--violet-100) !important;
}

/* رؤوس الجداول */
.list-row-head {
    background: var(--blue-300) !important;
    text-align: center !important;
    line-height: 30px !important;
    font-size: smaller !important;
    padding: 0 !important;
}

/* الجداول */
.table thead th {
    background: var(--blue-300) !important;
    color: var(--blue-900) !important;
    font-family: "Cairo", sans-serif !important;
    font-weight: 700 !important;
}

.table tbody tr:hover {
    background: var(--blue-50) !important;
}

/* محتوى الصفحة */
.page-content {
    padding: 5px 0 !important;
}

/* صفحة مساحات العمل */
#page-Workspaces {
    background: var(--blue-700) !important;
}

/* رأس الصفحة */
.page-head {
    margin-bottom: 0 !important;
    height: 40px !important;
    top: 45px !important;
}

.page-head .page-head-content {
    height: 40px !important;
    padding: 6px 0 !important;
}

/* القسم الرئيسي */
.layout-main-section-wrapper {
    padding: 0 5px !important;
    margin-left: 15px !important;
}

/* الروابط والاختصارات */
.link-text,
.shortcut-widget-box {
    border: 2px solid var(--blue-300) !important;
    background: var(--blue-50) !important;
    padding: 5px 7px !important;
    min-width: 60% !important;
    display: inline-block !important;
    color: var(--blue-800) !important;
    border-radius: 5px !important;
}

.shortcut-widget-box {
    background: var(--bg-purple) !important;
}

.link-text:hover {
    border: 2px solid var(--blue-400) !important;
}

/* إصلاح النصوص بدون إطارات - تطبيق الإطارات على جميع الروابط */
/* .widget.links-widget-box .link-item,
.widget.links-widget-box .link-item a,
.widget-group .widget-group-body .link-item,
.widget-group .widget-group-body .link-item a,
.standard-sidebar-section .sidebar-item,
.standard-sidebar-section .sidebar-item a,
.workspace-sidebar-item,
.workspace-sidebar-item a,
.shortcut-item,
.shortcut-item a {
    border: 2px solid var(--blue-300) !important;
    background: var(--blue-50) !important;
    padding: 5px 7px !important;
    margin: 2px 0 !important;
    display: inline-block !important;
    color: var(--blue-800) !important;
    border-radius: 5px !important;
    text-decoration: none !important;
    font-family: "Cairo", sans-serif !important;
    font-weight: 600 !important;
    min-width: 60% !important;
    text-align: center !important;
} */

/* .widget.links-widget-box .link-item:hover,
.widget.links-widget-box .link-item a:hover,
.widget-group .widget-group-body .link-item:hover,
.widget-group .widget-group-body .link-item a:hover,
.standard-sidebar-section .sidebar-item:hover,
.standard-sidebar-section .sidebar-item a:hover,
.workspace-sidebar-item:hover,
.workspace-sidebar-item a:hover,
.shortcut-item:hover,
.shortcut-item a:hover {
    border: 2px solid var(--blue-400) !important;
    background: var(--blue-100) !important;
    color: var(--blue-900) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
} */

/* الشريط الجانبي */
.desk-sidebar-item {
    margin: 5px auto !important;
    background: var(--blue-50) !important;
    border: 2px solid var(--blue-300) !important;
}

.desk-sidebar-item.selected {
    border: 2px solid var(--blue-700) !important;
}

.desk-sidebar-item.selected .item-anchor {
    background: var(--blue-300) !important;
}

.item-anchor:hover {
    background: var(--blue-200) !important;
}

/* القسم الجانبي */
.layout-side-section {
    padding-left: 5px !important;
    padding-right: 5px !important;
}

.list-sidebar {
    padding: 10px !important;
    border-radius: 5px !important;
    background: white !important;
}

/* البطاقات */
.card {
    border: 2px solid var(--blue-300) !important;
    border-radius: 5px !important;
}

.card-header {
    background: var(--blue-300) !important;
    color: var(--blue-900) !important;
    font-family: "Cairo", sans-serif !important;
    font-weight: 700 !important;
}

/* القوائم المنسدلة */
.dropdown-menu {
    border: 2px solid var(--blue-300) !important;
    border-radius: 5px !important;
}

.dropdown-item {
    font-family: "Cairo", sans-serif !important;
    font-size: smaller !important;
}

.dropdown-item:hover {
    background: var(--blue-100) !important;
    color: var(--blue-900) !important;
}

/* النوافذ المنبثقة - الكود الأصلي */
/*
.modal-content {
    border: 2px solid var(--blue-300) !important;
    border-radius: 15px !important;
}

.modal-header {
    background: var(--blue-400) !important;
    color: white !important;
    font-family: "Cairo", sans-serif !important;
    font-weight: 700 !important;
}
*/

/* النوافذ المنبثقة المحسنة - الكود الجديد */
/* تقليل حجم النوافذ المنبثقة */
.modal-dialog {
    max-width: 600px !important;
    width: 90% !important;
    margin: 30px auto !important;
}

.modal-dialog.modal-lg {
    max-width: 800px !important;
    width: 95% !important;
}

.modal-dialog.modal-xl {
    max-width: 1000px !important;
    width: 98% !important;
}

.modal-dialog.modal-sm {
    max-width: 400px !important;
    width: 80% !important;
}

/* تحسين النوافذ المنبثقة للمعاينة */
.modal-dialog.print-preview-modal {
    max-width: 700px !important;
    width: 90% !important;
    margin: 20px auto !important;
}

/* تحسين نافذة المعاينة */
.print-preview-wrapper {
    max-height: 70vh !important;
    overflow-y: auto !important;
    padding: 15px !important;
}

/* تحسين نوافذ الرسائل */
.msgprint {
    border-radius: 8px !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
    max-width: 500px !important;
    margin: 20px auto !important;
    padding: 20px !important;
}

/* تحسين نوافذ التأكيد */
.modal-content {
    border-radius: 12px !important;
    border: none !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2) !important;
}

.modal-header {
    background: linear-gradient(135deg, var(--blue-600), var(--blue-700)) !important;
    color: white !important;
    border-bottom: none !important;
    border-radius: 12px 12px 0 0 !important;
    padding: 15px 20px !important;
    font-family: "Cairo", sans-serif !important;
    font-weight: 700 !important;
}

.modal-header .modal-title {
    font-weight: 700 !important;
    font-size: 16px !important;
}

.modal-header .close {
    color: white !important;
    opacity: 0.8 !important;
    font-size: 24px !important;
}

.modal-header .close:hover {
    opacity: 1 !important;
}

.modal-body {
    padding: 20px !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
    font-family: "Cairo", sans-serif !important;
}

.modal-footer {
    border-top: 1px solid #e9ecef !important;
    padding: 15px 20px !important;
    display: flex !important;
    justify-content: flex-end !important;
    gap: 10px !important;
}

.modal-footer .btn {
    min-width: 80px !important;
    height: 36px !important;
    font-size: 13px !important;
    font-weight: 600 !important;
}

/* التنبيهات */
.alert {
    border-radius: 5px !important;
    font-family: "Cairo", sans-serif !important;
    font-weight: 600 !important;
}

.alert-info {
    background: var(--blue-50) !important;
    border: 2px solid var(--blue-500) !important;
    color: var(--blue-900) !important;
}

/* الروابط */
a {
    color: var(--blue-600) !important;
    font-family: "Cairo", sans-serif !important;
}

a:hover {
    color: var(--blue-800) !important;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .navbar {
        height: 40px !important;
        padding: 0.5rem !important;
    }

    .page-head {
        height: 35px !important;
        top: 40px !important;
    }

    .layout-main-section-wrapper {
        margin-left: 5px !important;
        padding: 0 2px !important;
    }

    .link-text,
    .shortcut-widget-box {
        min-width: 90% !important;
        padding: 3px 5px !important;
    }

    /* تحسين الأزرار للشاشات الصغيرة */
    .form-footer,
    .form-actions,
    .page-actions,
    .btn-group,
    .form-page .page-head .standard-actions,
    .page-head .standard-actions {
        flex-direction: row !important;
        flex-wrap: wrap !important;
        gap: 5px !important;
        padding: 5px !important;
    }

    button.btn-primary,
    .btn-login,
    .btn-primary,
    .btn-success,
    .btn-secondary,
    .btn-default,
    .btn-warning,
    .btn-info {
        min-width: 80px !important;
        height: 32px !important;
        font-size: 12px !important;
        padding: 6px 12px !important;
        margin: 1px !important;
    }

    /* تحسين شريط الأدوات للشاشات الصغيرة */
    .page-head .standard-actions {
        padding: 5px 8px !important;
        margin: 3px 0 !important;
    }

    /* تحسين النوافذ المنبثقة للشاشات الصغيرة */
    .modal-dialog {
        max-width: 95% !important;
        width: 95% !important;
        margin: 10px auto !important;
    }

    .modal-dialog.modal-lg,
    .modal-dialog.modal-xl {
        max-width: 98% !important;
        width: 98% !important;
    }

    .msgprint {
        max-width: 90% !important;
        margin: 10px auto !important;
        padding: 15px !important;
    }

    .print-preview-wrapper {
        max-height: 50vh !important;
        padding: 10px !important;
    }

    /* تحسين القوائم المنسدلة للشاشات الصغيرة */
    .dropdown-menu {
        min-width: 150px !important;
        max-width: 250px !important;
    }

    .dropdown-menu .btn {
        font-size: 12px !important;
        padding: 6px 10px !important;
        height: 30px !important;
    }
}

/* إصلاح شامل لجميع الروابط والعناصر التفاعلية */
/* a[href]:not(.btn):not(.navbar-brand),
.list-item-title a,
.sidebar-item-container a,
.workspace-item a,
.module-item a,
.report-item a,
.master-item a,
span[data-route],
div[data-route],
.link-item span,
.shortcut-widget span,
.widget-title + div a,
.widget-body a,
.sidebar-section a,
.workspace-sidebar a,
.module-link,
.report-link,
.master-link {
    border: 2px solid var(--blue-300) !important;
    background: var(--blue-50) !important;
    padding: 4px 8px !important;
    margin: 1px !important;
    display: inline-block !important;
    color: var(--blue-800) !important;
    border-radius: 5px !important;
    text-decoration: none !important;
    font-family: "Cairo", sans-serif !important;
    font-weight: 600 !important;
    min-width: 50% !important;
    text-align: center !important;
    transition: all 0.2s ease !important;
} */

/* تأثيرات الحركة عند التمرير */
a[href]:not(.btn):not(.navbar-brand):hover,
.list-item-title a:hover,
.sidebar-item-container a:hover,
.workspace-item a:hover,
.module-item a:hover,
.report-item a:hover,
.master-item a:hover,
span[data-route]:hover,
div[data-route]:hover,
.link-item span:hover,
.shortcut-widget span:hover,
.widget-title + div a:hover,
.widget-body a:hover,
.sidebar-section a:hover,
.workspace-sidebar a:hover,
.module-link:hover,
.report-link:hover,
.master-link:hover {
    border: 2px solid var(--blue-500) !important;
    background: var(--blue-100) !important;
    color: var(--blue-900) !important;
    transform: translateY(-1px) scale(1.02) !important;
    box-shadow: 0 3px 8px rgba(29, 78, 216, 0.2) !important;
}

/* إصلاح خاص للعناصر في الشريط الجانبي */
.standard-sidebar .sidebar-item,
.standard-sidebar .sidebar-item-container,
.workspace-sidebar .sidebar-item,
.workspace-sidebar .sidebar-item-container {
    border: 2px solid var(--blue-300) !important;
    background: var(--blue-50) !important;
    margin: 3px 0 !important;
    border-radius: 5px !important;
}

/* إصلاح للنصوص العادية التي تحتاج إطارات */
.widget-group-body > div,
.links-widget-box > div,
.shortcut-widget-box > div {
    border: 2px solid var(--blue-300) !important;
    background: var(--blue-50) !important;
    padding: 5px 7px !important;
    margin: 2px 0 !important;
    border-radius: 5px !important;
    color: var(--blue-800) !important;
    font-family: "Cairo", sans-serif !important;
    font-weight: 600 !important;
    text-align: center !important;
}

/* رسالة تأكيد */
body::after {
    content: "✅ ثيم Cairo مطبق!" !important;
    position: fixed !important;
    bottom: 20px !important;
    right: 20px !important;
    background: var(--blue-600) !important;
    color: white !important;
    padding: 10px 15px !important;
    border-radius: 5px !important;
    font-family: "Cairo", sans-serif !important;
    font-size: 12px !important;
    z-index: 9999 !important;
    opacity: 0 !important;
    animation: showThemeMessage 4s ease-in-out !important;
}

@keyframes showThemeMessage {
    0%, 90% { opacity: 0; }
    10%, 80% { opacity: 1; }
    100% { opacity: 0; }
}
