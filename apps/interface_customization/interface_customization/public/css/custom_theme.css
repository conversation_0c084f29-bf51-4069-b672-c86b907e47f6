/*
 * ثيم <PERSON> العربي - تطبيق مباشر - الكود الأصلي
 * Cairo Arabic Theme - Direct Application - Original Code
 */
/*
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');

:root {
    --blue-50: #eff6ff;
    --blue-100: #dbeafe;
    --blue-200: #bfdbfe;
    --blue-300: #93c5fd;
    --blue-400: #60a5fa;
    --blue-500: #3b82f6;
    --blue-600: #2563eb;
    --blue-700: #1d4ed8;
    --blue-800: #1e40af;
    --blue-900: #1e3a8a;

    --violet-100: #ede9fe;
    --violet-200: #ddd6fe;
    --violet-300: #c4b5fd;
    --violet-400: #a78bfa;
    --violet-500: #8b5cf6;

    --bg-purple: #f3e8ff;
    --bg-color: #ffffff;
    --text-muted: #6b7280;
    --text-color: #1f2937;

    --shadow-light: 0 2px 8px rgba(59, 130, 246, 0.15);
    --shadow-medium: 0 4px 16px rgba(59, 130, 246, 0.2);
    --shadow-heavy: 0 8px 32px rgba(59, 130, 246, 0.25);

    --border-radius: 5px;
    --border-radius-small: 2px;
    --border-radius-large: 15px;
}
*/

/*
 * ثيم Cairo العربي - تطبيق مباشر - الكود الجديد المحسن
 * Cairo Arabic Theme - Direct Application - Enhanced New Code
 */

/* تحميل خط Cairo من Google Fonts مع تحسين الأداء */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');

/* ===== متغيرات الألوان الأساسية المحسنة ===== */
:root {
    /* الألوان الزرقاء الجذابة */
    --blue-50: #eff6ff;
    --blue-100: #dbeafe;
    --blue-200: #bfdbfe;
    --blue-300: #93c5fd;
    --blue-400: #60a5fa;
    --blue-500: #3b82f6;
    --blue-600: #2563eb;
    --blue-700: #1d4ed8;
    --blue-800: #1e40af;
    --blue-900: #1e3a8a;

    /* الألوان البنفسجية */
    --violet-100: #ede9fe;
    --violet-200: #ddd6fe;
    --violet-300: #c4b5fd;
    --violet-400: #a78bfa;
    --violet-500: #8b5cf6;

    /* ألوان إضافية */
    --bg-purple: #f3e8ff;
    --bg-color: #ffffff;
    --text-muted: #6b7280;
    --text-color: #1f2937;

    /* الظلال المحسنة */
    --shadow-light: 0 2px 8px rgba(59, 130, 246, 0.15);
    --shadow-medium: 0 4px 16px rgba(59, 130, 246, 0.2);
    --shadow-heavy: 0 8px 32px rgba(59, 130, 246, 0.25);
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.15);

    /* الحدود المحسنة */
    --border-radius: 6px;
    --border-radius-small: 3px;
    --border-radius-large: 12px;
    --border-light: #e5e7eb;
}

/* ===== تطبيق خط Cairo على العناصر المحددة ===== */
.sidebar-item-label,
.h4,
.widget-title,
.page-title,
.ellipsis,
.control-label,
button,
h3, h4, h2,
.section-head {
    font-family: "Cairo", sans-serif !important;
    font-optical-sizing: auto !important;
    font-weight: 700 !important;
    font-style: normal !important;
}

/* ===== الأزرار الأساسية ===== */
button.btn-primary, .btn-login {
    background: var(--blue-700) !important;
}

/* ===== شريط التنقل ===== */
#navbar-breadcrumbs a {
    color: var(--blue-900) !important;
    font-family: Cairo !important;
    font-size: small !important;
}

/* ===== تنسيق النصوص ===== */
span.h4 {
    color: #333 !important;
}

span.ellipsis, span.sidebar-item-label {
    color: #5f5f5f !important;
}

h3.title-text {
    color: var(--blue-900) !important;
    font-size: small !important;
}

.control-label, span.ellipsis, span.sidebar-item-label, button.btn-primary span,
.list-row-head, span.dropdown-text, span.button-label, .dropdown-item {
    font-size: smaller !important;
}

/* ===== حقول الإدخال ===== */
.control-input {
    border: #eaf2ff 2px solid !important;
    border-radius: 2px !important;
    background: var(--blue-900) !important;
}

.title-area .indicator-pill {
    font-size: x-small !important;
}

/* ===== الشريط العلوي ===== */
.navbar {
    background: var(--blue-400) !important;
    height: 45px !important;
}

.navbar-home img {
    max-height: 40px !important;
}

/* ===== صفحة تسجيل الدخول ===== */
#page-login {
    background: var(--violet-100) !important;
}

/* ===== رؤوس الجداول ===== */
.list-row-head {
    background: var(--blue-300) !important;
    text-align: center !important;
    line-height: 30px !important;
    font-size: smaller !important;
    padding: 0 !important;
}

/* ===== محتوى الصفحة ===== */
.page-content {
    padding: 5px 0 !important;
}

/* ===== صفحة مساحات العمل ===== */
#page-Workspaces {
    background: var(--blue-700) !important;
}

/* ===== رأس الصفحة ===== */
.page-head {
    margin-bottom: 0 !important;
    height: 40px !important;
    top: 45px !important;
}

.page-head .page-head-content {
    height: 40px !important;
    padding: 6px 0 !important;
}

/* ===== القسم الرئيسي ===== */
.layout-main-section-wrapper {
    padding: 0 5px !important;
    margin-left: 15px !important;
}

/* ===== الروابط والاختصارات ===== */
.link-text, .shortcut-widget-box {
    border: 2px solid var(--blue-300) !important;
    background: var(--blue-50) !important;
    padding: 5px 7px !important;
    min-width: 60% !important;
    display: inline-block !important;
    color: var(--blue-800) !important;
    border-radius: 5px !important;
}

.shortcut-widget-box {
    background: var(--bg-purple) !important;
}

.link-text:hover {
    border: 2px solid var(--blue-400) !important;
}

/* ===== الشريط الجانبي ===== */
.sidebar-child-item .desk-sidebar-item {
    margin-right: 10px !important;
}

.desk-sidebar-item {
    margin: 5px auto !important;
    background: var(--blue-50) !important;
    border: 2px solid var(--blue-300) !important;
}

.desk-sidebar-item.selected {
    border: 2px solid var(--blue-700) !important;
}

.desk-sidebar-item.selected .item-anchor {
    background: var(--blue-300) !important;
}

.item-anchor:hover {
    background: var(--blue-200) !important;
}

.standard-sidebar-section {
    padding-right: 10px !important;
}

/* ===== القسم الجانبي ===== */
.layout-side-section {
    padding-left: 5px !important;
    padding-right: 5px !important;
}

.list-sidebar {
    padding: 10px !important;
    border-radius: 5px !important;
    background: var(--bg-color) !important;
}

/* ===== تحسينات للشاشات الكبيرة ===== */
@media (min-width: 768px) {
    body.full-width .container {
        width: 98% !important;
    }
}

/* ===== تحسينات الروابط المعطلة ===== */
.widget.links-widget-box .link-item .disabled-link {
    color: var(--text-muted) !important;
    border: 2px solid var(--blue-300) !important;
    background: var(--blue-50) !important;
    padding: 5px 7px !important;
    min-width: 60% !important;
    display: inline-block !important;
    color: var(--blue-800) !important;
    border-radius: 5px !important;
}

.widget.links-widget-box .link-item {
    display: flex !important;
    text-decoration: none !important;
    font-size: var(--text-base) !important;
    font-weight: var(--weight-regular) !important;
    letter-spacing: 0.02em !important;
    color: var(--text-color) !important;
    padding: 4px !important;
    margin-left: -4px !important;
    margin-bottom: 0px !important;
    cursor: pointer !important;
    border: 2px solid var(--blue-300) !important;
}

/* ===== تحسينات إضافية للثيم ===== */

/* تحسين الخطوط العربية */
body, .form-control, .btn, .nav-link {
    font-family: "Cairo", "Segoe UI", Tahoma, Geneva, Verdana, sans-serif !important;
}

/* تحسين الأزرار */
/* .btn {
    border-radius: var(--border-radius) !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
}

.btn-primary {
    background: var(--blue-700) !important;
    border-color: var(--blue-700) !important;
}

.btn-primary:hover {
    background: var(--blue-800) !important;
    border-color: var(--blue-800) !important;
    transform: translateY(-1px) !important;
} */
/* تحسين مظهر الأزرار وتصفيطها في صف واحد - الكود الأصلي */
/*
.btn {
    border-radius: var(--border-radius);
    font-weight: 600;
    padding: 6px 14px;
    transition: all 0.3s ease !important;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    margin: 2px;
}

.btn-primary {
    background: var(--blue-700);
    border: 1px solid var(--blue-700);
    color: #fff;
}

.btn-primary:hover {
    background: var(--blue-800);
    border-color: var(--blue-800);
    transform: translateY(-1px);
}

.page-actions,
.btn-group {
    display: flex !important;
    flex-direction: row !important;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
}
*/

/* ✅ تحسين مظهر الأزرار وتصفيطها في صف واحد - الكود الجديد المحسن */

/* ترتيب الأزرار في صف واحد */
.form-footer,
.form-actions,
.page-actions,
.btn-group,
.form-page .page-head .standard-actions,
.page-head .standard-actions {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 8px !important;
    align-items: center !important;
    justify-content: flex-start !important;
    margin: 10px 0 !important;
}

/* تنسيق عام لجميع الأزرار */
.btn {
    border-radius: var(--border-radius) !important;
    font-weight: 600 !important;
    padding: 8px 16px !important;
    transition: all 0.3s ease !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 6px !important;
    margin: 2px !important;
    min-width: 100px !important;
    height: 36px !important;
    font-family: "Cairo", sans-serif !important;
    font-size: 13px !important;
}

/* زر الحفظ أو الأساسي */
.btn-primary {
    background: linear-gradient(135deg, var(--blue-700), var(--blue-800)) !important;
    border: none !important;
    color: white !important;
    box-shadow: var(--shadow-sm) !important;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--blue-800), var(--blue-900)) !important;
    transform: translateY(-1px) !important;
    box-shadow: var(--shadow-md) !important;
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
    border: none !important;
    color: white !important;
}

.btn-secondary,
.btn-default {
    background: linear-gradient(135deg, #6c757d, #5a6268) !important;
    border: none !important;
    color: white !important;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107, #e0a800) !important;
    border: none !important;
    color: #212529 !important;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8, #138496) !important;
    border: none !important;
    color: white !important;
}

/* تحسين شريط الأدوات العلوي */
.page-head .standard-actions {
    background: rgba(255, 255, 255, 0.95) !important;
    border-radius: var(--border-radius) !important;
    padding: 8px 12px !important;
    box-shadow: var(--shadow-sm) !important;
    margin: 5px 0 !important;
}


/* تحسين النماذج */
.form-control {
    border: 2px solid var(--blue-300) !important;
    border-radius: var(--border-radius-small) !important;
    font-family: "Cairo", sans-serif !important;
}

.form-control:focus {
    border-color: var(--blue-700) !important;
    box-shadow: 0 0 0 0.2rem rgba(29, 78, 216, 0.25) !important;
}

/* تحسين الجداول */
.table {
    border-radius: var(--border-radius) !important;
    overflow: hidden !important;
}

.table thead th {
    background: var(--blue-300) !important;
    color: var(--blue-900) !important;
    font-family: "Cairo", sans-serif !important;
    font-weight: 700 !important;
}

.table tbody tr:hover {
    background: var(--blue-50) !important;
}

/* تحسين البطاقات */
.card {
    border: 2px solid var(--blue-300) !important;
    border-radius: var(--border-radius) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.card-header {
    background: var(--blue-300) !important;
    color: var(--blue-900) !important;
    font-family: "Cairo", sans-serif !important;
    font-weight: 700 !important;
}

/* ===== تحسينات القوائم المنسدلة ===== */
/* .dropdown-menu {
    border: 2px solid var(--blue-300) !important;
    border-radius: var(--border-radius) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
} */
/* تحديد النافذة المنبثقة وجعل حجمها صغير - الكود الأصلي */
/*
.modal, .frappe-control[data-fieldname="preview"] .popover, .dropdown-menu {
    max-width: 300px !important;
    width: auto !important;
    min-width: 200px !important;
    padding: 10px;
    font-size: 14px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0,0,0,0.2);
}

.dropdown-menu {
    top: calc(100% + 5px) !important;
    left: 0 !important;
}
*/

/* تحسينات النوافذ المنبثقة - الكود الجديد المحسن */
/* تقليل حجم النوافذ المنبثقة */
.modal-dialog {
    max-width: 600px !important;
    width: 90% !important;
    margin: 30px auto !important;
}

.modal-dialog.modal-lg {
    max-width: 800px !important;
    width: 95% !important;
}

.modal-dialog.modal-xl {
    max-width: 1000px !important;
    width: 98% !important;
}

.modal-dialog.modal-sm {
    max-width: 400px !important;
    width: 80% !important;
}

/* تحسين النوافذ المنبثقة للمعاينة */
.modal-dialog.print-preview-modal {
    max-width: 700px !important;
    width: 90% !important;
    margin: 20px auto !important;
}

/* تحسين نافذة المعاينة */
.print-preview-wrapper {
    max-height: 70vh !important;
    overflow-y: auto !important;
    padding: 15px !important;
}

/* تحسين نوافذ الرسائل */
.msgprint {
    border-radius: var(--border-radius) !important;
    box-shadow: var(--shadow-lg) !important;
    max-width: 500px !important;
    margin: 20px auto !important;
    padding: 20px !important;
}

/* جعلها تظهر أسفل الزر */
.dropdown-menu {
    top: calc(100% + 5px) !important;
    left: 0 !important;
    max-width: 300px !important;
    min-width: 200px !important;
    border-radius: var(--border-radius) !important;
    box-shadow: var(--shadow-md) !important;
}
.dropdown-item {
    font-family: "Cairo", sans-serif !important;
    font-size: smaller !important;
}

.dropdown-item:hover {
    background: var(--blue-100) !important;
    color: var(--blue-900) !important;
}

/* ===== تحسينات النوافذ المنبثقة - الكود الأصلي ===== */
/*
.modal-content {
    border: 2px solid var(--blue-300) !important;
    border-radius: var(--border-radius-large) !important;
}

.modal-header {
    background: var(--blue-400) !important;
    color: white !important;
    font-family: "Cairo", sans-serif !important;
    font-weight: 700 !important;
}
*/

/* ===== تحسينات النوافذ المنبثقة - الكود الجديد المحسن ===== */
.modal-content {
    border-radius: var(--border-radius-large) !important;
    border: none !important;
    box-shadow: var(--shadow-lg) !important;
}

.modal-header {
    background: linear-gradient(135deg, var(--blue-600), var(--blue-700)) !important;
    color: white !important;
    border-bottom: none !important;
    border-radius: var(--border-radius-large) var(--border-radius-large) 0 0 !important;
    padding: 15px 20px !important;
    font-family: "Cairo", sans-serif !important;
    font-weight: 700 !important;
}

.modal-header .modal-title {
    font-weight: 700 !important;
    font-size: 16px !important;
    font-family: "Cairo", sans-serif !important;
}

.modal-header .close {
    color: white !important;
    opacity: 0.8 !important;
    font-size: 24px !important;
}

.modal-header .close:hover {
    opacity: 1 !important;
}

.modal-body {
    padding: 20px !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
    font-family: "Cairo", sans-serif !important;
}

.modal-footer {
    border-top: 1px solid var(--border-light) !important;
    padding: 15px 20px !important;
    display: flex !important;
    justify-content: flex-end !important;
    gap: 10px !important;
}

.modal-footer .btn {
    min-width: 80px !important;
    height: 36px !important;
    font-size: 13px !important;
    font-weight: 600 !important;
    font-family: "Cairo", sans-serif !important;
}

/* ===== تحسينات التنبيهات ===== */
.alert {
    border-radius: var(--border-radius) !important;
    font-family: "Cairo", sans-serif !important;
    font-weight: 600 !important;
}

.alert-success {
    background: #d1fae5 !important;
    border: 2px solid #10b981 !important;
    color: #065f46 !important;
}

.alert-warning {
    background: #fef3c7 !important;
    border: 2px solid #f59e0b !important;
    color: #92400e !important;
}

.alert-danger {
    background: #fee2e2 !important;
    border: 2px solid #ef4444 !important;
    color: #991b1b !important;
}

.alert-info {
    background: var(--blue-50) !important;
    border: 2px solid var(--blue-500) !important;
    color: var(--blue-900) !important;
}

/* ===== تحسينات شريط التقدم ===== */
.progress {
    height: 8px !important;
    border-radius: 4px !important;
    background: var(--blue-100) !important;
}

.progress-bar {
    background: var(--blue-600) !important;
    border-radius: 4px !important;
}

/* ===== تحسينات الشارات ===== */
.badge {
    border-radius: 12px !important;
    font-family: "Cairo", sans-serif !important;
    font-weight: 600 !important;
}

.badge-primary {
    background: var(--blue-600) !important;
}

.badge-success {
    background: #10b981 !important;
}

.badge-warning {
    background: #f59e0b !important;
}

.badge-danger {
    background: #ef4444 !important;
}

/* ===== تحسينات خاصة للتجاوب مع الشاشات الصغيرة - الكود الأصلي ===== */
/*
@media (max-width: 768px) {
    .navbar {
        height: 40px !important;
        padding: 0.5rem !important;
    }

    .page-head {
        height: 35px !important;
        top: 40px !important;
    }

    .layout-main-section-wrapper {
        margin-left: 5px !important;
        padding: 0 2px !important;
    }

    .desk-sidebar-item {
        margin: 3px auto !important;
    }

    .link-text, .shortcut-widget-box {
        min-width: 90% !important;
        padding: 3px 5px !important;
    }
}
*/

/* ===== تحسينات خاصة للتجاوب مع الشاشات الصغيرة - الكود الجديد المحسن ===== */
@media (max-width: 768px) {
    .navbar {
        height: 40px !important;
        padding: 0.5rem !important;
    }

    .page-head {
        height: 35px !important;
        top: 40px !important;
    }

    .layout-main-section-wrapper {
        margin-left: 5px !important;
        padding: 0 2px !important;
    }

    .desk-sidebar-item {
        margin: 3px auto !important;
    }

    .link-text, .shortcut-widget-box {
        min-width: 90% !important;
        padding: 3px 5px !important;
    }

    /* تحسين الأزرار للشاشات الصغيرة */
    .form-footer,
    .form-actions,
    .page-actions,
    .btn-group,
    .form-page .page-head .standard-actions,
    .page-head .standard-actions {
        flex-direction: row !important;
        flex-wrap: wrap !important;
        gap: 5px !important;
        padding: 5px !important;
    }

    .btn {
        min-width: 80px !important;
        height: 32px !important;
        font-size: 12px !important;
        padding: 6px 12px !important;
        margin: 1px !important;
    }

    /* تحسين شريط الأدوات للشاشات الصغيرة */
    .page-head .standard-actions {
        padding: 5px 8px !important;
        margin: 3px 0 !important;
    }

    /* تحسين النوافذ المنبثقة للشاشات الصغيرة */
    .modal-dialog {
        max-width: 95% !important;
        width: 95% !important;
        margin: 10px auto !important;
    }

    .modal-dialog.modal-lg,
    .modal-dialog.modal-xl {
        max-width: 98% !important;
        width: 98% !important;
    }

    .msgprint {
        max-width: 90% !important;
        margin: 10px auto !important;
        padding: 15px !important;
    }

    .print-preview-wrapper {
        max-height: 50vh !important;
        padding: 10px !important;
    }

    /* تحسين القوائم المنسدلة للشاشات الصغيرة */
    .dropdown-menu {
        min-width: 150px !important;
        max-width: 250px !important;
    }

    .dropdown-item {
        font-size: 12px !important;
        padding: 6px 10px !important;
    }

    /* تحسين النصوص للشاشات الصغيرة */
    body, p, h1, h2, h3, h4, h5, h6 {
        font-size: 14px !important;
        line-height: 1.4 !important;
    }

    h1 { font-size: 20px !important; }
    h2 { font-size: 18px !important; }
    h3 { font-size: 16px !important; }
    h4 { font-size: 15px !important; }
    h5 { font-size: 14px !important; }
    h6 { font-size: 13px !important; }
}

/* ===== تحسينات إضافية للأداء ===== */
* {
    box-sizing: border-box !important;
}

/* تحسين الحركات والانتقالات */
.btn, .form-control, .card, .dropdown-menu, .modal-content {
    transition: all 0.3s ease !important;
}

/* تحسين التمرير */
::-webkit-scrollbar {
    width: 8px !important;
}

::-webkit-scrollbar-track {
    background: var(--blue-100) !important;
}

::-webkit-scrollbar-thumb {
    background: var(--blue-400) !important;
    border-radius: 4px !important;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--blue-600) !important;
}

/* ===== تحسينات نهائية للثيم ===== */

/* تحسين مظهر الروابط */
a {
    color: var(--blue-600) !important;
    text-decoration: none !important;
    font-family: "Cairo", sans-serif !important;
}

a:hover {
    color: var(--blue-800) !important;
    text-decoration: underline !important;
}

/* تحسين مظهر التبويبات */
.nav-tabs .nav-link {
    font-family: "Cairo", sans-serif !important;
    font-weight: 600 !important;
    border: 2px solid transparent !important;
}

.nav-tabs .nav-link.active {
    background: var(--blue-600) !important;
    color: white !important;
    border-color: var(--blue-600) !important;
}

.nav-tabs .nav-link:hover {
    background: var(--blue-100) !important;
    color: var(--blue-800) !important;
    border-color: var(--blue-300) !important;
}

/* تحسين مظهر شريط البحث */
.search-bar, input[type="search"] {
    border: 2px solid var(--blue-300) !important;
    border-radius: 25px !important;
    padding: 8px 15px !important;
    font-family: "Cairo", sans-serif !important;
}

.search-bar:focus, input[type="search"]:focus {
    border-color: var(--blue-600) !important;
    box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25) !important;
}

/* ===== تحسينات خاصة للعناصر الأساسية ===== */

/* تحسين مظهر الأزرار الصغيرة */
.btn-xs, .btn-sm {
    font-family: "Cairo", sans-serif !important;
    font-weight: 600 !important;
    border-radius: var(--border-radius-small) !important;
}

/* تحسين مظهر العناوين */
h1, h2, h3, h4, h5, h6 {
    font-family: "Cairo", sans-serif !important;
    font-weight: 700 !important;
    color: var(--blue-900) !important;
}

/* تحسين مظهر النصوص */
p, span, div {
    font-family: "Cairo", sans-serif !important;
}

/* تحسين مظهر القوائم */
ul, ol, li {
    font-family: "Cairo", sans-serif !important;
}

/* ===== تحسينات نهائية للثيم ===== */

/* تحسين مظهر الإشعارات */
.notification {
    font-family: "Cairo", sans-serif !important;
    border-radius: var(--border-radius) !important;
    border: 2px solid var(--blue-300) !important;
}

/* تحسين مظهر التولتيب */
.tooltip {
    font-family: "Cairo", sans-serif !important;
}

/* تحسين مظهر البوبوفر */
.popover {
    font-family: "Cairo", sans-serif !important;
    border: 2px solid var(--blue-300) !important;
    border-radius: var(--border-radius) !important;
}

/* تحسين مظهر الأكورديون */
.accordion {
    font-family: "Cairo", sans-serif !important;
}

.accordion-header {
    background: var(--blue-100) !important;
    color: var(--blue-900) !important;
    font-weight: 700 !important;
}

/* تحسين مظهر الكاروسيل */
.carousel {
    border-radius: var(--border-radius) !important;
    overflow: hidden !important;
}

/* تحسين مظهر الباجينيشن */
.pagination .page-link {
    font-family: "Cairo", sans-serif !important;
    color: var(--blue-600) !important;
    border: 2px solid var(--blue-300) !important;
}

.pagination .page-link:hover {
    background: var(--blue-100) !important;
    color: var(--blue-800) !important;
}

.pagination .page-item.active .page-link {
    background: var(--blue-600) !important;
    border-color: var(--blue-600) !important;
}

/* ===== تحسينات خاصة بالطباعة ===== */
@media print {
    body {
        font-family: "Cairo", sans-serif !important;
        color: black !important;
        background: white !important;
    }

    .navbar, .sidebar {
        display: none !important;
    }

    .page-content {
        margin: 0 !important;
        padding: 0 !important;
    }
}

/* ===== نهاية الثيم المخصص ===== */

/* رسالة تأكيد تحميل الثيم */
body::after {
    content: "✅ تم تحميل ثيم Cairo بنجاح!" !important;
    position: fixed !important;
    bottom: 20px !important;
    right: 20px !important;
    background: var(--blue-600) !important;
    color: white !important;
    padding: 10px 15px !important;
    border-radius: var(--border-radius) !important;
    font-family: "Cairo", sans-serif !important;
    font-size: 12px !important;
    z-index: 9999 !important;
    opacity: 0 !important;
    animation: showMessage 3s ease-in-out !important;
}

@keyframes showMessage {
    0%, 90% { opacity: 0; }
    10%, 80% { opacity: 1; }
    100% { opacity: 0; }
}


/* ===== نهاية ملف الثيم المخصص ===== */
