/* تخصيص واجهة الفاتورة النقدية */
/* POS Invoice Interface Customization */

/* تحسين عرض النموذج العام للفواتير النقدية */
.form-layout .form-page {
    background: linear-gradient(135deg, #f1f3f4 0%, #e8eaf6 100%);
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    margin: 15px;
    padding: 25px;
}

/* توحيد لون الأشرطة الفاصلة للفواتير النقدية */
.section-break, .column-break, .page-break {
    border-top: 2px solid #f8f9fa !important;
    margin: 20px 0 !important;
}

.form-section {
    border-bottom: 1px solid #f8f9fa;
    margin-bottom: 20px;
    padding-bottom: 15px;
}

/* تنسيق خاص لقسم العميل في الفواتير النقدية */
.pos-customer-section {
    background: linear-gradient(135deg, #1e88e5 0%, #1565c0 100%);
    color: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 6px 20px rgba(30, 136, 229, 0.3);
}

.pos-customer-section .frappe-control {
    margin-bottom: 15px;
}

.pos-customer-section label {
    color: white !important;
    font-weight: bold;
    font-size: 14px;
}

.pos-customer-section .form-control {
    background: rgba(255,255,255,0.95);
    border: none;
    border-radius: 8px;
    font-size: 16px;
    padding: 12px;
}

/* تنسيق قسم البنود للفواتير النقدية */
.pos-items-section {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    padding: 25px;
    margin-bottom: 25px;
}

.pos-items-section .section-head {
    background: linear-gradient(135deg, #43a047 0%, #2e7d32 100%);
    color: white;
    margin: -25px -25px 20px -25px;
    padding: 15px 25px;
    border-radius: 15px 15px 0 0;
    font-weight: bold;
    font-size: 18px;
    text-align: center;
}

/* تحسين جدول البنود للفواتير النقدية */
.grid-wrapper {
    background: white;
    border-radius: 12px;
    box-shadow: 0 3px 12px rgba(0,0,0,0.1);
    overflow: hidden;
    border: 2px solid #e3f2fd;
}

.grid-header-row {
    background: linear-gradient(135deg, #43a047 0%, #2e7d32 100%);
    color: white;
    font-weight: bold;
    font-size: 14px;
}

.grid-header-row .grid-static-col {
    color: white !important;
    text-align: center;
    padding: 15px 10px;
    border-right: 1px solid rgba(255,255,255,0.3);
    font-weight: bold;
}

.grid-row {
    border-bottom: 1px solid #e0e0e0;
    transition: all 0.3s ease;
}

.grid-row:hover {
    background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.grid-row .grid-static-col {
    padding: 12px 10px;
    text-align: center;
    vertical-align: middle;
    font-size: 14px;
}

/* تنسيق قسم المجاميع للفواتير النقدية */
.pos-totals-section {
    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
    color: white;
    border-radius: 15px;
    padding: 25px;
    margin: 25px 0;
    box-shadow: 0 6px 20px rgba(255, 152, 0, 0.3);
}

.pos-totals-section .frappe-control label {
    color: white !important;
    font-weight: bold;
    font-size: 14px;
}

.pos-totals-section .form-control {
    background: rgba(255,255,255,0.95);
    border: none;
    border-radius: 8px;
    font-weight: bold;
    text-align: center;
    font-size: 16px;
    padding: 12px;
}

/* تنسيق قسم الدفع للفواتير النقدية */
.pos-payment-section {
    background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
    color: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.3);
}

.pos-payment-section .section-head {
    background: linear-gradient(135deg, #2e7d32 0%, #1b5e20 100%);
    color: white;
    margin: -25px -25px 20px -25px;
    padding: 15px 25px;
    border-radius: 15px 15px 0 0;
    font-weight: bold;
    font-size: 18px;
    text-align: center;
}

/* تنسيق الحقول المهمة للفواتير النقدية */
/* تحسين عرض حقل العميل مع سهم قابل للنقر للفواتير النقدية */
.form-control[data-fieldname="customer"] {
    font-size: 18px;
    font-weight: bold;
    border: 3px solid #1e88e5;
    border-radius: 10px;
    padding: 15px 50px 15px 15px;
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    position: relative;
}

/* حاوي حقل العميل مع السهم للفواتير النقدية */
.frappe-control[data-fieldname="customer"] {
    position: relative;
}

/* سهم معلومات العميل للفواتير النقدية */
.customer-info-toggle {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: #1e88e5;
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    cursor: pointer;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    z-index: 10;
}

.customer-info-toggle:hover {
    background: #1565c0;
    transform: translateY(-50%) scale(1.1);
}

/* صندوق معلومات العميل القابل للطي للفواتير النقدية */
.customer-info-collapsible {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out;
    background: linear-gradient(135deg, #00bcd4 0%, #0097a7 100%);
    color: white;
    border-radius: 0 0 12px 12px;
    margin-top: -10px;
    position: relative;
    z-index: 5;
}

.customer-info-collapsible.expanded {
    max-height: 200px;
    padding: 20px;
    margin-top: 10px;
    border-radius: 12px;
}

.customer-info-collapsible h6 {
    color: white;
    margin-bottom: 15px;
    text-align: center;
    font-weight: bold;
    font-size: 16px;
}

.customer-info-collapsible .row {
    margin: 0;
}

.customer-info-collapsible .row div {
    padding: 5px 10px;
}

.customer-info-collapsible p {
    margin-bottom: 8px;
    font-size: 14px;
}

/* تأثير الحركة للسهم للفواتير النقدية */
.customer-info-toggle.rotated {
    transform: translateY(-50%) rotate(180deg);
}

.customer-info-toggle.rotated:hover {
    transform: translateY(-50%) rotate(180deg) scale(1.1);
}

.form-control[data-fieldname="posting_date"] {
    font-size: 16px;
    font-weight: bold;
    border: 3px solid #43a047;
    border-radius: 10px;
    padding: 12px;
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
}

.form-control[data-fieldname="grand_total"] {
    font-size: 28px;
    font-weight: bold;
    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
    color: white;
    text-align: center;
    border: none;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(255, 152, 0, 0.4);
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

.form-control[data-fieldname="paid_amount"] {
    font-size: 24px;
    font-weight: bold;
    background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
    color: white;
    text-align: center;
    border: none;
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
}

.form-control[data-fieldname="change_amount"] {
    font-size: 24px;
    font-weight: bold;
    background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
    color: white;
    text-align: center;
    border: none;
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.4);
}

/* تحسين حقل مسح الباركود */
.form-control[data-fieldname="scan_barcode"] {
    font-size: 20px;
    border: 3px solid #9c27b0;
    border-radius: 10px;
    padding: 15px;
    background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
    text-align: center;
    font-weight: bold;
}

.form-control[data-fieldname="scan_barcode"]:focus {
    border-color: #7b1fa2;
    box-shadow: 0 0 15px rgba(156, 39, 176, 0.3);
}

/* تنسيق الأزرار للفواتير النقدية - الكود الأصلي */
/*
.btn-primary {
    background: linear-gradient(135deg, #1e88e5 0%, #1565c0 100%);
    border: none;
    border-radius: 12px;
    padding: 15px 30px;
    font-weight: bold;
    font-size: 16px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(30, 136, 229, 0.3);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(30, 136, 229, 0.4);
    background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
}

.btn-success {
    background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
    border: none;
    border-radius: 12px;
    padding: 15px 30px;
    font-weight: bold;
    font-size: 16px;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.btn-success:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(76, 175, 80, 0.4);
}
*/

/* تنسيق الأزرار المحسن للفواتير النقدية - الكود الجديد */
/* ترتيب الأزرار في صف واحد */
.form-footer,
.form-actions,
.page-actions,
.btn-group,
.form-page .page-head .standard-actions {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 8px !important;
    align-items: center !important;
    justify-content: flex-start !important;
    margin: 10px 0 !important;
}

/* تنسيق الأزرار الفردية */
.btn-primary {
    background: linear-gradient(135deg, #1e88e5 0%, #1565c0 100%) !important;
    border: none !important;
    border-radius: 8px !important;
    padding: 10px 18px !important;
    font-weight: 600 !important;
    font-size: 14px !important;
    transition: all 0.3s ease !important;
    margin: 2px !important;
    min-width: 110px !important;
    height: 38px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    box-shadow: 0 3px 10px rgba(30, 136, 229, 0.3) !important;
}

.btn-primary:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 15px rgba(30, 136, 229, 0.4) !important;
    background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%) !important;
}

.btn-success {
    background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%) !important;
    border: none !important;
    border-radius: 8px !important;
    padding: 10px 18px !important;
    font-weight: 600 !important;
    font-size: 14px !important;
    margin: 2px !important;
    min-width: 110px !important;
    height: 38px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    box-shadow: 0 3px 10px rgba(76, 175, 80, 0.3) !important;
}

.btn-success:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4) !important;
}

.btn-secondary,
.btn-default {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%) !important;
    border: none !important;
    border-radius: 8px !important;
    padding: 10px 18px !important;
    font-weight: 600 !important;
    font-size: 14px !important;
    color: white !important;
    margin: 2px !important;
    min-width: 110px !important;
    height: 38px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important;
    border: none !important;
    border-radius: 8px !important;
    padding: 10px 18px !important;
    font-weight: 600 !important;
    font-size: 14px !important;
    color: #212529 !important;
    margin: 2px !important;
    min-width: 110px !important;
    height: 38px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
    border: none !important;
    border-radius: 8px !important;
    padding: 10px 18px !important;
    font-weight: 600 !important;
    font-size: 14px !important;
    color: white !important;
    margin: 2px !important;
    min-width: 110px !important;
    height: 38px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* تحسين شريط الأدوات العلوي */
.page-head .standard-actions {
    background: rgba(255, 255, 255, 0.95) !important;
    border-radius: 10px !important;
    padding: 10px 15px !important;
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1) !important;
    margin: 8px 0 !important;
}

/* تحسين جدول المدفوعات */
.payments-grid .grid-header-row {
    background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
    color: white;
}

.payments-grid .grid-row:hover {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
}

/* تنسيق معلومات العميل للفواتير النقدية */
.pos-customer-info-box {
    background: linear-gradient(135deg, #00bcd4 0%, #0097a7 100%);
    color: white;
    border-radius: 12px;
    padding: 20px;
    margin: 15px 0;
    box-shadow: 0 6px 15px rgba(0, 188, 212, 0.3);
}

.pos-customer-info-box h6 {
    color: white;
    margin-bottom: 15px;
    text-align: center;
    font-weight: bold;
    font-size: 16px;
}

.pos-customer-info-box p {
    margin-bottom: 8px;
    font-size: 14px;
}

/* تنسيق ملخص الدفع */
.pos-payment-summary {
    background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
    color: white;
    border-radius: 12px;
    padding: 20px;
    margin: 15px 0;
    box-shadow: 0 6px 15px rgba(76, 175, 80, 0.3);
}

.pos-payment-summary h5 {
    color: white;
    margin-bottom: 15px;
    text-align: center;
    font-weight: bold;
}

.pos-payment-summary .row div {
    text-align: center;
    padding: 10px;
}

/* تنسيق المعلومات السريعة للفواتير النقدية */
.pos-quick-info-section {
    background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);
    color: white;
    border-radius: 12px;
    padding: 20px;
    margin: 15px 0;
    box-shadow: 0 6px 15px rgba(156, 39, 176, 0.3);
}

.pos-quick-info-section h6 {
    color: white;
    margin-bottom: 15px;
    text-align: center;
    font-weight: bold;
    font-size: 16px;
}

.pos-quick-info-section .row div {
    text-align: center;
    padding: 10px;
    font-size: 14px;
}

/* تحسين المؤشرات للفواتير النقدية */
.indicator {
    border-radius: 25px;
    padding: 8px 20px;
    font-weight: bold;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.indicator.green {
    background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
    color: white;
    box-shadow: 0 3px 10px rgba(76, 175, 80, 0.3);
}

.indicator.blue {
    background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
    color: white;
    box-shadow: 0 3px 10px rgba(33, 150, 243, 0.3);
}

.indicator.orange {
    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
    color: white;
    box-shadow: 0 3px 10px rgba(255, 152, 0, 0.3);
}

/* تحسين عرض التبويبات للفواتير النقدية */
.form-tabs .nav-link {
    background: linear-gradient(135deg, #f5f5f5 0%, #eeeeee 100%);
    border: 2px solid #e0e0e0;
    border-radius: 10px 10px 0 0;
    margin-right: 8px;
    font-weight: bold;
    color: #424242;
    transition: all 0.3s ease;
    padding: 12px 20px;
}

.form-tabs .nav-link.active {
    background: linear-gradient(135deg, #1e88e5 0%, #1565c0 100%);
    color: white;
    border-color: #1e88e5;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(30, 136, 229, 0.3);
}

.form-tabs .nav-link:hover {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

/* تحسينات النوافذ المنبثقة للفواتير النقدية - الكود الجديد */
/* تقليل حجم النوافذ المنبثقة */
.modal-dialog {
    max-width: 650px !important;
    width: 90% !important;
    margin: 25px auto !important;
}

.modal-dialog.modal-lg {
    max-width: 850px !important;
    width: 95% !important;
}

.modal-dialog.modal-xl {
    max-width: 1050px !important;
    width: 98% !important;
}

.modal-dialog.modal-sm {
    max-width: 450px !important;
    width: 80% !important;
}

/* تحسين النوافذ المنبثقة للمعاينة */
.modal-dialog.print-preview-modal {
    max-width: 750px !important;
    width: 90% !important;
    margin: 20px auto !important;
}

/* تحسين نافذة المعاينة */
.print-preview-wrapper {
    max-height: 65vh !important;
    overflow-y: auto !important;
    padding: 20px !important;
}

/* تحسين نوافذ الرسائل */
.msgprint {
    border-radius: 10px !important;
    box-shadow: 0 6px 20px rgba(0,0,0,0.15) !important;
    max-width: 550px !important;
    margin: 25px auto !important;
    padding: 25px !important;
}

/* تحسين نوافذ التأكيد */
.modal-content {
    border-radius: 15px !important;
    border: none !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2) !important;
}

.modal-header {
    background: linear-gradient(135deg, #1e88e5 0%, #1565c0 100%) !important;
    color: white !important;
    border-bottom: none !important;
    border-radius: 15px 15px 0 0 !important;
    padding: 18px 25px !important;
}

.modal-header .modal-title {
    font-weight: 700 !important;
    font-size: 17px !important;
}

.modal-header .close {
    color: white !important;
    opacity: 0.8 !important;
    font-size: 26px !important;
}

.modal-header .close:hover {
    opacity: 1 !important;
}

.modal-body {
    padding: 25px !important;
    font-size: 15px !important;
    line-height: 1.6 !important;
}

.modal-footer {
    border-top: 1px solid #e9ecef !important;
    padding: 18px 25px !important;
    display: flex !important;
    justify-content: flex-end !important;
    gap: 12px !important;
}

.modal-footer .btn {
    min-width: 90px !important;
    height: 38px !important;
    font-size: 14px !important;
    font-weight: 600 !important;
}

/* تحسين عرض الشاشات الصغيرة للفواتير النقدية */
@media (max-width: 768px) {
    .form-layout .form-page {
        margin: 8px !important;
        padding: 15px !important;
    }

    .pos-customer-section,
    .pos-items-section,
    .pos-payment-section {
        padding: 15px !important;
        margin-bottom: 15px !important;
    }

    .pos-customer-info-box,
    .pos-payment-summary,
    .pos-quick-info-section {
        padding: 15px !important;
        margin: 10px 0 !important;
    }

    .form-control[data-fieldname="grand_total"] {
        font-size: 20px !important;
        padding: 15px !important;
    }

    .form-control[data-fieldname="paid_amount"],
    .form-control[data-fieldname="change_amount"] {
        font-size: 18px !important;
        padding: 12px !important;
    }

    .grid-wrapper {
        overflow-x: auto !important;
    }

    /* تحسين الأزرار للشاشات الصغيرة */
    .form-footer,
    .form-actions,
    .page-actions,
    .btn-group,
    .form-page .page-head .standard-actions {
        flex-direction: row !important;
        flex-wrap: wrap !important;
        gap: 6px !important;
        padding: 6px !important;
    }

    .btn-primary,
    .btn-success,
    .btn-secondary,
    .btn-default,
    .btn-warning,
    .btn-info {
        min-width: 90px !important;
        height: 34px !important;
        font-size: 13px !important;
        padding: 8px 14px !important;
        margin: 1px !important;
    }

    /* تحسين النوافذ المنبثقة للشاشات الصغيرة */
    .modal-dialog {
        max-width: 95% !important;
        width: 95% !important;
        margin: 10px auto !important;
    }

    .modal-dialog.modal-lg,
    .modal-dialog.modal-xl {
        max-width: 98% !important;
        width: 98% !important;
    }

    .msgprint {
        max-width: 90% !important;
        margin: 15px auto !important;
        padding: 18px !important;
    }

    .print-preview-wrapper {
        max-height: 50vh !important;
        padding: 15px !important;
    }
}

/* تحسين الطباعة للفواتير النقدية */
@media print {
    .pos-customer-section,
    .pos-items-section,
    .pos-payment-section {
        background: white !important;
        color: black !important;
        border: 2px solid #ccc;
        box-shadow: none;
    }

    .pos-customer-info-box,
    .pos-payment-summary,
    .pos-quick-info-section {
        background: white !important;
        color: black !important;
        border: 1px solid #ccc;
    }

    .btn {
        display: none;
    }

    .form-control[data-fieldname="grand_total"],
    .form-control[data-fieldname="paid_amount"],
    .form-control[data-fieldname="change_amount"] {
        background: white !important;
        color: black !important;
        border: 2px solid #333;
    }
}

/* تأثيرات الحركة للفواتير النقدية */
.pos-customer-section,
.pos-items-section,
.pos-payment-section {
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.grid-row {
    animation: fadeInScale 0.4s ease-out;
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}



/* تنسيق زر معاينة القيود */
.btn-preview-entries {
    background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%) !important;
    color: white !important;
    border: none !important;
    border-radius: 8px !important;
    padding: 10px 20px !important;
    font-weight: bold !important;
    font-size: 14px !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 12px rgba(156, 39, 176, 0.3) !important;
}

.btn-preview-entries:hover {
    background: linear-gradient(135deg, #8e24aa 0%, #6a1b9a 100%) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 16px rgba(156, 39, 176, 0.4) !important;
    color: white !important;
}

.btn-preview-entries:active {
    transform: translateY(0) !important;
    box-shadow: 0 2px 8px rgba(156, 39, 176, 0.3) !important;
}

.btn-preview-entries i {
    margin-right: 8px;
}



/* تحسين عرض الأيقونات للفواتير النقدية */
.pos-customer-section::before {
    content: "🧑‍💼";
    font-size: 28px;
    margin-right: 15px;
    display: inline-block;
}

.pos-items-section .section-head::before {
    content: "🛒";
    font-size: 24px;
    margin-right: 15px;
}

.pos-payment-section .section-head::before {
    content: "💳";
    font-size: 24px;
    margin-right: 15px;
}
