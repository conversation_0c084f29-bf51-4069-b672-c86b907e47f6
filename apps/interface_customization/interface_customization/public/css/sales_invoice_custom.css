/* تخصيص واجهة فاتورة المبيعات */
/* Sales Invoice Interface Customization */

/* تحسين عرض النموذج العام */
.form-layout .form-page {
    background: #f8f9fa;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin: 10px;
    padding: 20px;
}

/* توحيد لون الأشرطة الفاصلة */
.section-break, .column-break, .page-break {
    border-top: 2px solid #f8f9fa !important;
    margin: 20px 0 !important;
}

.form-section {
    border-bottom: 1px solid #f8f9fa;
    margin-bottom: 20px;
    padding-bottom: 15px;
}

/* تنسيق الأقسام */
.form-section {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 20px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.form-section .section-head {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    margin: -20px -20px 20px -20px;
    padding: 15px 20px;
    border-radius: 8px 8px 0 0;
    font-weight: bold;
    font-size: 16px;
    text-align: center;
}

/* تنسيق قسم بيانات العميل */
.customer-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.customer-section .frappe-control {
    margin-bottom: 15px;
}

.customer-section label {
    color: white !important;
    font-weight: bold;
}

.customer-section .form-control {
    background: rgba(255,255,255,0.9);
    border: none;
    border-radius: 5px;
}

/* تنسيق جدول البنود */
.grid-wrapper {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
}

.grid-header-row {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: white;
    font-weight: bold;
}

.grid-header-row .grid-static-col {
    color: white !important;
    text-align: center;
    padding: 12px 8px;
    border-right: 1px solid rgba(255,255,255,0.2);
}

.grid-row {
    border-bottom: 1px solid #e9ecef;
    transition: background-color 0.2s ease;
}

.grid-row:hover {
    background-color: #f8f9fa;
}

.grid-row .grid-static-col {
    padding: 10px 8px;
    text-align: center;
    vertical-align: middle;
}

/* تنسيق الحقول المهمة */
/* تحسين عرض حقل العميل مع سهم قابل للنقر */
.form-control[data-fieldname="customer"] {
    font-size: 18px;
    font-weight: bold;
    border: 3px solid #667eea;
    border-radius: 10px;
    padding: 15px 50px 15px 15px;
    background: linear-gradient(135deg, #e8eaf6 0%, #c5cae9 100%);
    position: relative;
}

/* حاوي حقل العميل مع السهم */
.frappe-control[data-fieldname="customer"] {
    position: relative;
}

/* سهم معلومات العميل */
.customer-info-toggle {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: #667eea;
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    cursor: pointer;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    z-index: 10;
}

.customer-info-toggle:hover {
    background: #5a67d8;
    transform: translateY(-50%) scale(1.1);
}

/* صندوق معلومات العميل القابل للطي */
.customer-info-collapsible {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out;
    background: linear-gradient(135deg, #00bcd4 0%, #0097a7 100%);
    color: white;
    border-radius: 0 0 12px 12px;
    margin-top: -10px;
    position: relative;
    z-index: 5;
}

.customer-info-collapsible.expanded {
    max-height: 200px;
    padding: 20px;
    margin-top: 10px;
    border-radius: 12px;
}

.customer-info-collapsible h6 {
    color: white;
    margin-bottom: 15px;
    text-align: center;
    font-weight: bold;
    font-size: 16px;
}

.customer-info-collapsible .row {
    margin: 0;
}

.customer-info-collapsible .row div {
    padding: 5px 10px;
}

.customer-info-collapsible p {
    margin-bottom: 8px;
    font-size: 14px;
}

/* تأثير الحركة للسهم */
.customer-info-toggle.rotated {
    transform: translateY(-50%) rotate(180deg);
}

.customer-info-toggle.rotated:hover {
    transform: translateY(-50%) rotate(180deg) scale(1.1);
}

.form-control[data-fieldname="posting_date"] {
    font-size: 14px;
    font-weight: bold;
    border: 2px solid #28a745;
    border-radius: 8px;
}

.form-control[data-fieldname="grand_total"] {
    font-size: 20px;
    font-weight: bold;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    text-align: center;
    border: none;
    border-radius: 8px;
    padding: 12px;
}

/* تنسيق قسم المجاميع */
.totals-section {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
}

.totals-section .frappe-control label {
    color: white !important;
    font-weight: bold;
}

.totals-section .form-control {
    background: rgba(255,255,255,0.9);
    border: none;
    border-radius: 5px;
    font-weight: bold;
    text-align: center;
}

/* تنسيق الأزرار - الكود الأصلي */
/*
.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 8px;
    padding: 10px 20px;
    font-weight: bold;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    border-radius: 8px;
    padding: 10px 20px;
    font-weight: bold;
}
*/

/* تنسيق الأزرار المحسن - الكود الجديد */
/* ترتيب الأزرار في صف واحد */
.form-footer,
.form-actions,
.page-actions,
.btn-group,
.form-page .page-head .standard-actions {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 8px !important;
    align-items: center !important;
    justify-content: flex-start !important;
    margin: 10px 0 !important;
}

/* تنسيق الأزرار الفردية */
.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border: none !important;
    border-radius: 6px !important;
    padding: 8px 16px !important;
    font-weight: 600 !important;
    font-size: 13px !important;
    transition: all 0.3s ease !important;
    margin: 2px !important;
    min-width: 100px !important;
    height: 36px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.btn-primary:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 3px 8px rgba(102, 126, 234, 0.3) !important;
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    border: none !important;
    border-radius: 6px !important;
    padding: 8px 16px !important;
    font-weight: 600 !important;
    font-size: 13px !important;
    margin: 2px !important;
    min-width: 100px !important;
    height: 36px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.btn-secondary,
.btn-default {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%) !important;
    border: none !important;
    border-radius: 6px !important;
    padding: 8px 16px !important;
    font-weight: 600 !important;
    font-size: 13px !important;
    color: white !important;
    margin: 2px !important;
    min-width: 100px !important;
    height: 36px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important;
    border: none !important;
    border-radius: 6px !important;
    padding: 8px 16px !important;
    font-weight: 600 !important;
    font-size: 13px !important;
    color: #212529 !important;
    margin: 2px !important;
    min-width: 100px !important;
    height: 36px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
    border: none !important;
    border-radius: 6px !important;
    padding: 8px 16px !important;
    font-weight: 600 !important;
    font-size: 13px !important;
    color: white !important;
    margin: 2px !important;
    min-width: 100px !important;
    height: 36px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* تحسين شريط الأدوات العلوي */
.page-head .standard-actions {
    background: rgba(255, 255, 255, 0.95) !important;
    border-radius: 8px !important;
    padding: 8px 12px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    margin: 5px 0 !important;
}

/* تحسين الأزرار في القوائم المنسدلة */
.dropdown-menu .btn {
    width: 100% !important;
    text-align: right !important;
    margin: 1px 0 !important;
    border-radius: 4px !important;
}

/* تنسيق معلومات العميل */
.customer-info-box {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
    box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
}

.customer-info-box h5 {
    color: white;
    margin-bottom: 15px;
    text-align: center;
    font-weight: bold;
}

.customer-info-box p {
    margin-bottom: 8px;
    font-size: 14px;
}

/* تنسيق المعلومات السريعة */
.quick-info-section {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    color: #212529;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
}

.quick-info-section h6 {
    color: #212529;
    margin-bottom: 15px;
    text-align: center;
    font-weight: bold;
    font-size: 16px;
}

.quick-info-section .row div {
    text-align: center;
    padding: 10px;
}

/* تنسيق المؤشرات */
.indicator {
    border-radius: 20px;
    padding: 5px 15px;
    font-weight: bold;
    font-size: 12px;
    text-transform: uppercase;
}

.indicator.green {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.indicator.blue {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
}

.indicator.orange {
    background: linear-gradient(135deg, #fd7e14 0%, #e55a00 100%);
    color: white;
}

/* تحسين عرض التبويبات */
.form-tabs .nav-link {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px 8px 0 0;
    margin-right: 5px;
    font-weight: bold;
    color: #495057;
    transition: all 0.3s ease;
}

.form-tabs .nav-link.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: #667eea;
}

.form-tabs .nav-link:hover {
    background: #e9ecef;
    transform: translateY(-2px);
}

/* تحسين عرض الحقول المطلوبة */
.has-error .form-control {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.reqd {
    color: #dc3545 !important;
    font-weight: bold;
}

/* تنسيق الرسائل - الكود الأصلي */
/*
.msgprint {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}
*/

/* تنسيق الرسائل والنوافذ المنبثقة المحسن - الكود الجديد */
/* تقليل حجم النوافذ المنبثقة */
.modal-dialog {
    max-width: 600px !important;
    width: 90% !important;
    margin: 30px auto !important;
}

.modal-dialog.modal-lg {
    max-width: 800px !important;
    width: 95% !important;
}

.modal-dialog.modal-xl {
    max-width: 1000px !important;
    width: 98% !important;
}

.modal-dialog.modal-sm {
    max-width: 400px !important;
    width: 80% !important;
}

/* تحسين النوافذ المنبثقة للمعاينة */
.modal-dialog.print-preview-modal {
    max-width: 700px !important;
    width: 90% !important;
    margin: 20px auto !important;
}

/* تحسين نافذة المعاينة */
.print-preview-wrapper {
    max-height: 70vh !important;
    overflow-y: auto !important;
    padding: 15px !important;
}

/* تحسين نوافذ الرسائل */
.msgprint {
    border-radius: 8px !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
    max-width: 500px !important;
    margin: 20px auto !important;
    padding: 20px !important;
}

/* تحسين نوافذ التأكيد */
.modal-content {
    border-radius: 12px !important;
    border: none !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2) !important;
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    border-bottom: none !important;
    border-radius: 12px 12px 0 0 !important;
    padding: 15px 20px !important;
}

.modal-header .modal-title {
    font-weight: 700 !important;
    font-size: 16px !important;
}

.modal-header .close {
    color: white !important;
    opacity: 0.8 !important;
    font-size: 24px !important;
}

.modal-header .close:hover {
    opacity: 1 !important;
}

.modal-body {
    padding: 20px !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
}

.modal-footer {
    border-top: 1px solid #e9ecef !important;
    padding: 15px 20px !important;
    display: flex !important;
    justify-content: flex-end !important;
    gap: 10px !important;
}

.modal-footer .btn {
    min-width: 80px !important;
    height: 36px !important;
    font-size: 13px !important;
    font-weight: 600 !important;
}

/* تحسين نوافذ الاختيار */
.frappe-control .link-field .awesomplete {
    max-width: 100% !important;
}

.frappe-control .link-field .awesomplete ul {
    max-height: 200px !important;
    overflow-y: auto !important;
    border-radius: 6px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* تحسين نوافذ التقارير */
.report-wrapper .modal-dialog {
    max-width: 900px !important;
    width: 95% !important;
}

/* تحسين نوافذ الطباعة */
.print-format-container {
    max-height: 60vh !important;
    overflow-y: auto !important;
    border: 1px solid #e9ecef !important;
    border-radius: 6px !important;
    padding: 15px !important;
    background: white !important;
}

/* تحسين نوافذ البحث */
.search-dialog .modal-dialog {
    max-width: 500px !important;
    width: 85% !important;
}

/* تحسين نوافذ التحديد المتعدد */
.multi-select-dialog .modal-dialog {
    max-width: 600px !important;
    width: 90% !important;
}

/* تحسين للشاشات الصغيرة */
@media (max-width: 768px) {
    .modal-dialog {
        max-width: 95% !important;
        width: 95% !important;
        margin: 10px auto !important;
    }

    .modal-dialog.modal-lg,
    .modal-dialog.modal-xl {
        max-width: 98% !important;
        width: 98% !important;
    }

    .msgprint {
        max-width: 90% !important;
        margin: 10px auto !important;
        padding: 15px !important;
    }

    .print-preview-wrapper {
        max-height: 50vh !important;
        padding: 10px !important;
    }
}

.alert {
    border-radius: 8px;
    border: none;
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f1b0b7 100%);
    color: #721c24;
}

/* تحسين عرض الشاشات الصغيرة - الكود الأصلي */
/*
@media (max-width: 768px) {
    .form-layout .form-page {
        margin: 5px;
        padding: 15px;
    }

    .form-section {
        padding: 15px;
    }

    .customer-info-box,
    .quick-info-section {
        padding: 15px;
    }

    .grid-wrapper {
        overflow-x: auto;
    }

    .form-control[data-fieldname="grand_total"] {
        font-size: 16px;
        padding: 10px;
    }
}
*/

/* تحسين عرض الشاشات الصغيرة المحسن - الكود الجديد */
@media (max-width: 768px) {
    .form-layout .form-page {
        margin: 5px !important;
        padding: 15px !important;
    }

    .form-section {
        padding: 15px !important;
    }

    .customer-info-box,
    .quick-info-section {
        padding: 15px !important;
    }

    .grid-wrapper {
        overflow-x: auto !important;
    }

    .form-control[data-fieldname="grand_total"] {
        font-size: 16px !important;
        padding: 10px !important;
    }

    /* تحسين الأزرار للشاشات الصغيرة */
    .form-footer,
    .form-actions,
    .page-actions,
    .btn-group,
    .form-page .page-head .standard-actions {
        flex-direction: row !important;
        flex-wrap: wrap !important;
        gap: 5px !important;
        padding: 5px !important;
    }

    .btn-primary,
    .btn-success,
    .btn-secondary,
    .btn-default,
    .btn-warning,
    .btn-info {
        min-width: 80px !important;
        height: 32px !important;
        font-size: 12px !important;
        padding: 6px 12px !important;
        margin: 1px !important;
    }

    /* تحسين شريط الأدوات للشاشات الصغيرة */
    .page-head .standard-actions {
        padding: 5px 8px !important;
        margin: 3px 0 !important;
    }

    /* تحسين القوائم المنسدلة للشاشات الصغيرة */
    .dropdown-menu {
        min-width: 150px !important;
        max-width: 250px !important;
    }

    .dropdown-menu .btn {
        font-size: 12px !important;
        padding: 6px 10px !important;
        height: 30px !important;
    }
}

/* تحسين الطباعة */
@media print {
    .form-section {
        box-shadow: none;
        border: 1px solid #ccc;
    }

    .customer-info-box,
    .quick-info-section {
        background: white !important;
        color: black !important;
        border: 1px solid #ccc;
    }

    .btn {
        display: none;
    }
}

/* تنسيق خاص للغة العربية */
[dir="rtl"] .form-control {
    text-align: right;
}

[dir="rtl"] .grid-static-col {
    text-align: right;
}

[dir="rtl"] .customer-info-box,
[dir="rtl"] .quick-info-section {
    text-align: right;
}

/* تحسينات إضافية للنوافذ المنبثقة المتخصصة */
/* نافذة معاينة الطباعة */
.print-preview-modal .modal-dialog {
    max-width: 650px !important;
    width: 85% !important;
}

/* نافذة اختيار تنسيق الطباعة */
.print-format-modal .modal-dialog {
    max-width: 500px !important;
    width: 80% !important;
}

/* نافذة إرسال البريد الإلكتروني */
.email-modal .modal-dialog {
    max-width: 600px !important;
    width: 85% !important;
}

/* نافذة المرفقات */
.attachment-modal .modal-dialog {
    max-width: 550px !important;
    width: 80% !important;
}

/* نافذة التعليقات */
.comment-modal .modal-dialog {
    max-width: 500px !important;
    width: 75% !important;
}

/* نافذة السجل */
.timeline-modal .modal-dialog {
    max-width: 700px !important;
    width: 90% !important;
}

/* نافذة الاتصالات */
.communication-modal .modal-dialog {
    max-width: 650px !important;
    width: 85% !important;
}

/* نافذة المشاركة */
.share-modal .modal-dialog {
    max-width: 450px !important;
    width: 75% !important;
}

/* نافذة التصدير */
.export-modal .modal-dialog {
    max-width: 500px !important;
    width: 80% !important;
}

/* نافذة الاستيراد */
.import-modal .modal-dialog {
    max-width: 600px !important;
    width: 85% !important;
}

/* تحسين نوافذ التحديد السريع */
.quick-entry-modal .modal-dialog {
    max-width: 550px !important;
    width: 80% !important;
}

/* تحسين نوافذ البحث المتقدم */
.advanced-search-modal .modal-dialog {
    max-width: 600px !important;
    width: 85% !important;
}

/* تحسين نوافذ المرشحات */
.filter-modal .modal-dialog {
    max-width: 500px !important;
    width: 80% !important;
}

/* تحسين نوافذ الإعدادات */
.settings-modal .modal-dialog {
    max-width: 650px !important;
    width: 85% !important;
}

/* تحسين عام لجميع النوافذ المنبثقة */
.modal {
    padding-right: 0 !important;
}

.modal-backdrop {
    background-color: rgba(0, 0, 0, 0.4) !important;
}

/* تحسين أزرار النوافذ المنبثقة */
.modal .btn {
    border-radius: 5px !important;
    font-weight: 600 !important;
    transition: all 0.2s ease !important;
}

.modal .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border: none !important;
}

.modal .btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%) !important;
    border: none !important;
    color: white !important;
}

.modal .btn:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2) !important;
}

/* تأثيرات الحركة */
.form-section {
    animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.grid-row {
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* تحسين عرض الأيقونات */
.form-section .section-head::before {
    content: "📋";
    margin-right: 10px;
    font-size: 18px;
}

.customer-section::before {
    content: "👤";
    font-size: 24px;
    margin-right: 15px;
}

.totals-section::before {
    content: "💰";
    font-size: 24px;
    margin-right: 15px;
}


