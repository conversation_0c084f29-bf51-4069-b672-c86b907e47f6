/* ثيم Cairo المبسط - نسخة محسّنة - الكود الأصلي */
/*
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');

.icon-preload {
    display: none;
}

body::before {
    content: "";
    background-image: url('/assets/frappe/icons/timeless/icons.svg');
    position: absolute;
    width: 0;
    height: 0;
    opacity: 0;
    pointer-events: none;
}

body::after {
    content: "";
    background: url('/website_script.js') no-repeat;
    position: absolute;
    width: 0;
    height: 0;
    opacity: 0;
    pointer-events: none;
}

@font-face {
    font-family: 'Cairo-Preload';
    src: url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');
    font-display: swap;
}

.preload-optimizer {
    display: none !important;
    visibility: hidden !important;
    position: absolute !important;
    top: -9999px !important;
    left: -9999px !important;
}

body, p, h1, h2, h3, h4, h5, h6 {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    margin-bottom: 0.6rem;
}

a {
    text-decoration: none;
    color: #0066cc;
}

a:hover {
    text-decoration: underline;
}
*/

/* ثيم Cairo المبسط - نسخة محسّنة - الكود الجديد المحسن */

/* تحميل الخط مع تحسين الأداء */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');

/* متغيرات الألوان الأساسية */
:root {
    --blue-50: #eff6ff;
    --blue-100: #dbeafe;
    --blue-200: #bfdbfe;
    --blue-300: #93c5fd;
    --blue-400: #60a5fa;
    --blue-500: #3b82f6;
    --blue-600: #2563eb;
    --blue-700: #1d4ed8;
    --blue-800: #1e40af;
    --blue-900: #1e3a8a;
    --violet-100: #ede9fe;
    --bg-purple: #f3e8ff;

    /* ألوان إضافية للتحسين */
    --border-light: #e5e7eb;
    --text-muted: #6b7280;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* أيقونات preload محسنة */
.icon-preload {
    display: none !important;
}

/* استخدام فوري للموارد المحملة مسبقاً */
body::before {
    content: "";
    background-image: url('/assets/frappe/icons/timeless/icons.svg');
    position: absolute;
    width: 0;
    height: 0;
    opacity: 0;
    pointer-events: none;
}

body::after {
    content: "";
    background: url('/website_script.js') no-repeat;
    position: absolute;
    width: 0;
    height: 0;
    opacity: 0;
    pointer-events: none;
}

/* تحسين تحميل الخط */
@font-face {
    font-family: 'Cairo-Preload';
    src: url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');
    font-display: swap;
}

.preload-optimizer {
    display: none !important;
    visibility: hidden !important;
    position: absolute !important;
    top: -9999px !important;
    left: -9999px !important;
}

/* تنسيق النصوص والروابط المحسن */
body, p, h1, h2, h3, h4, h5, h6 {
    font-family: 'Cairo', sans-serif !important;
    line-height: 1.6 !important;
    margin-bottom: 0.6rem !important;
    font-weight: 600 !important;
}

/* تحسين الروابط العامة */
a {
    text-decoration: none !important;
    color: var(--blue-600) !important;
    font-family: 'Cairo', sans-serif !important;
    transition: all 0.2s ease !important;
}

a:hover {
    text-decoration: none !important;
    color: var(--blue-800) !important;
}

/* تحسينات الأزرار - ترتيب في صف واحد */
.form-footer,
.form-actions,
.page-actions,
.btn-group,
.form-page .page-head .standard-actions,
.page-head .standard-actions {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 8px !important;
    align-items: center !important;
    justify-content: flex-start !important;
    margin: 10px 0 !important;
}

/* تنسيق الأزرار الفردية */
.btn-primary {
    background: linear-gradient(135deg, var(--blue-700), var(--blue-800)) !important;
    border: none !important;
    border-radius: 6px !important;
    padding: 8px 16px !important;
    font-weight: 600 !important;
    font-size: 13px !important;
    transition: all 0.3s ease !important;
    margin: 2px !important;
    min-width: 100px !important;
    height: 36px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: white !important;
    font-family: 'Cairo', sans-serif !important;
    box-shadow: var(--shadow-sm) !important;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--blue-800), var(--blue-900)) !important;
    transform: translateY(-1px) !important;
    box-shadow: var(--shadow-md) !important;
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
    border: none !important;
    border-radius: 6px !important;
    padding: 8px 16px !important;
    font-weight: 600 !important;
    font-size: 13px !important;
    margin: 2px !important;
    min-width: 100px !important;
    height: 36px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: white !important;
    font-family: 'Cairo', sans-serif !important;
}

.btn-secondary,
.btn-default {
    background: linear-gradient(135deg, #6c757d, #5a6268) !important;
    border: none !important;
    border-radius: 6px !important;
    padding: 8px 16px !important;
    font-weight: 600 !important;
    font-size: 13px !important;
    color: white !important;
    margin: 2px !important;
    min-width: 100px !important;
    height: 36px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-family: 'Cairo', sans-serif !important;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107, #e0a800) !important;
    border: none !important;
    border-radius: 6px !important;
    padding: 8px 16px !important;
    font-weight: 600 !important;
    font-size: 13px !important;
    color: #212529 !important;
    margin: 2px !important;
    min-width: 100px !important;
    height: 36px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-family: 'Cairo', sans-serif !important;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8, #138496) !important;
    border: none !important;
    border-radius: 6px !important;
    padding: 8px 16px !important;
    font-weight: 600 !important;
    font-size: 13px !important;
    color: white !important;
    margin: 2px !important;
    min-width: 100px !important;
    height: 36px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-family: 'Cairo', sans-serif !important;
}

/* تحسين شريط الأدوات العلوي */
.page-head .standard-actions {
    background: rgba(255, 255, 255, 0.95) !important;
    border-radius: 8px !important;
    padding: 8px 12px !important;
    box-shadow: var(--shadow-sm) !important;
    margin: 5px 0 !important;
}

/* تحسينات النوافذ المنبثقة - تقليل الحجم */
.modal-dialog {
    max-width: 600px !important;
    width: 90% !important;
    margin: 30px auto !important;
}

.modal-dialog.modal-lg {
    max-width: 800px !important;
    width: 95% !important;
}

.modal-dialog.modal-xl {
    max-width: 1000px !important;
    width: 98% !important;
}

.modal-dialog.modal-sm {
    max-width: 400px !important;
    width: 80% !important;
}

/* تحسين النوافذ المنبثقة للمعاينة */
.modal-dialog.print-preview-modal {
    max-width: 700px !important;
    width: 90% !important;
    margin: 20px auto !important;
}

/* تحسين نافذة المعاينة */
.print-preview-wrapper {
    max-height: 70vh !important;
    overflow-y: auto !important;
    padding: 15px !important;
}

/* تحسين نوافذ الرسائل */
.msgprint {
    border-radius: 8px !important;
    box-shadow: var(--shadow-lg) !important;
    max-width: 500px !important;
    margin: 20px auto !important;
    padding: 20px !important;
}

/* تحسين نوافذ التأكيد */
.modal-content {
    border-radius: 12px !important;
    border: none !important;
    box-shadow: var(--shadow-lg) !important;
}

.modal-header {
    background: linear-gradient(135deg, var(--blue-600), var(--blue-700)) !important;
    color: white !important;
    border-bottom: none !important;
    border-radius: 12px 12px 0 0 !important;
    padding: 15px 20px !important;
    font-family: 'Cairo', sans-serif !important;
    font-weight: 700 !important;
}

.modal-header .modal-title {
    font-weight: 700 !important;
    font-size: 16px !important;
    font-family: 'Cairo', sans-serif !important;
}

.modal-header .close {
    color: white !important;
    opacity: 0.8 !important;
    font-size: 24px !important;
}

.modal-header .close:hover {
    opacity: 1 !important;
}

.modal-body {
    padding: 20px !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
    font-family: 'Cairo', sans-serif !important;
}

.modal-footer {
    border-top: 1px solid var(--border-light) !important;
    padding: 15px 20px !important;
    display: flex !important;
    justify-content: flex-end !important;
    gap: 10px !important;
}

.modal-footer .btn {
    min-width: 80px !important;
    height: 36px !important;
    font-size: 13px !important;
    font-weight: 600 !important;
    font-family: 'Cairo', sans-serif !important;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    /* تحسين الأزرار للشاشات الصغيرة */
    .form-footer,
    .form-actions,
    .page-actions,
    .btn-group,
    .form-page .page-head .standard-actions,
    .page-head .standard-actions {
        flex-direction: row !important;
        flex-wrap: wrap !important;
        gap: 5px !important;
        padding: 5px !important;
    }

    .btn-primary,
    .btn-success,
    .btn-secondary,
    .btn-default,
    .btn-warning,
    .btn-info {
        min-width: 80px !important;
        height: 32px !important;
        font-size: 12px !important;
        padding: 6px 12px !important;
        margin: 1px !important;
    }

    /* تحسين شريط الأدوات للشاشات الصغيرة */
    .page-head .standard-actions {
        padding: 5px 8px !important;
        margin: 3px 0 !important;
    }

    /* تحسين النوافذ المنبثقة للشاشات الصغيرة */
    .modal-dialog {
        max-width: 95% !important;
        width: 95% !important;
        margin: 10px auto !important;
    }

    .modal-dialog.modal-lg,
    .modal-dialog.modal-xl {
        max-width: 98% !important;
        width: 98% !important;
    }

    .msgprint {
        max-width: 90% !important;
        margin: 10px auto !important;
        padding: 15px !important;
    }

    .print-preview-wrapper {
        max-height: 50vh !important;
        padding: 10px !important;
    }

    /* تحسين القوائم المنسدلة للشاشات الصغيرة */
    .dropdown-menu {
        min-width: 150px !important;
        max-width: 250px !important;
    }

    .dropdown-menu .btn {
        font-size: 12px !important;
        padding: 6px 10px !important;
        height: 30px !important;
    }

    /* تحسين النصوص للشاشات الصغيرة */
    body, p, h1, h2, h3, h4, h5, h6 {
        font-size: 14px !important;
        line-height: 1.4 !important;
        margin-bottom: 0.4rem !important;
    }

    h1 { font-size: 20px !important; }
    h2 { font-size: 18px !important; }
    h3 { font-size: 16px !important; }
    h4 { font-size: 15px !important; }
    h5 { font-size: 14px !important; }
    h6 { font-size: 13px !important; }
}

/* تحسينات إضافية للتجربة العامة */
/* تحسين الجداول */
.table {
    font-family: 'Cairo', sans-serif !important;
}

.table thead th {
    background: linear-gradient(135deg, var(--blue-100), var(--blue-200)) !important;
    color: var(--blue-900) !important;
    font-weight: 700 !important;
    border: none !important;
    padding: 12px 8px !important;
}

.table tbody tr:hover {
    background: var(--blue-50) !important;
    transition: all 0.2s ease !important;
}

/* تحسين النماذج */
.form-control {
    border: 2px solid var(--blue-200) !important;
    border-radius: 6px !important;
    font-family: 'Cairo', sans-serif !important;
    transition: all 0.2s ease !important;
}

.form-control:focus {
    border-color: var(--blue-600) !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

/* تحسين البطاقات */
.card {
    border: 1px solid var(--blue-200) !important;
    border-radius: 10px !important;
    box-shadow: var(--shadow-sm) !important;
    transition: all 0.2s ease !important;
}

.card:hover {
    border-color: var(--blue-300) !important;
    box-shadow: var(--shadow-md) !important;
    transform: translateY(-1px) !important;
}

.card-header {
    background: linear-gradient(135deg, var(--blue-400), var(--blue-500)) !important;
    color: white !important;
    font-family: 'Cairo', sans-serif !important;
    font-weight: 700 !important;
    border-bottom: none !important;
}
