/* تنسيق زر "جديد" العام - Universal New Button Styles */

/* الزر الرئيسي */
.universal-new-btn {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
    border: none !important;
    color: white !important;
    font-family: 'Cairo', sans-serif !important;
    font-weight: 700 !important;
    padding: 8px 16px !important;
    border-radius: 6px !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3) !important;
    position: relative !important;
    overflow: hidden !important;
}

.universal-new-btn:hover {
    background: linear-gradient(135deg, #218838, #1ea080) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4) !important;
    color: white !important;
}

.universal-new-btn:active {
    transform: translateY(0) !important;
    box-shadow: 0 2px 6px rgba(40, 167, 69, 0.3) !important;
}

/* تأثير الموجة عند النقر */
.universal-new-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.universal-new-btn:active::before {
    width: 300px;
    height: 300px;
}

/* أيقونة الزر */
.universal-new-btn i {
    margin-left: 5px !important;
    font-size: 14px !important;
}

/* القائمة المنسدلة للإنشاء السريع */
.quick-create-dropdown {
    min-width: 250px !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
    border: none !important;
    padding: 8px 0 !important;
}

.quick-create-dropdown .dropdown-item {
    padding: 12px 20px !important;
    font-family: 'Cairo', sans-serif !important;
    font-weight: 600 !important;
    color: #495057 !important;
    border-bottom: 1px solid #f8f9fa !important;
    transition: all 0.2s ease !important;
}

.quick-create-dropdown .dropdown-item:last-child {
    border-bottom: none !important;
}

.quick-create-dropdown .dropdown-item:hover {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
    color: #007bff !important;
    transform: translateX(5px) !important;
}

.quick-create-dropdown .dropdown-item i {
    margin-left: 10px !important;
    width: 20px !important;
    text-align: center !important;
    color: #6c757d !important;
    transition: color 0.2s ease !important;
}

.quick-create-dropdown .dropdown-item:hover i {
    color: #007bff !important;
}

/* زر الإنشاء السريع */
.quick-create-btn {
    background: linear-gradient(135deg, #007bff, #0056b3) !important;
    border: none !important;
    color: white !important;
    font-family: 'Cairo', sans-serif !important;
    font-weight: 600 !important;
    padding: 6px 12px !important;
    border-radius: 5px !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 6px rgba(0, 123, 255, 0.3) !important;
}

.quick-create-btn:hover {
    background: linear-gradient(135deg, #0056b3, #004085) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 3px 8px rgba(0, 123, 255, 0.4) !important;
    color: white !important;
}

/* تحسينات للشاشات الصغيرة - الكود الأصلي */
/*
@media (max-width: 768px) {
    .universal-new-btn {
        padding: 6px 12px !important;
        font-size: 12px !important;
    }

    .quick-create-dropdown {
        min-width: 200px !important;
    }

    .quick-create-dropdown .dropdown-item {
        padding: 10px 15px !important;
        font-size: 13px !important;
    }
}
*/

/* تحسينات للشاشات الصغيرة المحسنة - الكود الجديد */
@media (max-width: 768px) {
    .universal-new-btn {
        padding: 6px 12px !important;
        font-size: 12px !important;
        min-width: 80px !important;
        height: 32px !important;
    }

    .quick-create-dropdown {
        min-width: 200px !important;
        max-width: 280px !important;
    }

    .quick-create-dropdown .dropdown-item {
        padding: 10px 15px !important;
        font-size: 13px !important;
    }
}

/* تحسينات النوافذ المنبثقة لزر "جديد" العام */
/* تحسين نوافذ الإنشاء السريع */
.quick-create-modal .modal-dialog {
    max-width: 500px !important;
    width: 85% !important;
    margin: 30px auto !important;
}

/* تحسين نوافذ اختيار النوع */
.doctype-selector-modal .modal-dialog {
    max-width: 450px !important;
    width: 80% !important;
}

/* تحسين نوافذ النماذج الجديدة */
.new-form-modal .modal-dialog {
    max-width: 600px !important;
    width: 90% !important;
}

/* تحسين عام للنوافذ المنبثقة المرتبطة بالزر */
.universal-new-btn + .modal .modal-content {
    border-radius: 10px !important;
    border: none !important;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15) !important;
}

.universal-new-btn + .modal .modal-header {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
    color: white !important;
    border-bottom: none !important;
    border-radius: 10px 10px 0 0 !important;
    padding: 15px 20px !important;
}

.universal-new-btn + .modal .modal-header .modal-title {
    font-weight: 700 !important;
    font-size: 16px !important;
}

.universal-new-btn + .modal .modal-header .close {
    color: white !important;
    opacity: 0.8 !important;
    font-size: 24px !important;
}

.universal-new-btn + .modal .modal-header .close:hover {
    opacity: 1 !important;
}

.universal-new-btn + .modal .modal-body {
    padding: 20px !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
}

.universal-new-btn + .modal .modal-footer {
    border-top: 1px solid #e9ecef !important;
    padding: 15px 20px !important;
    display: flex !important;
    justify-content: flex-end !important;
    gap: 10px !important;
}

.universal-new-btn + .modal .modal-footer .btn {
    min-width: 80px !important;
    height: 36px !important;
    font-size: 13px !important;
    font-weight: 600 !important;
    border-radius: 5px !important;
}

/* تنسيق مجموعة الأزرار */
.btn-group .universal-new-btn {
    margin-left: 5px !important;
}

/* تنسيق الزر في شريط الأدوات */
.form-toolbar .universal-new-btn {
    margin-left: 10px !important;
    margin-right: 5px !important;
}

/* تأثيرات الحركة */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

.universal-new-btn.pulse {
    animation: pulse 2s infinite;
}

/* تنسيق النص داخل الزر */
.universal-new-btn .btn-text {
    position: relative;
    z-index: 1;
}

/* تحسين التباعد */
.universal-new-btn + .universal-new-btn {
    margin-left: 8px !important;
}

/* تنسيق الأيقونات */
.universal-new-btn .fa {
    margin-left: 6px !important;
    font-size: 13px !important;
}

/* تنسيق خاص للزر في النماذج */
.form-page .universal-new-btn {
    position: relative !important;
    z-index: 10 !important;
}

/* تحسين الظهور */
.universal-new-btn {
    opacity: 0;
    animation: fadeInUp 0.5s ease forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تنسيق الحالة النشطة */
.universal-new-btn.active {
    background: linear-gradient(135deg, #218838, #1e7e34) !important;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

/* تنسيق الحالة المعطلة */
.universal-new-btn:disabled {
    background: #6c757d !important;
    cursor: not-allowed !important;
    opacity: 0.6 !important;
}

.universal-new-btn:disabled:hover {
    transform: none !important;
    box-shadow: none !important;
}
