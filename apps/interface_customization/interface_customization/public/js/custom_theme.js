/**
 * ثيم مخصص جذاب - تحسينات JavaScript
 * Custom Attractive Theme - JavaScript Enhancements
 */

console.log('🎨 تحميل الثيم المخصص الجذاب...');

// تطبيق الثيم فوراً بدون انتظار jQuery
(function() {
    const style = document.createElement('style');
    style.id = 'cairo-theme-immediate';
    style.innerHTML = `
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');

        :root {
            --blue-50: #eff6ff; --blue-100: #dbeafe; --blue-200: #bfdbfe;
            --blue-300: #93c5fd; --blue-400: #60a5fa; --blue-500: #3b82f6;
            --blue-600: #2563eb; --blue-700: #1d4ed8; --blue-800: #1e40af;
            --blue-900: #1e3a8a; --violet-100: #ede9fe; --bg-purple: #f3e8ff;
        }

        body, .sidebar-item-label, .h4, .widget-title, .page-title, .ellipsis,
        .control-label, button, h3, h4, h2, .section-head, .form-control, .btn, .nav-link {
            font-family: "Cairo", sans-serif !important;
            font-weight: 700 !important;
        }

        .navbar { background: var(--blue-400) !important; height: 45px !important; }
        button.btn-primary, .btn-login { background: var(--blue-700) !important; }
        .list-row-head { background: var(--blue-300) !important; }
        .desk-sidebar-item { background: var(--blue-50) !important; border: 2px solid var(--blue-300) !important; }
    `;
    document.head.appendChild(style);
    console.log('⚡ تم تطبيق الثيم الأساسي فوراً');
})();

// إعدادات الثيم
const THEME_CONFIG = {
    // تفعيل التأثيرات
    ENABLE_ANIMATIONS: true,
    ENABLE_PARTICLES: false, // يمكن تفعيلها لاحقاً
    ENABLE_SMOOTH_SCROLL: true,

    // ألوان ديناميكية
    DYNAMIC_COLORS: true,

    // تحسينات الأداء
    OPTIMIZE_PERFORMANCE: true
};

// تطبيق التحسينات عند تحميل الصفحة
$(document).ready(function() {
    console.log('🚀 تطبيق تحسينات الثيم...');
    console.log('📍 الوقت الحالي:', new Date().toLocaleString());

    // إضافة CSS الثيم مباشرة
    addThemeCSS();

    // تطبيق التحسينات الأساسية
    applyThemeEnhancements();

    // إضافة تأثيرات الحركة
    if (THEME_CONFIG.ENABLE_ANIMATIONS) {
        addAnimationEffects();
    }

    // تحسين التمرير
    if (THEME_CONFIG.ENABLE_SMOOTH_SCROLL) {
        enableSmoothScrolling();
    }

    // مراقبة التغييرات في الصفحة
    observePageChanges();

    // إضافة إشعار مرئي
    showThemeNotification();

    console.log('✅ تم تطبيق الثيم المخصص بنجاح');
    console.log('🎨 ثيم Cairo نشط الآن!');
});

// إضافة إشعار مرئي لتأكيد تطبيق الثيم
function showThemeNotification() {
    const notification = $(`
        <div id="theme-notification" style="
            position: fixed;
            top: 20px;
            right: 20px;
            background: #1d4ed8;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            font-family: 'Cairo', sans-serif;
            font-weight: 700;
            z-index: 9999;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            animation: slideIn 0.5s ease-out;
        ">
            🎨 تم تطبيق ثيم Cairo بنجاح!
        </div>
        <style>
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        </style>
    `);

    $('body').append(notification);

    // إخفاء الإشعار بعد 5 ثوان
    setTimeout(() => {
        $('#theme-notification').fadeOut(500, function() {
            $(this).remove();
        });
    }, 5000);
}

// إضافة CSS الثيم مباشرة
function addThemeCSS() {
    const themeCSS = `
        <style id="cairo-theme">
            /* ===== تحميل خط Cairo ===== */
            @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');

            /* ===== متغيرات الألوان الأساسية ===== */
            :root {
                --blue-50: #eff6ff;
                --blue-100: #dbeafe;
                --blue-200: #bfdbfe;
                --blue-300: #93c5fd;
                --blue-400: #60a5fa;
                --blue-500: #3b82f6;
                --blue-600: #2563eb;
                --blue-700: #1d4ed8;
                --blue-800: #1e40af;
                --blue-900: #1e3a8a;
                --violet-100: #ede9fe;
                --bg-purple: #f3e8ff;
                --bg-color: #ffffff;
                --text-muted: #6b7280;
                --text-color: #1f2937;
            }

            /* ===== تطبيق خط Cairo على العناصر المحددة ===== */
            .sidebar-item-label,
            .h4,
            .widget-title,
            .page-title,
            .ellipsis,
            .control-label,
            button,
            h3, h4, h2,
            .section-head,
            body, .form-control, .btn, .nav-link {
                font-family: "Cairo", sans-serif !important;
                font-optical-sizing: auto !important;
                font-weight: 700 !important;
                font-style: normal !important;
            }

            /* ===== الأزرار الأساسية ===== */
            button.btn-primary, .btn-login {
                background: var(--blue-700) !important;
            }

            /* ===== شريط التنقل ===== */
            #navbar-breadcrumbs a {
                color: var(--blue-900) !important;
                font-family: Cairo !important;
                font-size: small !important;
            }

            /* ===== تنسيق النصوص ===== */
            span.h4 {
                color: #333 !important;
            }

            span.ellipsis, span.sidebar-item-label {
                color: #5f5f5f !important;
            }

            h3.title-text {
                color: var(--blue-900) !important;
                font-size: small !important;
            }

            .control-label, span.ellipsis, span.sidebar-item-label, button.btn-primary span,
            .list-row-head, span.dropdown-text, span.button-label, .dropdown-item {
                font-size: smaller !important;
            }

            /* ===== حقول الإدخال ===== */
            .control-input {
                border: #eaf2ff 2px solid !important;
                border-radius: 2px !important;
                background: var(--blue-900) !important;
            }

            .title-area .indicator-pill {
                font-size: x-small !important;
            }

            /* ===== الشريط العلوي ===== */
            .navbar {
                background: var(--blue-400) !important;
                height: 45px !important;
            }

            .navbar-home img {
                max-height: 40px !important;
            }

            /* ===== صفحة تسجيل الدخول ===== */
            #page-login {
                background: var(--violet-100) !important;
            }

            /* ===== رؤوس الجداول ===== */
            .list-row-head {
                background: var(--blue-300) !important;
                text-align: center !important;
                line-height: 30px !important;
                font-size: smaller !important;
                padding: 0 !important;
            }

            /* ===== محتوى الصفحة ===== */
            .page-content {
                padding: 5px 0 !important;
            }

            /* ===== صفحة مساحات العمل ===== */
            #page-Workspaces {
                background: var(--blue-700) !important;
            }

            /* ===== رأس الصفحة ===== */
            .page-head {
                margin-bottom: 0 !important;
                height: 40px !important;
                top: 45px !important;
            }

            .page-head .page-head-content {
                height: 40px !important;
                padding: 6px 0 !important;
            }

            /* ===== القسم الرئيسي ===== */
            .layout-main-section-wrapper {
                padding: 0 5px !important;
                margin-left: 15px !important;
            }

            /* ===== الروابط والاختصارات ===== */
            .link-text, .shortcut-widget-box {
                border: 2px solid var(--blue-300) !important;
                background: var(--blue-50) !important;
                padding: 5px 7px !important;
                min-width: 60% !important;
                display: inline-block !important;
                color: var(--blue-800) !important;
                border-radius: 5px !important;
            }

            .shortcut-widget-box {
                background: var(--bg-purple) !important;
            }

            .link-text:hover {
                border: 2px solid var(--blue-400) !important;
            }

            /* ===== الشريط الجانبي ===== */
            .sidebar-child-item .desk-sidebar-item {
                margin-right: 10px !important;
            }

            .desk-sidebar-item {
                margin: 5px auto !important;
                background: var(--blue-50) !important;
                border: 2px solid var(--blue-300) !important;
            }

            .desk-sidebar-item.selected {
                border: 2px solid var(--blue-700) !important;
            }

            .desk-sidebar-item.selected .item-anchor {
                background: var(--blue-300) !important;
            }

            .item-anchor:hover {
                background: var(--blue-200) !important;
            }

            .standard-sidebar-section {
                padding-right: 10px !important;
            }

            /* ===== القسم الجانبي ===== */
            .layout-side-section {
                padding-left: 5px !important;
                padding-right: 5px !important;
            }

            .list-sidebar {
                padding: 10px !important;
                border-radius: 5px !important;
                background: var(--bg-color) !important;
            }
        </style>
    `;

    // إزالة الثيم القديم إذا كان موجوداً
    $('#cairo-theme').remove();

    // إضافة الثيم الجديد
    $('head').append(themeCSS);

    console.log('🎨 تم إضافة CSS الثيم مباشرة');
}

// دالة تطبيق التحسينات الأساسية
function applyThemeEnhancements() {
    // إضافة class للجسم الرئيسي
    $('body').addClass('custom-theme-active');

    // تحسين الشريط العلوي
    enhanceNavbar();

    // تحسين الشريط الجانبي
    enhanceSidebar();

    // تحسين البطاقات والكروت
    enhanceCards();

    // تحسين الأزرار
    enhanceButtons();

    // تحسين النماذج
    enhanceForms();

    // تحسين الجداول
    enhanceTables();
}

// تحسين الشريط العلوي
function enhanceNavbar() {
    $('.navbar').each(function() {
        $(this).addClass('enhanced-navbar');

        // إضافة تأثير الشفافية عند التمرير
        $(window).scroll(function() {
            const scrollTop = $(window).scrollTop();
            const opacity = Math.max(0.9, 1 - (scrollTop / 200));
            $('.navbar').css('background', `rgba(255, 255, 255, ${opacity})`);
        });
    });
}

// تحسين الشريط الجانبي
function enhanceSidebar() {
    $('.desk-sidebar').each(function() {
        $(this).addClass('enhanced-sidebar');

        // إضافة تأثيرات للعناصر
        $(this).find('.sidebar-item').each(function(index) {
            $(this).css('animation-delay', `${index * 0.1}s`);
            $(this).addClass('sidebar-item-enhanced');
        });
    });
}

// تحسين البطاقات والكروت
function enhanceCards() {
    $('.card, .widget, .layout-main-section').each(function(index) {
        $(this).addClass('enhanced-card');

        // إضافة تأخير للحركة
        $(this).css('animation-delay', `${index * 0.1}s`);

        // إضافة تأثير hover متقدم
        $(this).hover(
            function() {
                $(this).addClass('card-hover-effect');
            },
            function() {
                $(this).removeClass('card-hover-effect');
            }
        );
    });
}

// // تحسين الأزرار
// function enhanceButtons() {
//     $('.btn').each(function() {
//         $(this).addClass('enhanced-btn');

//         // إضافة تأثير الموجة عند النقر
//         $(this).on('click', function(e) {
//             addRippleEffect(this, e);
//         });

//         // إضافة تأثير التحميل
//         $(this).on('click', function() {
//             const $btn = $(this);
//             const originalText = $btn.text();

//             // إذا كان الزر يحتوي على نموذج أو عملية
//             if ($btn.hasClass('btn-primary') || $btn.hasClass('btn-success')) {
//                 $btn.addClass('btn-loading');
//                 $btn.html('<i class="fa fa-spinner fa-spin"></i> جاري المعالجة...');

//                 // إعادة النص الأصلي بعد ثانيتين (يمكن تخصيصه)
//                 setTimeout(() => {
//                     $btn.removeClass('btn-loading');
//                     $btn.html(originalText);
//                 }, 2000);
//             }
//         });
//     });
// }

// // تأثير الموجة للأزرار
// function addRippleEffect(button, event) {
//     const $button = $(button);
//     const rect = button.getBoundingClientRect();
//     const size = Math.max(rect.width, rect.height);
//     const x = event.clientX - rect.left - size / 2;
//     const y = event.clientY - rect.top - size / 2;

//     const $ripple = $('<span class="ripple"></span>').css({
//         width: size,
//         height: size,
//         left: x,
//         top: y
//     });

//     $button.append($ripple);

//     setTimeout(() => {
//         $ripple.remove();
//     }, 600);
// }

// تحسين الأزرار
function enhanceButtons() {
    $('.btn').each(function () {
        const $btn = $(this);
        
        // إضافة فئة عامة للتصميم
        $btn.addClass('enhanced-btn');

        // إزالة الأحداث المكررة إن وُجدت
        $btn.off('click.enhance');

        // تأثير الموجة فقط إذا لم يكن مضاف مسبقًا
        $btn.on('click.enhance', function (e) {
            addRippleEffect(this, e);
        });

        // إضافة تأثير التحميل فقط للأزرار الأساسية
        if ($btn.hasClass('btn-primary') || $btn.hasClass('btn-success')) {
            $btn.on('click.enhance', function () {
                const originalContent = $btn.html();
                
                // منع التكرار إن كان الزر بالفعل في حالة تحميل
                if ($btn.hasClass('btn-loading')) return;

                $btn.addClass('btn-loading');
                $btn.html('<i class="fa fa-spinner fa-spin"></i> جاري المعالجة...');

                setTimeout(() => {
                    $btn.removeClass('btn-loading');
                    $btn.html(originalContent);
                }, 2000);
            });
        }
    });
}

// تأثير الموجة للأزرار
function addRippleEffect(button, event) {
    const $button = $(button);
    const rect = button.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;

    const $ripple = $('<span class="ripple"></span>').css({
        width: size,
        height: size,
        left: x,
        top: y
    });

    $button.append($ripple);

    setTimeout(() => {
        $ripple.remove();
    }, 600);
}

// تحسين النماذج
function enhanceForms() {
    $('.form-control').each(function() {
        $(this).addClass('enhanced-input');

        // إضافة تأثيرات التركيز
        $(this).on('focus', function() {
            $(this).closest('.form-group').addClass('input-focused');
        });

        $(this).on('blur', function() {
            $(this).closest('.form-group').removeClass('input-focused');
        });

        // إضافة تأثير الكتابة
        $(this).on('input', function() {
            if ($(this).val().length > 0) {
                $(this).addClass('has-content');
            } else {
                $(this).removeClass('has-content');
            }
        });
    });
}

// تحسين الجداول
function enhanceTables() {
    $('.table').each(function() {
        $(this).addClass('enhanced-table');

        // إضافة تأثيرات للصفوف
        $(this).find('tbody tr').each(function(index) {
            $(this).css('animation-delay', `${index * 0.05}s`);
            $(this).addClass('table-row-enhanced');
        });

        // إضافة تأثير hover للصفوف
        $(this).find('tbody tr').hover(
            function() {
                $(this).addClass('row-highlight');
            },
            function() {
                $(this).removeClass('row-highlight');
            }
        );
    });
}

// إضافة تأثيرات الحركة
function addAnimationEffects() {
    // إضافة CSS للتأثيرات
    const animationCSS = `
        <style id="theme-animations">
            .enhanced-card {
                animation: fadeInUp 0.6s ease-out forwards;
                opacity: 0;
            }

            .sidebar-item-enhanced {
                animation: slideInRight 0.4s ease-out forwards;
                opacity: 0;
            }

            .table-row-enhanced {
                animation: fadeInUp 0.3s ease-out forwards;
                opacity: 0;
            }

            .card-hover-effect {
                transform: translateY(-8px) scale(1.02) !important;
                box-shadow: 0 12px 40px rgba(30, 136, 229, 0.25) !important;
            }

            .input-focused {
                transform: scale(1.02);
                transition: transform 0.3s ease;
            }

            .row-highlight {
                background: linear-gradient(90deg, rgba(30, 136, 229, 0.1), rgba(30, 136, 229, 0.05)) !important;
                transform: scale(1.01);
            }

            .btn-loading {
                pointer-events: none;
                opacity: 0.7;
            }

            .ripple {
                position: absolute;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.6);
                transform: scale(0);
                animation: ripple-animation 0.6s linear;
                pointer-events: none;
            }

            @keyframes ripple-animation {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }

            .enhanced-btn:hover {
                transform: translateY(-2px) !important;
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
            }

            .enhanced-input:focus {
                transform: scale(1.02);
                box-shadow: 0 0 0 0.2rem rgba(30, 136, 229, 0.25) !important;
            }

            .has-content {
                border-color: #4caf50 !important;
            }
        </style>
    `;

    $('head').append(animationCSS);
}

// تفعيل التمرير السلس
function enableSmoothScrolling() {
    $('html').css('scroll-behavior', 'smooth');

    // تحسين التمرير للروابط الداخلية
    $('a[href^="#"]').on('click', function(e) {
        e.preventDefault();
        const target = $(this.getAttribute('href'));
        if (target.length) {
            $('html, body').animate({
                scrollTop: target.offset().top - 100
            }, 800);
        }
    });
}

// مراقبة التغييرات في الصفحة
function observePageChanges() {
    // مراقب للعناصر الجديدة
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.addedNodes.length > 0) {
                // تطبيق التحسينات على العناصر الجديدة
                setTimeout(() => {
                    applyThemeToNewElements();
                }, 100);
            }
        });
    });

    // بدء المراقبة
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
}

// تطبيق الثيم على العناصر الجديدة
function applyThemeToNewElements() {
    // البطاقات الجديدة
    $('.card:not(.enhanced-card), .widget:not(.enhanced-card)').each(function(index) {
        $(this).addClass('enhanced-card');
        $(this).css('animation-delay', `${index * 0.1}s`);
    });

    // الأزرار الجديدة
    $('.btn:not(.enhanced-btn)').each(function() {
        $(this).addClass('enhanced-btn');
        $(this).on('click', function(e) {
            addRippleEffect(this, e);
        });
    });

    // الحقول الجديدة
    $('.form-control:not(.enhanced-input)').each(function() {
        $(this).addClass('enhanced-input');
        $(this).on('focus blur input', function() {
            // إعادة تطبيق أحداث النماذج
        });
    });
}

// دالة لتغيير لون الثيم ديناميكياً (اختيارية)
function changeThemeColor(newColor) {
    if (THEME_CONFIG.DYNAMIC_COLORS) {
        const root = document.documentElement;
        root.style.setProperty('--primary-blue', newColor);
        root.style.setProperty('--primary-blue-dark', adjustBrightness(newColor, -20));
        root.style.setProperty('--primary-blue-light', adjustBrightness(newColor, 20));

        console.log(`🎨 تم تغيير لون الثيم إلى: ${newColor}`);
    }
}

// دالة مساعدة لتعديل سطوع اللون
function adjustBrightness(color, amount) {
    const usePound = color[0] === '#';
    const col = usePound ? color.slice(1) : color;
    const num = parseInt(col, 16);
    let r = (num >> 16) + amount;
    let g = (num >> 8 & 0x00FF) + amount;
    let b = (num & 0x0000FF) + amount;
    r = r > 255 ? 255 : r < 0 ? 0 : r;
    g = g > 255 ? 255 : g < 0 ? 0 : g;
    b = b > 255 ? 255 : b < 0 ? 0 : b;
    return (usePound ? '#' : '') + (r << 16 | g << 8 | b).toString(16);
}

// تصدير الدوال للاستخدام الخارجي
window.customTheme = {
    changeColor: changeThemeColor,
    applyEnhancements: applyThemeEnhancements,
    config: THEME_CONFIG
};

console.log('✅ تم تحميل الثيم المخصص الجذاب بنجاح');
