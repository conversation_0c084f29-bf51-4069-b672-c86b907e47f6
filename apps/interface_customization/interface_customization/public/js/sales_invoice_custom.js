// تخصيص واجهة فاتورة المبيعات للمستخدمين العرب
// Sales Invoice Interface Customization for Arabic Users

frappe.ui.form.on('Sales Invoice', {
    refresh: function(frm) {
        // إخفاء الحقول غير الضرورية للمستخدمين العاديين
        hide_unnecessary_fields(frm);

        // إعادة ترتيب الحقول حسب الأولوية
        reorganize_fields(frm);

        // تخصيص التسميات للعربية
        customize_labels(frm);

        // إضافة أقسام واضحة
        add_custom_sections(frm);

        // تحسين عرض جدول البنود
        customize_items_table(frm);

        // إضافة معلومات مفيدة
        add_helpful_info(frm);
    },

    onload: function(frm) {
        // تطبيق التخصيصات عند تحميل النموذج
        setup_form_layout(frm);
    },

    customer: function(frm) {
        // عرض معلومات العميل بوضوح
        if (frm.doc.customer) {
            show_customer_info(frm);
        }
    }
});

// إخفاء الحقول غير الضرورية
function hide_unnecessary_fields(frm) {
    const fields_to_hide = [
        'title', 'naming_series', 'tax_id', 'company_tax_id',
        // 'set_posting_time', 'posting_time', 'amended_from',
        // 'is_pos', 'pos_profile', 'is_consolidated', 'is_return',
        // 'return_against', 'update_billed_amount_in_sales_order',
        // 'update_billed_amount_in_delivery_note', 'is_debit_note',
        // 'dimension_col_break', 'conversion_rate', 'price_list_currency',
        // 'plc_conversion_rate', 'ignore_pricing_rule', 'scan_barcode',
        // 'update_stock', 'set_warehouse', 'set_target_warehouse',
        'shipping_rule', 'incoterm', 'named_place', 'tax_category',
        'apply_discount_on', 'is_cash_or_non_trade_discount',
        'additional_discount_account', 'use_company_roundoff_cost_center',
        'disable_rounded_total', 'total_advance', 'outstanding_amount',
        'base_discount_amount', 'base_total', 'base_net_total',
        'base_total_taxes_and_charges', 'base_grand_total',
        'base_rounding_adjustment', 'base_rounded_total', 'base_in_words',
        'rounding_adjustment', 'rounded_total', 'total_net_weight'
    ];

    fields_to_hide.forEach(field => {
        frm.toggle_display(field, false);
    });

    // إخفاء أقسام كاملة غير ضرورية
    frm.toggle_display('currency_and_price_list', false);
    frm.toggle_display('sec_tax_breakup', false);
    frm.toggle_display('pricing_rule_details', false);
    frm.toggle_display('packing_list', false);
    frm.toggle_display('time_sheet_list', false);
}

// إعادة ترتيب الحقول
function reorganize_fields(frm) {
    // نقل الحقول المهمة إلى الأعلى
    const important_fields = ['customer', 'posting_date', 'due_date'];

    // تطبيق ترتيب مخصص للحقول
    setTimeout(() => {
        const form_layout = frm.layout.wrapper;

        // إعادة ترتيب أقسام النموذج
        reorder_sections(form_layout);
    }, 500);
}

// تخصيص التسميات
function customize_labels(frm) {
    const arabic_labels = {
        'customer': 'العميل',
        'customer_name': 'اسم العميل',
        'posting_date': 'تاريخ الفاتورة',
        'due_date': 'تاريخ الاستحقاق',
        'company': 'الشركة',
        'cost_center': 'مركز التكلفة',
        'project': 'المشروع',
        'currency': 'العملة',
        'selling_price_list': 'قائمة الأسعار',
        'total_qty': 'إجمالي الكمية',
        'total': 'المجموع',
        'net_total': 'صافي المجموع',
        'grand_total': 'المجموع الإجمالي',
        'in_words': 'المبلغ بالكلمات',
        'taxes_and_charges': 'قالب الضرائب والرسوم',
        'total_taxes_and_charges': 'إجمالي الضرائب والرسوم',
        'additional_discount_percentage': 'نسبة الخصم الإضافي',
        'discount_amount': 'مبلغ الخصم',
        'contact_person': 'الشخص المسؤول',
        'customer_address': 'عنوان العميل',
        'territory': 'المنطقة'
    };

    Object.keys(arabic_labels).forEach(field => {
        if (frm.fields_dict[field]) {
            frm.fields_dict[field].df.label = arabic_labels[field];
            frm.refresh_field(field);
        }
    });
}

// إضافة أقسام مخصصة
function add_custom_sections(frm) {
    // إضافة قسم معلومات سريعة
    if (!frm.doc.__islocal) {
        add_quick_info_section(frm);
    }
}

// تخصيص جدول البنود
function customize_items_table(frm) {
    // تخصيص عرض جدول البنود
    if (frm.fields_dict.items && frm.fields_dict.items.grid) {
        const grid = frm.fields_dict.items.grid;

        // إخفاء أعمدة غير ضرورية
        const columns_to_hide = [
            'image', 'item_name', 'description', 'item_group',
            'brand', 'stock_uom', 'conversion_factor', 'stock_qty',
            'price_list_rate', 'base_price_list_rate', 'margin_type',
            'margin_rate_or_amount', 'rate_with_margin', 'discount_percentage',
            'discount_amount', 'base_rate', 'base_amount', 'pricing_rules',
            'stock_uom_rate', 'is_free_item', 'grant_commission',
            'net_rate', 'net_amount', 'base_net_rate', 'base_net_amount',
            'delivered_by_supplier', 'income_account', 'expense_account',
            'enable_deferred_revenue', 'deferred_revenue_account',
            'service_start_date', 'service_end_date', 'service_stop_date',
            'item_weight_details', 'weight_per_unit', 'total_weight',
            'weight_uom', 'warehouse', 'target_warehouse', 'quality_inspection',
            'allow_zero_valuation_rate', 'against_blanket_order',
            'blanket_order', 'blanket_order_rate', 'projected_qty',
            'ordered_qty', 'planned_qty', 'work_order',
            'bom_no', 'page_break'
        ];

        setTimeout(() => {
            columns_to_hide.forEach(col => {
                if (grid.docfields.find(df => df.fieldname === col)) {
                    grid.toggle_display(col, false);
                }
            });

            // إعادة ترتيب الأعمدة المهمة
            reorder_item_columns(grid);

            // إضافة معلومات المخزون للبنود
            add_stock_info_to_items(frm);
        }, 1000);
    }
}

// إعادة ترتيب أعمدة البنود
function reorder_item_columns(grid) {
    // الأعمدة المهمة بالترتيب المطلوب
    const important_columns = [
        'item_code',
        'qty',
        'rate',
        'amount',
        'actual_qty',  // الكمية المتبقية في المخزن
        'cost_center', // مركز التكلفة
        'valuation_rate' // معدل التقييم
    ];

    // تطبيق تسميات عربية للأعمدة
    const arabic_column_labels = {
        'item_code': 'رمز الصنف',
        'qty': 'الكمية',
        'rate': 'السعر',
        'amount': 'المبلغ',
        'actual_qty': 'الكمية المتاحة',
        'cost_center': 'مركز التكلفة',
        'valuation_rate': 'معدل التقييم'
    };

    // تطبيق التسميات العربية
    grid.docfields.forEach(df => {
        if (arabic_column_labels[df.fieldname]) {
            df.label = arabic_column_labels[df.fieldname];
        }
    });

    grid.refresh();
}

// إضافة معلومات مفيدة
function add_helpful_info(frm) {
    // إضافة معلومات سريعة في أعلى النموذج
    if (!frm.doc.__islocal && frm.doc.docstatus === 1) {
        frm.dashboard.add_indicator(__('فاتورة مؤكدة'), 'green');
    } else if (frm.doc.__islocal) {
        frm.dashboard.add_indicator(__('فاتورة جديدة'), 'blue');
    } else {
        frm.dashboard.add_indicator(__('مسودة'), 'orange');
    }
}

// عرض معلومات العميل مع السهم القابل للنقر
function show_customer_info(frm) {
    if (frm.doc.customer) {
        // إزالة المعلومات والأزرار السابقة أولاً
        $('.customer-info-collapsible').remove();
        $('.customer-info-toggle').remove();

        // إضافة السهم القابل للنقر
        add_customer_toggle_button(frm);

        frappe.call({
            method: 'interface_customization.custom_interface.sales_invoice_customization.get_customer_summary',
            args: {
                customer: frm.doc.customer
            },
            callback: function(r) {
                if (r.message) {
                    const customer = r.message;
                    let info_html = `
                        <div class="customer-info-collapsible" id="customer-info-${frm.doc.name || 'new'}">
                            <h6>معلومات العميل</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>الاسم:</strong> ${customer.customer_name || ''}</p>
                                    <p><strong>المجموعة:</strong> ${customer.customer_group || ''}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>إجمالي المبيعات:</strong> ${format_currency(customer.total_sales || 0)}</p>
                                    <p><strong>عدد الفواتير:</strong> ${customer.invoice_count || 0}</p>
                                </div>
                            </div>
                        </div>
                    `;

                    // إضافة المعلومات الجديدة
                    $(frm.fields_dict.customer.wrapper).after(info_html);
                }
            }
        });
    } else {
        // إزالة المعلومات والأزرار عند عدم وجود عميل
        $('.customer-info-collapsible').remove();
        $('.customer-info-toggle').remove();
    }
}

// إضافة زر السهم القابل للنقر
function add_customer_toggle_button(frm) {
    // إزالة الزر السابق إن وجد
    $(frm.fields_dict.customer.wrapper).find('.customer-info-toggle').remove();

    // إضافة الزر الجديد
    const toggle_button = `
        <button class="customer-info-toggle" type="button" title="عرض/إخفاء معلومات العميل">
            <i class="fa fa-chevron-down"></i>
        </button>
    `;

    $(frm.fields_dict.customer.wrapper).append(toggle_button);

    // ربط حدث النقر
    $(frm.fields_dict.customer.wrapper).find('.customer-info-toggle').on('click', function() {
        toggle_customer_info(frm, this);
    });
}

// تبديل عرض معلومات العميل
function toggle_customer_info(frm, button) {
    const info_box = $(frm.fields_dict.customer.wrapper).next('.customer-info-collapsible');
    const icon = $(button).find('i');

    if (info_box.hasClass('expanded')) {
        // إخفاء المعلومات
        info_box.removeClass('expanded');
        icon.removeClass('fa-chevron-up').addClass('fa-chevron-down');
        $(button).removeClass('rotated');
    } else {
        // عرض المعلومات
        info_box.addClass('expanded');
        icon.removeClass('fa-chevron-down').addClass('fa-chevron-up');
        $(button).addClass('rotated');
    }
}

// إعداد تخطيط النموذج
function setup_form_layout(frm) {
    // تطبيق تنسيق مخصص للنموذج
    setTimeout(() => {
        // إضافة CSS مخصص
        add_custom_css();

        // تحسين عرض الأقسام
        improve_section_display(frm);
    }, 1000);
}

// إضافة CSS مخصص
function add_custom_css() {
    if (!$('#sales-invoice-custom-css').length) {
        $('head').append(`
            <style id="sales-invoice-custom-css">
                .form-layout .form-page .form-section {
                    margin-bottom: 20px;
                    border: 1px solid #e0e6ed;
                    border-radius: 8px;
                    padding: 15px;
                }

                .form-layout .form-page .form-section .section-head {
                    background: #f8f9fa;
                    margin: -15px -15px 15px -15px;
                    padding: 10px 15px;
                    border-radius: 8px 8px 0 0;
                    font-weight: bold;
                    color: #495057;
                }

                .customer-info-box {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border-radius: 10px;
                    padding: 15px;
                    margin: 10px 0;
                }

                .customer-info-box h5 {
                    color: white;
                    margin-bottom: 10px;
                }

                .grid-row .grid-static-col {
                    font-weight: 500;
                }

                .form-control[data-fieldname="grand_total"] {
                    font-size: 18px;
                    font-weight: bold;
                    background: #e8f5e8;
                }
            </style>
        `);
    }
}

// تحسين عرض الأقسام
function improve_section_display(frm) {
    // إضافة عناوين واضحة للأقسام
    const section_titles = {
        'customer_section': 'بيانات العميل',
        'items_section': 'بنود الفاتورة',
        'taxes_section': 'الضرائب والرسوم',
        'totals': 'المجاميع',
        'contact_and_address_tab': 'العناوين وجهات الاتصال',
        'terms_tab': 'الشروط والأحكام'
    };

    Object.keys(section_titles).forEach(section => {
        const section_wrapper = frm.fields_dict[section];
        if (section_wrapper && section_wrapper.wrapper) {
            const title_element = $(section_wrapper.wrapper).find('.section-head');
            if (title_element.length === 0) {
                $(section_wrapper.wrapper).prepend(`<div class="section-head">${section_titles[section]}</div>`);
            }
        }
    });
}

// إعادة ترتيب الأقسام
function reorder_sections(form_layout) {
    // هذه الدالة يمكن تطويرها لإعادة ترتيب الأقسام حسب الحاجة
    console.log('تم تطبيق إعادة ترتيب الأقسام');
}

// إضافة قسم معلومات سريعة
function add_quick_info_section(frm) {
    const quick_info = `
        <div class="quick-info-section" style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 10px; margin: 10px 0;">
            <h6>معلومات سريعة</h6>
            <div class="row">
                <div class="col-md-4">
                    <strong>رقم الفاتورة:</strong> ${frm.doc.name || ''}
                </div>
                <div class="col-md-4">
                    <strong>الحالة:</strong> ${frm.doc.status || ''}
                </div>
                <div class="col-md-4">
                    <strong>المجموع الإجمالي:</strong> ${format_currency(frm.doc.grand_total || 0, frm.doc.currency)}
                </div>
            </div>
        </div>
    `;

    if (!frm.quick_info_added) {
        $(frm.wrapper).find('.form-layout').prepend(quick_info);
        frm.quick_info_added = true;
    }
}

// إضافة معلومات المخزون للبنود
function add_stock_info_to_items(frm) {
    if (frm.doc.items) {
        frm.doc.items.forEach((item, idx) => {
            if (item.item_code) {
                // الحصول على معلومات المخزون
                frappe.call({
                    method: 'interface_customization.custom_interface.sales_invoice_customization.get_item_stock_info',
                    args: {
                        item_code: item.item_code,
                        warehouse: item.warehouse || frm.doc.set_warehouse
                    },
                    callback: function(r) {
                        if (r.message) {
                            const stock_info = r.message;

                            // تحديث الحقول في الجدول
                            frappe.model.set_value(item.doctype, item.name, 'actual_qty', stock_info.actual_qty);
                            frappe.model.set_value(item.doctype, item.name, 'valuation_rate', stock_info.valuation_rate);

                            // إضافة تحذير إذا كانت الكمية المطلوبة أكبر من المتاحة
                            if (item.qty > stock_info.actual_qty) {
                                show_stock_warning(item, stock_info.actual_qty);
                            }
                        }
                    }
                });
            }
        });
    }
}

// عرض تحذير المخزون
function show_stock_warning(item, available_qty) {
    const warning_msg = `تحذير: الكمية المطلوبة (${item.qty}) أكبر من الكمية المتاحة (${available_qty}) للصنف ${item.item_code}`;

    frappe.show_alert({
        message: warning_msg,
        indicator: 'orange'
    }, 5);
}

// تحديث معلومات المخزون عند تغيير الصنف
frappe.ui.form.on('Sales Invoice Item', {
    item_code: function(frm, cdt, cdn) {
        const item = locals[cdt][cdn];
        if (item.item_code) {
            // الحصول على معلومات المخزون للصنف الجديد
            frappe.call({
                method: 'interface_customization.custom_interface.sales_invoice_customization.get_item_stock_info',
                args: {
                    item_code: item.item_code,
                    warehouse: item.warehouse || frm.doc.set_warehouse
                },
                callback: function(r) {
                    if (r.message) {
                        const stock_info = r.message;

                        // تحديث الحقول
                        frappe.model.set_value(cdt, cdn, 'actual_qty', stock_info.actual_qty);
                        frappe.model.set_value(cdt, cdn, 'valuation_rate', stock_info.valuation_rate);

                        // عرض معلومات الصنف
                        show_item_info(item, stock_info);
                    }
                }
            });
        }
    },

    qty: function(frm, cdt, cdn) {
        const item = locals[cdt][cdn];
        if (item.item_code && item.actual_qty) {
            // التحقق من توفر الكمية
            if (item.qty > item.actual_qty) {
                show_stock_warning(item, item.actual_qty);
            }
        }
    }
});

// عرض معلومات الصنف
function show_item_info(item, stock_info) {
    const info_msg = `
        <div style="font-size: 12px; color: #666;">
            <strong>${stock_info.item_name}</strong><br>
            الكمية المتاحة: ${stock_info.actual_qty} ${stock_info.stock_uom}<br>
            معدل التقييم: ${format_currency(stock_info.valuation_rate)}
        </div>
    `;

    // يمكن إضافة المعلومات كتلميح أو في مكان مخصص
    console.log('معلومات الصنف:', stock_info);
}

// تنسيق العملة
function format_currency(amount, currency) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: currency || 'SAR'
    }).format(amount);
}


