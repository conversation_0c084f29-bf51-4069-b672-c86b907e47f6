// WhatsApp Group Form Script

frappe.ui.form.on('WhatsApp Group', {
    refresh: function(frm) {
        // Add custom button to sync group members
        frm.add_custom_button(__('Sync Members'), function() {
            frappe.confirm(
                __('This will sync members from WhatsApp. Continue?'),
                function() {
                    frm.call('sync_members')
                        .then(r => {
                            frm.reload_doc();
                        });
                }
            );
        });
        
        // Add custom button to send message to group
        frm.add_custom_button(__('Send Message'), function() {
            frappe.prompt([
                {
                    fieldname: 'message',
                    label: __('Message'),
                    fieldtype: 'Small Text',
                    reqd: 1
                },
                {
                    fieldname: 'attachment',
                    label: __('Attachment'),
                    fieldtype: 'Attach'
                }
            ], function(values) {
                frm.call({
                    method: 'send_message',
                    args: {
                        message: values.message,
                        attachments: values.attachment ? [values.attachment] : null
                    },
                    callback: function(r) {
                        if (r.message) {
                            frappe.show_alert({
                                message: __('Message sent to group.'),
                                indicator: 'green'
                            });
                        }
                    }
                });
            }, __('Send Message to Group'), __('Send'));
        }, __('Actions'));
    },
    
    sender_account: function(frm) {
        // Check if sender account is connected
        if (frm.doc.sender_account) {
            frappe.db.get_value('WhatsApp Sender Account', frm.doc.sender_account, 'status')
                .then(r => {
                    if (r.message && r.message.status !== 'Connected') {
                        frappe.show_alert({
                            message: __('Selected sender account is not connected.'),
                            indicator: 'red'
                        });
                    }
                });
        }
    }
});
