// WhatsApp Message Template Form Script

frappe.ui.form.on('WhatsApp Message Template', {
    refresh: function(frm) {
        // Add custom button to test template
        frm.add_custom_button(__('Test Template'), function() {
            // Get available doctypes from the template
            let doctypes = [];
            if (frm.doc.applicable_doctypes && frm.doc.applicable_doctypes.length) {
                doctypes = frm.doc.applicable_doctypes.map(d => d.doctype_name);
            }
            
            // If no doctypes specified, allow any doctype
            if (!doctypes.length) {
                doctypes = ['Sales Invoice', 'Sales Order', 'Purchase Order', 'Lead', 'Opportunity', 'Customer'];
            }
            
            // Prompt for test parameters
            let fields = [
                {
                    fieldname: 'doctype',
                    label: __('DocType'),
                    fieldtype: 'Select',
                    options: doctypes.join('\n'),
                    reqd: 1
                },
                {
                    fieldname: 'docname',
                    label: __('Document Name'),
                    fieldtype: 'Dynamic Link',
                    options: 'doctype',
                    reqd: 1
                },
                {
                    fieldname: 'sender_account',
                    label: __('Sender Account'),
                    fieldtype: 'Link',
                    options: 'WhatsApp Sender Account',
                    reqd: 1
                },
                {
                    fieldname: 'recipient',
                    label: __('Recipient (Override)'),
                    fieldtype: 'Data',
                    description: __('Leave empty to use recipient from document')
                }
            ];
            
            frappe.prompt(fields, function(values) {
                frappe.call({
                    method: 'whatsapp.whatsapp.doctype.whatsapp_message_template.whatsapp_message_template.test_template',
                    args: {
                        template: frm.doc.name,
                        doctype: values.doctype,
                        docname: values.docname,
                        sender_account: values.sender_account,
                        recipient: values.recipient
                    },
                    callback: function(r) {
                        if (r.message) {
                            frappe.msgprint({
                                title: __('Template Preview'),
                                indicator: 'green',
                                message: '<h4>' + __('Rendered Message:') + '</h4>' +
                                         '<div style="border: 1px solid #ddd; padding: 10px; margin-bottom: 15px;">' + 
                                         r.message.message + 
                                         '</div>' +
                                         '<p>' + __('Recipient: {0}', [r.message.recipient]) + '</p>'
                            });
                        }
                    }
                });
            }, __('Test Template'), __('Preview'));
        }, __('Actions'));
    }
});
