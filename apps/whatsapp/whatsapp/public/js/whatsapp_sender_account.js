// WhatsApp Sender Account Form Script

frappe.ui.form.on('WhatsApp Sender Account', {
    refresh: function(frm) {
        // Add custom buttons based on status
        if (frm.doc.status === "Disconnected") {
            frm.add_custom_button(__('Connect'), function() {
                // Call API to connect account
                frappe.call({
                    method: 'whatsapp.whatsapp.api.connect_account',
                    args: {
                        account_id: frm.doc.name
                    },
                    callback: function(r) {
                        if (r.message && r.message.success) {
                            frappe.show_alert({
                                message: __('Connecting WhatsApp account. Please wait for QR code.'),
                                indicator: 'green'
                            });
                            setTimeout(function() {
                                frm.reload_doc();
                            }, 5000);
                        } else {
                            frappe.msgprint(__('Failed to connect WhatsApp account: {0}',
                                [(r.message && r.message.error) || 'Unknown error']));
                        }
                    }
                });
            }).addClass('btn-primary');
        } else if (frm.doc.status === "Connected") {
            frm.add_custom_button(__('Disconnect'), function() {
                frappe.confirm(
                    __('Are you sure you want to disconnect this WhatsApp account?'),
                    function() {
                        // Call API to disconnect account
                        frappe.call({
                            method: 'whatsapp.whatsapp.api.disconnect_account',
                            args: {
                                account_id: frm.doc.name
                            },
                            callback: function(r) {
                                if (r.message && r.message.success) {
                                    frappe.show_alert({
                                        message: __('WhatsApp account disconnected.'),
                                        indicator: 'green'
                                    });
                                    frm.reload_doc();
                                } else {
                                    frappe.msgprint(__('Failed to disconnect WhatsApp account: {0}',
                                        [(r.message && r.message.error) || 'Unknown error']));
                                }
                            }
                        });
                    }
                );
            }).addClass('btn-danger');

            // Add button to send test message
            frm.add_custom_button(__('Send Test Message'), function() {
                frappe.prompt([
                    {
                        fieldname: 'recipient',
                        label: __('Recipient Phone Number'),
                        fieldtype: 'Data',
                        reqd: 1
                    },
                    {
                        fieldname: 'message',
                        label: __('Message'),
                        fieldtype: 'Small Text',
                        reqd: 1,
                        default: 'This is a test message from ERPNext WhatsApp Integration.'
                    }
                ], function(values) {
                    frappe.call({
                        method: 'whatsapp.whatsapp.api.send_message',
                        args: {
                            account_id: frm.doc.name,
                            to: values.recipient,
                            message: values.message
                        },
                        callback: function(r) {
                            if (r.message && r.message.success) {
                                frappe.show_alert({
                                    message: __('Test message sent successfully.'),
                                    indicator: 'green'
                                });
                            } else {
                                frappe.msgprint(__('Failed to send test message: {0}',
                                    [(r.message && r.message.error) || 'Unknown error']));
                            }
                        }
                    });
                }, __('Send Test Message'), __('Send'));
            }, __('Actions'));
        }

        // Auto-refresh when pending
        if (frm.doc.status === "Pending") {
            frm.add_custom_button(__('Refresh QR Code'), function() {
                frm.reload_doc();
            }).addClass('btn-primary');

            // Auto-refresh every 10 seconds
            if (!frm.qr_refresh_interval) {
                frm.qr_refresh_interval = setInterval(function() {
                    if (frm.doc.status === "Pending") {
                        frm.reload_doc();
                    } else {
                        clearInterval(frm.qr_refresh_interval);
                        frm.qr_refresh_interval = null;
                    }
                }, 10000);

                // Clear interval when form is closed
                $(window).on('beforeunload', function() {
                    if (frm.qr_refresh_interval) {
                        clearInterval(frm.qr_refresh_interval);
                        frm.qr_refresh_interval = null;
                    }
                });
            }
        } else if (frm.qr_refresh_interval) {
            clearInterval(frm.qr_refresh_interval);
            frm.qr_refresh_interval = null;
        }
    }
});
