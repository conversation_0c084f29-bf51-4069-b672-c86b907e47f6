# Copyright (c) 2023, moneer and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document
from datetime import datetime
from frappe import _

class WhatsAppGroup(Document):
    def validate(self):
        # Ensure sender account is connected
        sender = frappe.get_doc("WhatsApp Sender Account", self.sender_account)
        if sender.status != "Connected":
            frappe.throw(_("Sender account {0} is not connected").format(self.sender_account))
    
    def sync_members(self):
        """Sync group members from WhatsApp"""
        # This would be implemented with whatsapp-web.js
        # For now, we'll just update the last_synced timestamp
        self.last_synced = datetime.now()
        self.save()
        
        frappe.msgprint(_("Group members synced successfully"))
        
    def send_message(self, message, attachments=None):
        """Send a message to the group"""
        if not self.group_id:
            frappe.throw(_("Group ID is not set. Please sync the group first."))
            
        # Get the sender account
        sender = frappe.get_doc("WhatsApp Sender Account", self.sender_account)
        
        # This would be implemented with whatsapp-web.js
        # For now, we'll just log the message
        log = frappe.new_doc("WhatsApp Message Log")
        log.sender_account = self.sender_account
        log.recipient = self.group_name
        log.is_group = 1
        log.message = message
        log.status = "Pending"
        
        if attachments:
            log.has_attachments = 1
            
        log.save()
        
        return log.name
