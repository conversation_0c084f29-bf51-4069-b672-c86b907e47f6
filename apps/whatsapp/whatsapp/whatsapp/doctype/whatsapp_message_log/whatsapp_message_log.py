# Copyright (c) 2023, moneer and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document
from datetime import datetime

class WhatsAppMessageLog(Document):
    def validate(self):
        if self.status == "Sent" and not self.sent_time:
            self.sent_time = datetime.now()
        elif self.status == "Delivered" and not self.delivered_time:
            self.delivered_time = datetime.now()
        elif self.status == "Read" and not self.read_time:
            self.read_time = datetime.now()
    
    def update_status(self, status):
        """Update the message status"""
        self.status = status
        self.validate()  # This will set the appropriate timestamp
        self.save()
        
    def add_attachment(self, file_url, file_name=None, file_type=None):
        """Add an attachment to the message"""
        if not self.has_attachments:
            self.has_attachments = 1
            
        attachment = {
            "file_url": file_url,
            "file_name": file_name or file_url.split("/")[-1],
            "file_type": file_type or ""
        }
        
        self.append("attachments", attachment)
        self.save()
