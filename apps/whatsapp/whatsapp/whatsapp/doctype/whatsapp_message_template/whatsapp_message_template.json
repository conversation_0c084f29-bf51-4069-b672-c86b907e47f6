{"actions": [], "allow_rename": 1, "autoname": "field:template_name", "creation": "2023-05-21 14:10:00.000000", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["template_name", "description", "message_section", "message_text", "variables_section", "variables_html", "usage_section", "applicable_doctypes", "doctype_events"], "fields": [{"fieldname": "template_name", "fieldtype": "Data", "in_list_view": 1, "label": "Template Name", "reqd": 1, "unique": 1}, {"fieldname": "description", "fieldtype": "Small Text", "label": "Description"}, {"fieldname": "message_section", "fieldtype": "Section Break", "label": "Message"}, {"fieldname": "message_text", "fieldtype": "Text Editor", "label": "Message Text", "reqd": 1}, {"fieldname": "variables_section", "fieldtype": "Section Break", "label": "Variables"}, {"fieldname": "variables_html", "fieldtype": "HTML", "label": "Variables", "options": "<div class=\"alert alert-info\">\n<p><strong>Available Variables:</strong></p>\n<ul>\n<li><code>{{ doc.name }}</code> - Document Name</li>\n<li><code>{{ doc.customer_name }}</code> - Customer Name (for Sales documents)</li>\n<li><code>{{ doc.grand_total }}</code> - Grand Total (for Sales documents)</li>\n<li><code>{{ doc.posting_date }}</code> - Posting Date</li>\n<li><code>{{ frappe.utils.get_url() }}</code> - System URL</li>\n<li><code>{{ frappe.utils.get_url() }}/{{ doc.doctype }}/{{ doc.name }}</code> - Document URL</li>\n</ul>\n<p>You can use any field from the document by using <code>{{ doc.field_name }}</code></p>\n</div>"}, {"fieldname": "usage_section", "fieldtype": "Section Break", "label": "Usage"}, {"fieldname": "applicable_doctypes", "fieldtype": "Table MultiSelect", "label": "Applicable DocTypes", "options": "WhatsApp Template DocType"}, {"fieldname": "doctype_events", "fieldtype": "Table MultiSelect", "label": "DocType Events", "options": "WhatsApp Template Event"}], "index_web_pages_for_search": 1, "links": [], "modified": "2023-05-21 14:10:00.000000", "modified_by": "Administrator", "module": "WhatsApp", "name": "WhatsApp Message Template", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "All", "share": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}