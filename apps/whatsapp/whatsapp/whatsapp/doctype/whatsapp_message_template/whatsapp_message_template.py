# Copyright (c) 2023, moneer and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document
import re
from frappe import _
from jinja2 import Template

class WhatsAppMessageTemplate(Document):
    def validate(self):
        self.validate_variables()

    def validate_variables(self):
        """Validate that all variables in the template are valid"""
        # Extract variables from the template using regex
        variables = re.findall(r'{{(.*?)}}', self.message_text)

        # Check if variables are valid
        for var in variables:
            var = var.strip()
            if not var.startswith('doc.') and not var.startswith('frappe.'):
                frappe.throw(_("Invalid variable: {0}. Variables should start with 'doc.' or 'frappe.'").format(var))

    def render_template(self, doc):
        """Render the template with the given document"""
        if isinstance(doc, str):
            doc = frappe.get_doc(doc)

        context = {
            'doc': doc,
            'frappe': frappe
        }

        template = Template(self.message_text)
        return template.render(**context)

    def send_message(self, doc, sender_account, recipient_field=None, recipient=None):
        """Send a message using this template"""
        if isinstance(doc, str):
            doc = frappe.get_doc(doc)

        # Get the recipient
        if not recipient:
            if not recipient_field:
                # Try to guess the recipient field
                if hasattr(doc, 'contact_mobile'):
                    recipient = doc.contact_mobile
                elif hasattr(doc, 'mobile_no'):
                    recipient = doc.mobile_no
                elif hasattr(doc, 'phone'):
                    recipient = doc.phone
                else:
                    frappe.throw(_("Recipient not specified and could not be determined automatically"))
            else:
                recipient = getattr(doc, recipient_field, None)
                if not recipient:
                    frappe.throw(_("Recipient field {0} not found or empty in document").format(recipient_field))

        # Render the template
        message = self.render_template(doc)

        # Get the sender account
        if isinstance(sender_account, str):
            sender_account = frappe.get_doc("WhatsApp Sender Account", sender_account)

        # Send the message
        return sender_account.send_message(recipient, message)

@frappe.whitelist()
def test_template(template, doctype, docname, sender_account, recipient=None):
    """Test a WhatsApp message template with a document"""
    try:
        # Get the template
        template_doc = frappe.get_doc("WhatsApp Message Template", template)

        # Get the document
        doc = frappe.get_doc(doctype, docname)

        # Render the template
        message = template_doc.render_template(doc)

        # Get the recipient
        if not recipient:
            # Try to guess the recipient field
            if hasattr(doc, 'contact_mobile'):
                recipient = doc.contact_mobile
            elif hasattr(doc, 'mobile_no'):
                recipient = doc.mobile_no
            elif hasattr(doc, 'phone'):
                recipient = doc.phone
            else:
                recipient = "Not found in document"

        return {
            "message": message,
            "recipient": recipient
        }
    except Exception as e:
        frappe.log_error(f"Error testing WhatsApp template: {str(e)}")
        frappe.throw(_("Error testing template: {0}").format(str(e)))
