# Copyright (c) 2023, moneer and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document
import json
import os
import time
from frappe import _
from datetime import datetime

class WhatsAppSenderAccount(Document):
    def validate(self):
        # Validate phone number format
        if self.phone_number:
            # Remove any spaces or special characters
            self.phone_number = ''.join(filter(str.isdigit, self.phone_number))
            
            # Ensure it starts with country code
            if not self.phone_number.startswith('+'):
                self.phone_number = '+' + self.phone_number
    
    def connect(self):
        """Generate QR code for WhatsApp Web connection"""
        # This would be implemented with whatsapp-web.js
        # For now, we'll just update the status
        self.status = "Pending"
        self.save()
        
        # Return a placeholder QR code
        return {
            "qr_code": self.get_qr_placeholder(),
            "status": "pending"
        }
    
    def get_qr_placeholder(self):
        """Return a placeholder QR code HTML"""
        return """
        <div style="text-align: center; padding: 20px;">
            <img src="/assets/whatsapp/images/whatsapp_qr_placeholder.svg" 
                 alt="WhatsApp QR Code" 
                 style="width: 256px; height: 256px; border: 1px solid #ddd;">
            <p>Scan this QR code with your WhatsApp mobile app</p>
        </div>
        """
    
    def update_connection_status(self, status):
        """Update the connection status"""
        self.status = status
        if status == "Connected":
            self.last_connected = datetime.now()
        self.save()
        
    def disconnect(self):
        """Disconnect WhatsApp Web"""
        # This would be implemented with whatsapp-web.js
        # For now, we'll just update the status
        self.status = "Disconnected"
        self.save()
        
    def send_message(self, to_number, message, attachments=None):
        """Send a WhatsApp message"""
        if self.status != "Connected":
            frappe.throw(_("WhatsApp account is not connected"))
            
        # This would be implemented with whatsapp-web.js
        # For now, we'll just log the message
        log = frappe.new_doc("WhatsApp Message Log")
        log.sender_account = self.name
        log.recipient = to_number
        log.message = message
        log.status = "Pending"
        
        if attachments:
            log.has_attachments = 1
            
        log.save()
        
        return log.name
