{"actions": [], "allow_rename": 1, "autoname": "field:trigger_name", "creation": "2023-05-21 14:45:00.000000", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["trigger_name", "enabled", "doctype_section", "reference_doctype", "event", "condition", "message_section", "sender_account", "recipient_field", "message_template", "include_attachments", "attachment_field", "attachment_type"], "fields": [{"fieldname": "trigger_name", "fieldtype": "Data", "in_list_view": 1, "label": "Trigger Name", "reqd": 1, "unique": 1}, {"default": "1", "fieldname": "enabled", "fieldtype": "Check", "in_list_view": 1, "label": "Enabled"}, {"fieldname": "doctype_section", "fieldtype": "Section Break", "label": "DocType"}, {"fieldname": "reference_doctype", "fieldtype": "Link", "in_list_view": 1, "label": "Reference DocType", "options": "DocType", "reqd": 1}, {"fieldname": "event", "fieldtype": "Select", "in_list_view": 1, "label": "Event", "options": "on_submit\non_update\non_cancel\non_trash\non_update_after_submit\non_change", "reqd": 1}, {"fieldname": "condition", "fieldtype": "Code", "label": "Condition", "options": "Python"}, {"fieldname": "message_section", "fieldtype": "Section Break", "label": "Message"}, {"fieldname": "sender_account", "fieldtype": "Link", "label": "Sender Account", "options": "WhatsA<PERSON> Sender Account", "reqd": 1}, {"fieldname": "recipient_field", "fieldtype": "Data", "label": "Recipient Field", "reqd": 1}, {"fieldname": "message_template", "fieldtype": "Link", "label": "Message Template", "options": "WhatsApp Message Template", "reqd": 1}, {"default": "0", "fieldname": "include_attachments", "fieldtype": "Check", "label": "Include Attachments"}, {"depends_on": "include_attachments", "fieldname": "attachment_field", "fieldtype": "Data", "label": "Attachment Field"}, {"depends_on": "include_attachments", "fieldname": "attachment_type", "fieldtype": "Select", "label": "Attachment Type", "options": "File\nPDF\nImage"}], "index_web_pages_for_search": 1, "links": [], "modified": "2023-05-21 14:45:00.000000", "modified_by": "Administrator", "module": "WhatsApp", "name": "WhatsApp <PERSON>gger <PERSON>s", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}