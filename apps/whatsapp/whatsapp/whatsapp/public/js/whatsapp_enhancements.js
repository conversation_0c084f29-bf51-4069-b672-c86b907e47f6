/**
 * WhatsApp Enhancements
 * تحسينات إضافية لتطبيق WhatsApp
 */

console.log('🚀 تحميل WhatsApp Enhancements...');

// إضافة تحسينات للواجهة عند تحميل الصفحة
$(document).ready(function() {
    // إضافة أيقونة WhatsApp للشريط الجانبي
    addWhatsAppSidebarIcon();
    
    // إضافة اختصارات لوحة المفاتيح
    addKeyboardShortcuts();
    
    // تحسين مظهر الإشعارات
    enhanceNotifications();
    
    // إضافة معلومات إضافية للمستندات
    enhanceDocumentInfo();
});

function addWhatsAppSidebarIcon() {
    // إضافة أيقونة WhatsApp في الشريط الجانبي
    const sidebarItems = $('.sidebar-menu');
    if (sidebarItems.length && !$('.whatsapp-sidebar-icon').length) {
        const whatsappIcon = $(`
            <li class="whatsapp-sidebar-icon">
                <a href="#List/WhatsApp Message Log" class="sidebar-link">
                    <i class="fa fa-whatsapp" style="color: #25D366;"></i>
                    <span class="sidebar-item-label">WhatsApp</span>
                </a>
            </li>
        `);
        sidebarItems.prepend(whatsappIcon);
    }
}

function addKeyboardShortcuts() {
    // إضافة اختصارات لوحة المفاتيح
    $(document).on('keydown', function(e) {
        // Ctrl + Shift + W = فتح نافذة WhatsApp
        if (e.ctrlKey && e.shiftKey && e.which === 87) {
            e.preventDefault();
            if (window.cur_frm && cur_frm.doc && !cur_frm.doc.__islocal) {
                showWhatsAppDialog(cur_frm);
            }
        }
        
        // Ctrl + Shift + P = إرسال PDF سريع
        if (e.ctrlKey && e.shiftKey && e.which === 80) {
            e.preventDefault();
            if (window.cur_frm && cur_frm.doc && !cur_frm.doc.__islocal) {
                sendDocumentQuick(cur_frm, 'PDF');
            }
        }
        
        // Ctrl + Shift + L = إرسال رابط سريع
        if (e.ctrlKey && e.shiftKey && e.which === 76) {
            e.preventDefault();
            if (window.cur_frm && cur_frm.doc && !cur_frm.doc.__islocal) {
                sendDocumentQuick(cur_frm, 'رابط');
            }
        }
    });
}

function enhanceNotifications() {
    // تحسين مظهر الإشعارات
    const originalShowAlert = frappe.show_alert;
    frappe.show_alert = function(message, indicator) {
        if (typeof message === 'string' && message.includes('WhatsApp')) {
            // إضافة أيقونة WhatsApp للإشعارات
            message = `<i class="fa fa-whatsapp" style="color: #25D366; margin-right: 5px;"></i>${message}`;
        }
        return originalShowAlert.call(this, message, indicator);
    };
}

function enhanceDocumentInfo() {
    // إضافة معلومات WhatsApp للمستندات
    if (window.cur_frm && cur_frm.doc) {
        addWhatsAppInfo(cur_frm);
    }
}

function addWhatsAppInfo(frm) {
    // إضافة معلومات حول إرسال WhatsApp في المستند
    if (!frm.doc.__islocal && WHATSAPP_SUPPORTED_DOCTYPES.includes(frm.doctype)) {
        // البحث عن آخر رسالة WhatsApp مرسلة لهذا المستند
        frappe.call({
            method: 'frappe.client.get_list',
            args: {
                doctype: 'WhatsApp Message Log',
                filters: {
                    reference_doctype: frm.doctype,
                    reference_name: frm.doc.name
                },
                fields: ['name', 'recipient', 'sent_time', 'status'],
                order_by: 'sent_time desc',
                limit: 1
            },
            callback: function(r) {
                if (r.message && r.message.length > 0) {
                    const lastMessage = r.message[0];
                    addWhatsAppStatusInfo(frm, lastMessage);
                }
            }
        });
    }
}

function addWhatsAppStatusInfo(frm, messageInfo) {
    // إضافة معلومات حالة WhatsApp في المستند
    const statusHtml = `
        <div class="alert alert-info whatsapp-status-info" style="margin: 10px 0;">
            <i class="fa fa-whatsapp" style="color: #25D366; margin-right: 5px;"></i>
            <strong>آخر إرسال WhatsApp:</strong><br>
            المستقبل: ${messageInfo.recipient}<br>
            التاريخ: ${moment(messageInfo.sent_time).format('DD/MM/YYYY HH:mm')}<br>
            الحالة: ${messageInfo.status}
        </div>
    `;
    
    // إضافة المعلومات في أعلى النموذج
    if (!$('.whatsapp-status-info').length) {
        $(statusHtml).insertAfter('.form-layout .section-body:first');
    }
}

// دالة لتحديث حالة الاتصال بـ WhatsApp
function updateWhatsAppConnectionStatus() {
    frappe.call({
        method: 'whatsapp.whatsapp.api.get_default_sender_account',
        callback: function(r) {
            if (r.message) {
                // التحقق من حالة الاتصال
                frappe.call({
                    method: 'frappe.client.get_value',
                    args: {
                        doctype: 'WhatsApp Sender Account',
                        fieldname: 'status',
                        filters: {name: r.message}
                    },
                    callback: function(status_r) {
                        updateConnectionIndicator(status_r.message ? status_r.message.status : 'Disconnected');
                    }
                });
            } else {
                updateConnectionIndicator('No Account');
            }
        }
    });
}

function updateConnectionIndicator(status) {
    // تحديث مؤشر حالة الاتصال في الشريط العلوي
    let indicator = $('.whatsapp-connection-indicator');
    if (!indicator.length) {
        indicator = $(`
            <div class="whatsapp-connection-indicator" style="
                position: fixed;
                top: 10px;
                right: 10px;
                z-index: 1000;
                padding: 5px 10px;
                border-radius: 15px;
                font-size: 12px;
                color: white;
                cursor: pointer;
            ">
                <i class="fa fa-whatsapp"></i>
                <span class="status-text"></span>
            </div>
        `);
        $('body').append(indicator);
        
        // إضافة حدث النقر للانتقال إلى إعدادات WhatsApp
        indicator.on('click', function() {
            frappe.set_route('Form', 'WhatsApp Settings');
        });
    }
    
    const statusText = indicator.find('.status-text');
    
    switch(status) {
        case 'Connected':
            indicator.css('background-color', '#25D366');
            statusText.text('متصل');
            break;
        case 'Pending':
            indicator.css('background-color', '#ffc107');
            statusText.text('في الانتظار');
            break;
        case 'Disconnected':
            indicator.css('background-color', '#dc3545');
            statusText.text('غير متصل');
            break;
        default:
            indicator.css('background-color', '#6c757d');
            statusText.text('لا يوجد حساب');
    }
}

// تحديث حالة الاتصال كل دقيقة
setInterval(updateWhatsAppConnectionStatus, 60000);

// تحديث حالة الاتصال عند تحميل الصفحة
setTimeout(updateWhatsAppConnectionStatus, 2000);

// دالة لإضافة قوالب رسائل سريعة
function addQuickMessageTemplates() {
    const templates = {
        'تحية': 'السلام عليكم ورحمة الله وبركاته',
        'شكر': 'شكراً لكم على تعاملكم معنا',
        'اعتذار': 'نعتذر عن أي إزعاج قد يكون حدث',
        'متابعة': 'نود متابعة الموضوع معكم',
        'تأكيد': 'تم تأكيد طلبكم بنجاح',
        'إلغاء': 'تم إلغاء الطلب كما هو مطلوب',
        'تذكير': 'هذا تذكير بخصوص الموضوع المطلوب'
    };
    
    return templates;
}

// دالة لإضافة إحصائيات WhatsApp
function addWhatsAppStats() {
    if (frappe.user.has_role('System Manager')) {
        frappe.call({
            method: 'frappe.client.get_list',
            args: {
                doctype: 'WhatsApp Message Log',
                fields: ['status'],
                filters: {
                    sent_time: ['>=', frappe.datetime.add_days(frappe.datetime.now_date(), -30)]
                }
            },
            callback: function(r) {
                if (r.message) {
                    const stats = {
                        total: r.message.length,
                        sent: r.message.filter(m => m.status === 'Sent').length,
                        delivered: r.message.filter(m => m.status === 'Delivered').length,
                        read: r.message.filter(m => m.status === 'Read').length,
                        failed: r.message.filter(m => m.status === 'Failed').length
                    };
                    
                    displayWhatsAppStats(stats);
                }
            }
        });
    }
}

function displayWhatsAppStats(stats) {
    // عرض إحصائيات WhatsApp في لوحة التحكم
    const statsHtml = `
        <div class="whatsapp-stats-widget" style="
            background: linear-gradient(135deg, #25D366, #128C7E);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
        ">
            <h5><i class="fa fa-whatsapp"></i> إحصائيات WhatsApp (آخر 30 يوم)</h5>
            <div class="row">
                <div class="col-md-3">
                    <div class="text-center">
                        <h4>${stats.total}</h4>
                        <small>إجمالي الرسائل</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h4>${stats.sent}</h4>
                        <small>مرسلة</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h4>${stats.delivered}</h4>
                        <small>مستلمة</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h4>${stats.read}</h4>
                        <small>مقروءة</small>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // إضافة الإحصائيات في الصفحة الرئيسية
    if (window.location.hash === '#desk') {
        setTimeout(() => {
            if (!$('.whatsapp-stats-widget').length) {
                $('.layout-main-section').prepend(statsHtml);
            }
        }, 1000);
    }
}

// عرض الإحصائيات عند تحميل الصفحة الرئيسية
$(document).on('page-change', function() {
    if (window.location.hash === '#desk') {
        setTimeout(addWhatsAppStats, 2000);
    }
});

console.log('✅ تم تحميل WhatsApp Enhancements بنجاح');
