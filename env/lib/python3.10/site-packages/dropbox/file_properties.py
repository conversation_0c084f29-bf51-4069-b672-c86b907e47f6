# -*- coding: utf-8 -*-
# Auto-generated by <PERSON>, do not modify.
# @generated
# flake8: noqa
# pylint: skip-file
"""
This namespace contains helpers for property and template metadata endpoints.

These endpoints enable you to tag arbitrary key/value data to Dropbox files.

The most basic unit in this namespace is the :class:`PropertyField`. These fields encapsulate the actual key/value data.

Fields are added to a Dropbox file using a :class:`PropertyGroup`. Property groups contain a reference to a Dropbox file and a :class:`PropertyGroupTemplate`. Property groups are uniquely identified by the combination of their associated Dropbox file and template.

The :class:`PropertyGroupTemplate` is a way of restricting the possible key names and value types of the data within a property group. The possible key names and value types are explicitly enumerated using :class:`PropertyFieldTemplate` objects.

You can think of a property group template as a class definition for a particular key/value metadata object, and the property groups themselves as the instantiations of these objects.

Templates are owned either by a user/app pair or team/app pair. Templates and their associated properties can't be accessed by any app other than the app that created them, and even then, only when the app is linked with the owner of the template (either a user or team).

User-owned templates are accessed via the user-auth file_properties/templates/*_for_user endpoints, while team-owned templates are accessed via the team-auth file_properties/templates/*_for_team endpoints. Properties associated with either type of template can be accessed via the user-auth properties/* endpoints.

Finally, properties can be accessed from a number of endpoints that return metadata, including `files/get_metadata`, and `files/list_folder`. Properties can also be added during upload, using `files/upload`.
"""

from __future__ import unicode_literals
from stone.backends.python_rsrc import stone_base as bb
from stone.backends.python_rsrc import stone_validators as bv

class AddPropertiesArg(bb.Struct):
    """
    :ivar file_properties.AddPropertiesArg.path: A unique identifier for the
        file or folder.
    :ivar file_properties.AddPropertiesArg.property_groups: The property groups
        which are to be added to a Dropbox file. No two groups in the input
        should  refer to the same template.
    """

    __slots__ = [
        '_path_value',
        '_property_groups_value',
    ]

    _has_required_fields = True

    def __init__(self,
                 path=None,
                 property_groups=None):
        self._path_value = bb.NOT_SET
        self._property_groups_value = bb.NOT_SET
        if path is not None:
            self.path = path
        if property_groups is not None:
            self.property_groups = property_groups

    # Instance attribute type: str (validator is set below)
    path = bb.Attribute("path")

    # Instance attribute type: list of [PropertyGroup] (validator is set below)
    property_groups = bb.Attribute("property_groups")

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(AddPropertiesArg, self)._process_custom_annotations(annotation_type, field_path, processor)

AddPropertiesArg_validator = bv.Struct(AddPropertiesArg)

class TemplateError(bb.Union):
    """
    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar str file_properties.TemplateError.template_not_found: Template does
        not exist for the given identifier.
    :ivar file_properties.TemplateError.restricted_content: You do not have
        permission to modify this template.
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    restricted_content = None
    # Attribute is overwritten below the class definition
    other = None

    @classmethod
    def template_not_found(cls, val):
        """
        Create an instance of this class set to the ``template_not_found`` tag
        with value ``val``.

        :param str val:
        :rtype: TemplateError
        """
        return cls('template_not_found', val)

    def is_template_not_found(self):
        """
        Check if the union tag is ``template_not_found``.

        :rtype: bool
        """
        return self._tag == 'template_not_found'

    def is_restricted_content(self):
        """
        Check if the union tag is ``restricted_content``.

        :rtype: bool
        """
        return self._tag == 'restricted_content'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def get_template_not_found(self):
        """
        Template does not exist for the given identifier.

        Only call this if :meth:`is_template_not_found` is true.

        :rtype: str
        """
        if not self.is_template_not_found():
            raise AttributeError("tag 'template_not_found' not set")
        return self._value

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(TemplateError, self)._process_custom_annotations(annotation_type, field_path, processor)

TemplateError_validator = bv.Union(TemplateError)

class PropertiesError(TemplateError):
    """
    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar file_properties.PropertiesError.unsupported_folder: This folder cannot
        be tagged. Tagging folders is not supported for team-owned templates.
    """

    # Attribute is overwritten below the class definition
    unsupported_folder = None

    @classmethod
    def path(cls, val):
        """
        Create an instance of this class set to the ``path`` tag with value
        ``val``.

        :param LookupError val:
        :rtype: PropertiesError
        """
        return cls('path', val)

    def is_path(self):
        """
        Check if the union tag is ``path``.

        :rtype: bool
        """
        return self._tag == 'path'

    def is_unsupported_folder(self):
        """
        Check if the union tag is ``unsupported_folder``.

        :rtype: bool
        """
        return self._tag == 'unsupported_folder'

    def get_path(self):
        """
        Only call this if :meth:`is_path` is true.

        :rtype: LookupError
        """
        if not self.is_path():
            raise AttributeError("tag 'path' not set")
        return self._value

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(PropertiesError, self)._process_custom_annotations(annotation_type, field_path, processor)

PropertiesError_validator = bv.Union(PropertiesError)

class InvalidPropertyGroupError(PropertiesError):
    """
    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar file_properties.InvalidPropertyGroupError.property_field_too_large:
        One or more of the supplied property field values is too large.
    :ivar file_properties.InvalidPropertyGroupError.does_not_fit_template: One
        or more of the supplied property fields does not conform to the template
        specifications.
    :ivar file_properties.InvalidPropertyGroupError.duplicate_property_groups:
        There are 2 or more property groups referring to the same templates in
        the input.
    """

    # Attribute is overwritten below the class definition
    property_field_too_large = None
    # Attribute is overwritten below the class definition
    does_not_fit_template = None
    # Attribute is overwritten below the class definition
    duplicate_property_groups = None

    def is_property_field_too_large(self):
        """
        Check if the union tag is ``property_field_too_large``.

        :rtype: bool
        """
        return self._tag == 'property_field_too_large'

    def is_does_not_fit_template(self):
        """
        Check if the union tag is ``does_not_fit_template``.

        :rtype: bool
        """
        return self._tag == 'does_not_fit_template'

    def is_duplicate_property_groups(self):
        """
        Check if the union tag is ``duplicate_property_groups``.

        :rtype: bool
        """
        return self._tag == 'duplicate_property_groups'

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(InvalidPropertyGroupError, self)._process_custom_annotations(annotation_type, field_path, processor)

InvalidPropertyGroupError_validator = bv.Union(InvalidPropertyGroupError)

class AddPropertiesError(InvalidPropertyGroupError):
    """
    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar file_properties.AddPropertiesError.property_group_already_exists: A
        property group associated with this template and file already exists.
    """

    # Attribute is overwritten below the class definition
    property_group_already_exists = None

    def is_property_group_already_exists(self):
        """
        Check if the union tag is ``property_group_already_exists``.

        :rtype: bool
        """
        return self._tag == 'property_group_already_exists'

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(AddPropertiesError, self)._process_custom_annotations(annotation_type, field_path, processor)

AddPropertiesError_validator = bv.Union(AddPropertiesError)

class PropertyGroupTemplate(bb.Struct):
    """
    Defines how a property group may be structured.

    :ivar file_properties.PropertyGroupTemplate.name: Display name for the
        template. Template names can be up to 256 bytes.
    :ivar file_properties.PropertyGroupTemplate.description: Description for the
        template. Template descriptions can be up to 1024 bytes.
    :ivar file_properties.PropertyGroupTemplate.fields: Definitions of the
        property fields associated with this template. There can be up to 32
        properties in a single template.
    """

    __slots__ = [
        '_name_value',
        '_description_value',
        '_fields_value',
    ]

    _has_required_fields = True

    def __init__(self,
                 name=None,
                 description=None,
                 fields=None):
        self._name_value = bb.NOT_SET
        self._description_value = bb.NOT_SET
        self._fields_value = bb.NOT_SET
        if name is not None:
            self.name = name
        if description is not None:
            self.description = description
        if fields is not None:
            self.fields = fields

    # Instance attribute type: str (validator is set below)
    name = bb.Attribute("name")

    # Instance attribute type: str (validator is set below)
    description = bb.Attribute("description")

    # Instance attribute type: list of [PropertyFieldTemplate] (validator is set below)
    fields = bb.Attribute("fields")

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(PropertyGroupTemplate, self)._process_custom_annotations(annotation_type, field_path, processor)

PropertyGroupTemplate_validator = bv.Struct(PropertyGroupTemplate)

class AddTemplateArg(PropertyGroupTemplate):

    __slots__ = [
    ]

    _has_required_fields = True

    def __init__(self,
                 name=None,
                 description=None,
                 fields=None):
        super(AddTemplateArg, self).__init__(name,
                                             description,
                                             fields)

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(AddTemplateArg, self)._process_custom_annotations(annotation_type, field_path, processor)

AddTemplateArg_validator = bv.Struct(AddTemplateArg)

class AddTemplateResult(bb.Struct):
    """
    :ivar file_properties.AddTemplateResult.template_id: An identifier for
        template added by  See
        :meth:`dropbox.dropbox_client.Dropbox.file_properties_templates_add_for_user`
        or
        :meth:`dropbox.dropbox_client.Dropbox.file_properties_templates_add_for_team`.
    """

    __slots__ = [
        '_template_id_value',
    ]

    _has_required_fields = True

    def __init__(self,
                 template_id=None):
        self._template_id_value = bb.NOT_SET
        if template_id is not None:
            self.template_id = template_id

    # Instance attribute type: str (validator is set below)
    template_id = bb.Attribute("template_id")

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(AddTemplateResult, self)._process_custom_annotations(annotation_type, field_path, processor)

AddTemplateResult_validator = bv.Struct(AddTemplateResult)

class GetTemplateArg(bb.Struct):
    """
    :ivar file_properties.GetTemplateArg.template_id: An identifier for template
        added by route  See
        :meth:`dropbox.dropbox_client.Dropbox.file_properties_templates_add_for_user`
        or
        :meth:`dropbox.dropbox_client.Dropbox.file_properties_templates_add_for_team`.
    """

    __slots__ = [
        '_template_id_value',
    ]

    _has_required_fields = True

    def __init__(self,
                 template_id=None):
        self._template_id_value = bb.NOT_SET
        if template_id is not None:
            self.template_id = template_id

    # Instance attribute type: str (validator is set below)
    template_id = bb.Attribute("template_id")

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(GetTemplateArg, self)._process_custom_annotations(annotation_type, field_path, processor)

GetTemplateArg_validator = bv.Struct(GetTemplateArg)

class GetTemplateResult(PropertyGroupTemplate):

    __slots__ = [
    ]

    _has_required_fields = True

    def __init__(self,
                 name=None,
                 description=None,
                 fields=None):
        super(GetTemplateResult, self).__init__(name,
                                                description,
                                                fields)

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(GetTemplateResult, self)._process_custom_annotations(annotation_type, field_path, processor)

GetTemplateResult_validator = bv.Struct(GetTemplateResult)

class ListTemplateResult(bb.Struct):
    """
    :ivar file_properties.ListTemplateResult.template_ids: List of identifiers
        for templates added by  See
        :meth:`dropbox.dropbox_client.Dropbox.file_properties_templates_add_for_user`
        or
        :meth:`dropbox.dropbox_client.Dropbox.file_properties_templates_add_for_team`.
    """

    __slots__ = [
        '_template_ids_value',
    ]

    _has_required_fields = True

    def __init__(self,
                 template_ids=None):
        self._template_ids_value = bb.NOT_SET
        if template_ids is not None:
            self.template_ids = template_ids

    # Instance attribute type: list of [str] (validator is set below)
    template_ids = bb.Attribute("template_ids")

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(ListTemplateResult, self)._process_custom_annotations(annotation_type, field_path, processor)

ListTemplateResult_validator = bv.Struct(ListTemplateResult)

class LogicalOperator(bb.Union):
    """
    Logical operator to join search queries together.

    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar file_properties.LogicalOperator.or_operator: Append a query with an
        "or" operator.
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    or_operator = None
    # Attribute is overwritten below the class definition
    other = None

    def is_or_operator(self):
        """
        Check if the union tag is ``or_operator``.

        :rtype: bool
        """
        return self._tag == 'or_operator'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(LogicalOperator, self)._process_custom_annotations(annotation_type, field_path, processor)

LogicalOperator_validator = bv.Union(LogicalOperator)

class LookUpPropertiesError(bb.Union):
    """
    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar file_properties.LookUpPropertiesError.property_group_not_found: No
        property group was found.
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    property_group_not_found = None
    # Attribute is overwritten below the class definition
    other = None

    def is_property_group_not_found(self):
        """
        Check if the union tag is ``property_group_not_found``.

        :rtype: bool
        """
        return self._tag == 'property_group_not_found'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(LookUpPropertiesError, self)._process_custom_annotations(annotation_type, field_path, processor)

LookUpPropertiesError_validator = bv.Union(LookUpPropertiesError)

class LookupError(bb.Union):
    """
    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar file_properties.LookupError.not_found: There is nothing at the given
        path.
    :ivar file_properties.LookupError.not_file: We were expecting a file, but
        the given path refers to something that isn't a file.
    :ivar file_properties.LookupError.not_folder: We were expecting a folder,
        but the given path refers to something that isn't a folder.
    :ivar file_properties.LookupError.restricted_content: The file cannot be
        transferred because the content is restricted. For example, we might
        restrict a file due to legal requirements.
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    not_found = None
    # Attribute is overwritten below the class definition
    not_file = None
    # Attribute is overwritten below the class definition
    not_folder = None
    # Attribute is overwritten below the class definition
    restricted_content = None
    # Attribute is overwritten below the class definition
    other = None

    @classmethod
    def malformed_path(cls, val):
        """
        Create an instance of this class set to the ``malformed_path`` tag with
        value ``val``.

        :param str val:
        :rtype: LookupError
        """
        return cls('malformed_path', val)

    def is_malformed_path(self):
        """
        Check if the union tag is ``malformed_path``.

        :rtype: bool
        """
        return self._tag == 'malformed_path'

    def is_not_found(self):
        """
        Check if the union tag is ``not_found``.

        :rtype: bool
        """
        return self._tag == 'not_found'

    def is_not_file(self):
        """
        Check if the union tag is ``not_file``.

        :rtype: bool
        """
        return self._tag == 'not_file'

    def is_not_folder(self):
        """
        Check if the union tag is ``not_folder``.

        :rtype: bool
        """
        return self._tag == 'not_folder'

    def is_restricted_content(self):
        """
        Check if the union tag is ``restricted_content``.

        :rtype: bool
        """
        return self._tag == 'restricted_content'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def get_malformed_path(self):
        """
        Only call this if :meth:`is_malformed_path` is true.

        :rtype: str
        """
        if not self.is_malformed_path():
            raise AttributeError("tag 'malformed_path' not set")
        return self._value

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(LookupError, self)._process_custom_annotations(annotation_type, field_path, processor)

LookupError_validator = bv.Union(LookupError)

class ModifyTemplateError(TemplateError):
    """
    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar file_properties.ModifyTemplateError.conflicting_property_names: A
        property field key with that name already exists in the template.
    :ivar file_properties.ModifyTemplateError.too_many_properties: There are too
        many properties in the changed template. The maximum number of
        properties per template is 32.
    :ivar file_properties.ModifyTemplateError.too_many_templates: There are too
        many templates for the team.
    :ivar file_properties.ModifyTemplateError.template_attribute_too_large: The
        template name, description or one or more of the property field keys is
        too large.
    """

    # Attribute is overwritten below the class definition
    conflicting_property_names = None
    # Attribute is overwritten below the class definition
    too_many_properties = None
    # Attribute is overwritten below the class definition
    too_many_templates = None
    # Attribute is overwritten below the class definition
    template_attribute_too_large = None

    def is_conflicting_property_names(self):
        """
        Check if the union tag is ``conflicting_property_names``.

        :rtype: bool
        """
        return self._tag == 'conflicting_property_names'

    def is_too_many_properties(self):
        """
        Check if the union tag is ``too_many_properties``.

        :rtype: bool
        """
        return self._tag == 'too_many_properties'

    def is_too_many_templates(self):
        """
        Check if the union tag is ``too_many_templates``.

        :rtype: bool
        """
        return self._tag == 'too_many_templates'

    def is_template_attribute_too_large(self):
        """
        Check if the union tag is ``template_attribute_too_large``.

        :rtype: bool
        """
        return self._tag == 'template_attribute_too_large'

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(ModifyTemplateError, self)._process_custom_annotations(annotation_type, field_path, processor)

ModifyTemplateError_validator = bv.Union(ModifyTemplateError)

class OverwritePropertyGroupArg(bb.Struct):
    """
    :ivar file_properties.OverwritePropertyGroupArg.path: A unique identifier
        for the file or folder.
    :ivar file_properties.OverwritePropertyGroupArg.property_groups: The
        property groups "snapshot" updates to force apply. No two groups in the
        input should  refer to the same template.
    """

    __slots__ = [
        '_path_value',
        '_property_groups_value',
    ]

    _has_required_fields = True

    def __init__(self,
                 path=None,
                 property_groups=None):
        self._path_value = bb.NOT_SET
        self._property_groups_value = bb.NOT_SET
        if path is not None:
            self.path = path
        if property_groups is not None:
            self.property_groups = property_groups

    # Instance attribute type: str (validator is set below)
    path = bb.Attribute("path")

    # Instance attribute type: list of [PropertyGroup] (validator is set below)
    property_groups = bb.Attribute("property_groups")

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(OverwritePropertyGroupArg, self)._process_custom_annotations(annotation_type, field_path, processor)

OverwritePropertyGroupArg_validator = bv.Struct(OverwritePropertyGroupArg)

class PropertiesSearchArg(bb.Struct):
    """
    :ivar file_properties.PropertiesSearchArg.queries: Queries to search.
    :ivar file_properties.PropertiesSearchArg.template_filter: Filter results to
        contain only properties associated with these template IDs.
    """

    __slots__ = [
        '_queries_value',
        '_template_filter_value',
    ]

    _has_required_fields = True

    def __init__(self,
                 queries=None,
                 template_filter=None):
        self._queries_value = bb.NOT_SET
        self._template_filter_value = bb.NOT_SET
        if queries is not None:
            self.queries = queries
        if template_filter is not None:
            self.template_filter = template_filter

    # Instance attribute type: list of [PropertiesSearchQuery] (validator is set below)
    queries = bb.Attribute("queries")

    # Instance attribute type: TemplateFilter (validator is set below)
    template_filter = bb.Attribute("template_filter", user_defined=True)

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(PropertiesSearchArg, self)._process_custom_annotations(annotation_type, field_path, processor)

PropertiesSearchArg_validator = bv.Struct(PropertiesSearchArg)

class PropertiesSearchContinueArg(bb.Struct):
    """
    :ivar file_properties.PropertiesSearchContinueArg.cursor: The cursor
        returned by your last call to
        :meth:`dropbox.dropbox_client.Dropbox.file_properties_properties_search`
        or
        :meth:`dropbox.dropbox_client.Dropbox.file_properties_properties_search_continue`.
    """

    __slots__ = [
        '_cursor_value',
    ]

    _has_required_fields = True

    def __init__(self,
                 cursor=None):
        self._cursor_value = bb.NOT_SET
        if cursor is not None:
            self.cursor = cursor

    # Instance attribute type: str (validator is set below)
    cursor = bb.Attribute("cursor")

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(PropertiesSearchContinueArg, self)._process_custom_annotations(annotation_type, field_path, processor)

PropertiesSearchContinueArg_validator = bv.Struct(PropertiesSearchContinueArg)

class PropertiesSearchContinueError(bb.Union):
    """
    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar file_properties.PropertiesSearchContinueError.reset: Indicates that
        the cursor has been invalidated. Call
        :meth:`dropbox.dropbox_client.Dropbox.file_properties_properties_search`
        to obtain a new cursor.
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    reset = None
    # Attribute is overwritten below the class definition
    other = None

    def is_reset(self):
        """
        Check if the union tag is ``reset``.

        :rtype: bool
        """
        return self._tag == 'reset'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(PropertiesSearchContinueError, self)._process_custom_annotations(annotation_type, field_path, processor)

PropertiesSearchContinueError_validator = bv.Union(PropertiesSearchContinueError)

class PropertiesSearchError(bb.Union):
    """
    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    other = None

    @classmethod
    def property_group_lookup(cls, val):
        """
        Create an instance of this class set to the ``property_group_lookup``
        tag with value ``val``.

        :param LookUpPropertiesError val:
        :rtype: PropertiesSearchError
        """
        return cls('property_group_lookup', val)

    def is_property_group_lookup(self):
        """
        Check if the union tag is ``property_group_lookup``.

        :rtype: bool
        """
        return self._tag == 'property_group_lookup'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def get_property_group_lookup(self):
        """
        Only call this if :meth:`is_property_group_lookup` is true.

        :rtype: LookUpPropertiesError
        """
        if not self.is_property_group_lookup():
            raise AttributeError("tag 'property_group_lookup' not set")
        return self._value

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(PropertiesSearchError, self)._process_custom_annotations(annotation_type, field_path, processor)

PropertiesSearchError_validator = bv.Union(PropertiesSearchError)

class PropertiesSearchMatch(bb.Struct):
    """
    :ivar file_properties.PropertiesSearchMatch.id: The ID for the matched file
        or folder.
    :ivar file_properties.PropertiesSearchMatch.path: The path for the matched
        file or folder.
    :ivar file_properties.PropertiesSearchMatch.is_deleted: Whether the file or
        folder is deleted.
    :ivar file_properties.PropertiesSearchMatch.property_groups: List of custom
        property groups associated with the file.
    """

    __slots__ = [
        '_id_value',
        '_path_value',
        '_is_deleted_value',
        '_property_groups_value',
    ]

    _has_required_fields = True

    def __init__(self,
                 id=None,
                 path=None,
                 is_deleted=None,
                 property_groups=None):
        self._id_value = bb.NOT_SET
        self._path_value = bb.NOT_SET
        self._is_deleted_value = bb.NOT_SET
        self._property_groups_value = bb.NOT_SET
        if id is not None:
            self.id = id
        if path is not None:
            self.path = path
        if is_deleted is not None:
            self.is_deleted = is_deleted
        if property_groups is not None:
            self.property_groups = property_groups

    # Instance attribute type: str (validator is set below)
    id = bb.Attribute("id")

    # Instance attribute type: str (validator is set below)
    path = bb.Attribute("path")

    # Instance attribute type: bool (validator is set below)
    is_deleted = bb.Attribute("is_deleted")

    # Instance attribute type: list of [PropertyGroup] (validator is set below)
    property_groups = bb.Attribute("property_groups")

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(PropertiesSearchMatch, self)._process_custom_annotations(annotation_type, field_path, processor)

PropertiesSearchMatch_validator = bv.Struct(PropertiesSearchMatch)

class PropertiesSearchMode(bb.Union):
    """
    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar str file_properties.PropertiesSearchMode.field_name: Search for a
        value associated with this field name.
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    other = None

    @classmethod
    def field_name(cls, val):
        """
        Create an instance of this class set to the ``field_name`` tag with
        value ``val``.

        :param str val:
        :rtype: PropertiesSearchMode
        """
        return cls('field_name', val)

    def is_field_name(self):
        """
        Check if the union tag is ``field_name``.

        :rtype: bool
        """
        return self._tag == 'field_name'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def get_field_name(self):
        """
        Search for a value associated with this field name.

        Only call this if :meth:`is_field_name` is true.

        :rtype: str
        """
        if not self.is_field_name():
            raise AttributeError("tag 'field_name' not set")
        return self._value

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(PropertiesSearchMode, self)._process_custom_annotations(annotation_type, field_path, processor)

PropertiesSearchMode_validator = bv.Union(PropertiesSearchMode)

class PropertiesSearchQuery(bb.Struct):
    """
    :ivar file_properties.PropertiesSearchQuery.query: The property field value
        for which to search across templates.
    :ivar file_properties.PropertiesSearchQuery.mode: The mode with which to
        perform the search.
    :ivar file_properties.PropertiesSearchQuery.logical_operator: The logical
        operator with which to append the query.
    """

    __slots__ = [
        '_query_value',
        '_mode_value',
        '_logical_operator_value',
    ]

    _has_required_fields = True

    def __init__(self,
                 query=None,
                 mode=None,
                 logical_operator=None):
        self._query_value = bb.NOT_SET
        self._mode_value = bb.NOT_SET
        self._logical_operator_value = bb.NOT_SET
        if query is not None:
            self.query = query
        if mode is not None:
            self.mode = mode
        if logical_operator is not None:
            self.logical_operator = logical_operator

    # Instance attribute type: str (validator is set below)
    query = bb.Attribute("query")

    # Instance attribute type: PropertiesSearchMode (validator is set below)
    mode = bb.Attribute("mode", user_defined=True)

    # Instance attribute type: LogicalOperator (validator is set below)
    logical_operator = bb.Attribute("logical_operator", user_defined=True)

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(PropertiesSearchQuery, self)._process_custom_annotations(annotation_type, field_path, processor)

PropertiesSearchQuery_validator = bv.Struct(PropertiesSearchQuery)

class PropertiesSearchResult(bb.Struct):
    """
    :ivar file_properties.PropertiesSearchResult.matches: A list (possibly
        empty) of matches for the query.
    :ivar file_properties.PropertiesSearchResult.cursor: Pass the cursor into
        :meth:`dropbox.dropbox_client.Dropbox.file_properties_properties_search_continue`
        to continue to receive search results. Cursor will be null when there
        are no more results.
    """

    __slots__ = [
        '_matches_value',
        '_cursor_value',
    ]

    _has_required_fields = True

    def __init__(self,
                 matches=None,
                 cursor=None):
        self._matches_value = bb.NOT_SET
        self._cursor_value = bb.NOT_SET
        if matches is not None:
            self.matches = matches
        if cursor is not None:
            self.cursor = cursor

    # Instance attribute type: list of [PropertiesSearchMatch] (validator is set below)
    matches = bb.Attribute("matches")

    # Instance attribute type: str (validator is set below)
    cursor = bb.Attribute("cursor", nullable=True)

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(PropertiesSearchResult, self)._process_custom_annotations(annotation_type, field_path, processor)

PropertiesSearchResult_validator = bv.Struct(PropertiesSearchResult)

class PropertyField(bb.Struct):
    """
    Raw key/value data to be associated with a Dropbox file. Property fields are
    added to Dropbox files as a :class:`PropertyGroup`.

    :ivar file_properties.PropertyField.name: Key of the property field
        associated with a file and template. Keys can be up to 256 bytes.
    :ivar file_properties.PropertyField.value: Value of the property field
        associated with a file and template. Values can be up to 1024 bytes.
    """

    __slots__ = [
        '_name_value',
        '_value_value',
    ]

    _has_required_fields = True

    def __init__(self,
                 name=None,
                 value=None):
        self._name_value = bb.NOT_SET
        self._value_value = bb.NOT_SET
        if name is not None:
            self.name = name
        if value is not None:
            self.value = value

    # Instance attribute type: str (validator is set below)
    name = bb.Attribute("name")

    # Instance attribute type: str (validator is set below)
    value = bb.Attribute("value")

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(PropertyField, self)._process_custom_annotations(annotation_type, field_path, processor)

PropertyField_validator = bv.Struct(PropertyField)

class PropertyFieldTemplate(bb.Struct):
    """
    Defines how a single property field may be structured. Used exclusively by
    :class:`PropertyGroupTemplate`.

    :ivar file_properties.PropertyFieldTemplate.name: Key of the property field
        being described. Property field keys can be up to 256 bytes.
    :ivar file_properties.PropertyFieldTemplate.description: Description of the
        property field. Property field descriptions can be up to 1024 bytes.
    :ivar file_properties.PropertyFieldTemplate.type: Data type of the value of
        this property field. This type will be enforced upon property creation
        and modifications.
    """

    __slots__ = [
        '_name_value',
        '_description_value',
        '_type_value',
    ]

    _has_required_fields = True

    def __init__(self,
                 name=None,
                 description=None,
                 type=None):
        self._name_value = bb.NOT_SET
        self._description_value = bb.NOT_SET
        self._type_value = bb.NOT_SET
        if name is not None:
            self.name = name
        if description is not None:
            self.description = description
        if type is not None:
            self.type = type

    # Instance attribute type: str (validator is set below)
    name = bb.Attribute("name")

    # Instance attribute type: str (validator is set below)
    description = bb.Attribute("description")

    # Instance attribute type: PropertyType (validator is set below)
    type = bb.Attribute("type", user_defined=True)

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(PropertyFieldTemplate, self)._process_custom_annotations(annotation_type, field_path, processor)

PropertyFieldTemplate_validator = bv.Struct(PropertyFieldTemplate)

class PropertyGroup(bb.Struct):
    """
    A subset of the property fields described by the corresponding
    :class:`PropertyGroupTemplate`. Properties are always added to a Dropbox
    file as a :class:`PropertyGroup`. The possible key names and value types in
    this group are defined by the corresponding :class:`PropertyGroupTemplate`.

    :ivar file_properties.PropertyGroup.template_id: A unique identifier for the
        associated template.
    :ivar file_properties.PropertyGroup.fields: The actual properties associated
        with the template. There can be up to 32 property types per template.
    """

    __slots__ = [
        '_template_id_value',
        '_fields_value',
    ]

    _has_required_fields = True

    def __init__(self,
                 template_id=None,
                 fields=None):
        self._template_id_value = bb.NOT_SET
        self._fields_value = bb.NOT_SET
        if template_id is not None:
            self.template_id = template_id
        if fields is not None:
            self.fields = fields

    # Instance attribute type: str (validator is set below)
    template_id = bb.Attribute("template_id")

    # Instance attribute type: list of [PropertyField] (validator is set below)
    fields = bb.Attribute("fields")

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(PropertyGroup, self)._process_custom_annotations(annotation_type, field_path, processor)

PropertyGroup_validator = bv.Struct(PropertyGroup)

class PropertyGroupUpdate(bb.Struct):
    """
    :ivar file_properties.PropertyGroupUpdate.template_id: A unique identifier
        for a property template.
    :ivar file_properties.PropertyGroupUpdate.add_or_update_fields: Property
        fields to update. If the property field already exists, it is updated.
        If the property field doesn't exist, the property group is added.
    :ivar file_properties.PropertyGroupUpdate.remove_fields: Property fields to
        remove (by name), provided they exist.
    """

    __slots__ = [
        '_template_id_value',
        '_add_or_update_fields_value',
        '_remove_fields_value',
    ]

    _has_required_fields = True

    def __init__(self,
                 template_id=None,
                 add_or_update_fields=None,
                 remove_fields=None):
        self._template_id_value = bb.NOT_SET
        self._add_or_update_fields_value = bb.NOT_SET
        self._remove_fields_value = bb.NOT_SET
        if template_id is not None:
            self.template_id = template_id
        if add_or_update_fields is not None:
            self.add_or_update_fields = add_or_update_fields
        if remove_fields is not None:
            self.remove_fields = remove_fields

    # Instance attribute type: str (validator is set below)
    template_id = bb.Attribute("template_id")

    # Instance attribute type: list of [PropertyField] (validator is set below)
    add_or_update_fields = bb.Attribute("add_or_update_fields", nullable=True)

    # Instance attribute type: list of [str] (validator is set below)
    remove_fields = bb.Attribute("remove_fields", nullable=True)

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(PropertyGroupUpdate, self)._process_custom_annotations(annotation_type, field_path, processor)

PropertyGroupUpdate_validator = bv.Struct(PropertyGroupUpdate)

class PropertyType(bb.Union):
    """
    Data type of the given property field added.

    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar file_properties.PropertyType.string: The associated property field
        will be of type string. Unicode is supported.
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    string = None
    # Attribute is overwritten below the class definition
    other = None

    def is_string(self):
        """
        Check if the union tag is ``string``.

        :rtype: bool
        """
        return self._tag == 'string'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(PropertyType, self)._process_custom_annotations(annotation_type, field_path, processor)

PropertyType_validator = bv.Union(PropertyType)

class RemovePropertiesArg(bb.Struct):
    """
    :ivar file_properties.RemovePropertiesArg.path: A unique identifier for the
        file or folder.
    :ivar file_properties.RemovePropertiesArg.property_template_ids: A list of
        identifiers for a template created by
        :meth:`dropbox.dropbox_client.Dropbox.file_properties_templates_add_for_user`
        or
        :meth:`dropbox.dropbox_client.Dropbox.file_properties_templates_add_for_team`.
    """

    __slots__ = [
        '_path_value',
        '_property_template_ids_value',
    ]

    _has_required_fields = True

    def __init__(self,
                 path=None,
                 property_template_ids=None):
        self._path_value = bb.NOT_SET
        self._property_template_ids_value = bb.NOT_SET
        if path is not None:
            self.path = path
        if property_template_ids is not None:
            self.property_template_ids = property_template_ids

    # Instance attribute type: str (validator is set below)
    path = bb.Attribute("path")

    # Instance attribute type: list of [str] (validator is set below)
    property_template_ids = bb.Attribute("property_template_ids")

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(RemovePropertiesArg, self)._process_custom_annotations(annotation_type, field_path, processor)

RemovePropertiesArg_validator = bv.Struct(RemovePropertiesArg)

class RemovePropertiesError(PropertiesError):
    """
    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.
    """

    @classmethod
    def property_group_lookup(cls, val):
        """
        Create an instance of this class set to the ``property_group_lookup``
        tag with value ``val``.

        :param LookUpPropertiesError val:
        :rtype: RemovePropertiesError
        """
        return cls('property_group_lookup', val)

    def is_property_group_lookup(self):
        """
        Check if the union tag is ``property_group_lookup``.

        :rtype: bool
        """
        return self._tag == 'property_group_lookup'

    def get_property_group_lookup(self):
        """
        Only call this if :meth:`is_property_group_lookup` is true.

        :rtype: LookUpPropertiesError
        """
        if not self.is_property_group_lookup():
            raise AttributeError("tag 'property_group_lookup' not set")
        return self._value

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(RemovePropertiesError, self)._process_custom_annotations(annotation_type, field_path, processor)

RemovePropertiesError_validator = bv.Union(RemovePropertiesError)

class RemoveTemplateArg(bb.Struct):
    """
    :ivar file_properties.RemoveTemplateArg.template_id: An identifier for a
        template created by
        :meth:`dropbox.dropbox_client.Dropbox.file_properties_templates_add_for_user`
        or
        :meth:`dropbox.dropbox_client.Dropbox.file_properties_templates_add_for_team`.
    """

    __slots__ = [
        '_template_id_value',
    ]

    _has_required_fields = True

    def __init__(self,
                 template_id=None):
        self._template_id_value = bb.NOT_SET
        if template_id is not None:
            self.template_id = template_id

    # Instance attribute type: str (validator is set below)
    template_id = bb.Attribute("template_id")

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(RemoveTemplateArg, self)._process_custom_annotations(annotation_type, field_path, processor)

RemoveTemplateArg_validator = bv.Struct(RemoveTemplateArg)

class TemplateFilterBase(bb.Union):
    """
    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar list of [str] file_properties.TemplateFilterBase.filter_some: Only
        templates with an ID in the supplied list will be returned (a subset of
        templates will be returned).
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    other = None

    @classmethod
    def filter_some(cls, val):
        """
        Create an instance of this class set to the ``filter_some`` tag with
        value ``val``.

        :param list of [str] val:
        :rtype: TemplateFilterBase
        """
        return cls('filter_some', val)

    def is_filter_some(self):
        """
        Check if the union tag is ``filter_some``.

        :rtype: bool
        """
        return self._tag == 'filter_some'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def get_filter_some(self):
        """
        Only templates with an ID in the supplied list will be returned (a
        subset of templates will be returned).

        Only call this if :meth:`is_filter_some` is true.

        :rtype: list of [str]
        """
        if not self.is_filter_some():
            raise AttributeError("tag 'filter_some' not set")
        return self._value

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(TemplateFilterBase, self)._process_custom_annotations(annotation_type, field_path, processor)

TemplateFilterBase_validator = bv.Union(TemplateFilterBase)

class TemplateFilter(TemplateFilterBase):
    """
    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar file_properties.TemplateFilter.filter_none: No templates will be
        filtered from the result (all templates will be returned).
    """

    # Attribute is overwritten below the class definition
    filter_none = None

    def is_filter_none(self):
        """
        Check if the union tag is ``filter_none``.

        :rtype: bool
        """
        return self._tag == 'filter_none'

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(TemplateFilter, self)._process_custom_annotations(annotation_type, field_path, processor)

TemplateFilter_validator = bv.Union(TemplateFilter)

class TemplateOwnerType(bb.Union):
    """
    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar file_properties.TemplateOwnerType.user: Template will be associated
        with a user.
    :ivar file_properties.TemplateOwnerType.team: Template will be associated
        with a team.
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    user = None
    # Attribute is overwritten below the class definition
    team = None
    # Attribute is overwritten below the class definition
    other = None

    def is_user(self):
        """
        Check if the union tag is ``user``.

        :rtype: bool
        """
        return self._tag == 'user'

    def is_team(self):
        """
        Check if the union tag is ``team``.

        :rtype: bool
        """
        return self._tag == 'team'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(TemplateOwnerType, self)._process_custom_annotations(annotation_type, field_path, processor)

TemplateOwnerType_validator = bv.Union(TemplateOwnerType)

class UpdatePropertiesArg(bb.Struct):
    """
    :ivar file_properties.UpdatePropertiesArg.path: A unique identifier for the
        file or folder.
    :ivar file_properties.UpdatePropertiesArg.update_property_groups: The
        property groups "delta" updates to apply.
    """

    __slots__ = [
        '_path_value',
        '_update_property_groups_value',
    ]

    _has_required_fields = True

    def __init__(self,
                 path=None,
                 update_property_groups=None):
        self._path_value = bb.NOT_SET
        self._update_property_groups_value = bb.NOT_SET
        if path is not None:
            self.path = path
        if update_property_groups is not None:
            self.update_property_groups = update_property_groups

    # Instance attribute type: str (validator is set below)
    path = bb.Attribute("path")

    # Instance attribute type: list of [PropertyGroupUpdate] (validator is set below)
    update_property_groups = bb.Attribute("update_property_groups")

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(UpdatePropertiesArg, self)._process_custom_annotations(annotation_type, field_path, processor)

UpdatePropertiesArg_validator = bv.Struct(UpdatePropertiesArg)

class UpdatePropertiesError(InvalidPropertyGroupError):
    """
    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.
    """

    @classmethod
    def property_group_lookup(cls, val):
        """
        Create an instance of this class set to the ``property_group_lookup``
        tag with value ``val``.

        :param LookUpPropertiesError val:
        :rtype: UpdatePropertiesError
        """
        return cls('property_group_lookup', val)

    def is_property_group_lookup(self):
        """
        Check if the union tag is ``property_group_lookup``.

        :rtype: bool
        """
        return self._tag == 'property_group_lookup'

    def get_property_group_lookup(self):
        """
        Only call this if :meth:`is_property_group_lookup` is true.

        :rtype: LookUpPropertiesError
        """
        if not self.is_property_group_lookup():
            raise AttributeError("tag 'property_group_lookup' not set")
        return self._value

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(UpdatePropertiesError, self)._process_custom_annotations(annotation_type, field_path, processor)

UpdatePropertiesError_validator = bv.Union(UpdatePropertiesError)

class UpdateTemplateArg(bb.Struct):
    """
    :ivar file_properties.UpdateTemplateArg.template_id: An identifier for
        template added by  See
        :meth:`dropbox.dropbox_client.Dropbox.file_properties_templates_add_for_user`
        or
        :meth:`dropbox.dropbox_client.Dropbox.file_properties_templates_add_for_team`.
    :ivar file_properties.UpdateTemplateArg.name: A display name for the
        template. template names can be up to 256 bytes.
    :ivar file_properties.UpdateTemplateArg.description: Description for the new
        template. Template descriptions can be up to 1024 bytes.
    :ivar file_properties.UpdateTemplateArg.add_fields: Property field templates
        to be added to the group template. There can be up to 32 properties in a
        single template.
    """

    __slots__ = [
        '_template_id_value',
        '_name_value',
        '_description_value',
        '_add_fields_value',
    ]

    _has_required_fields = True

    def __init__(self,
                 template_id=None,
                 name=None,
                 description=None,
                 add_fields=None):
        self._template_id_value = bb.NOT_SET
        self._name_value = bb.NOT_SET
        self._description_value = bb.NOT_SET
        self._add_fields_value = bb.NOT_SET
        if template_id is not None:
            self.template_id = template_id
        if name is not None:
            self.name = name
        if description is not None:
            self.description = description
        if add_fields is not None:
            self.add_fields = add_fields

    # Instance attribute type: str (validator is set below)
    template_id = bb.Attribute("template_id")

    # Instance attribute type: str (validator is set below)
    name = bb.Attribute("name", nullable=True)

    # Instance attribute type: str (validator is set below)
    description = bb.Attribute("description", nullable=True)

    # Instance attribute type: list of [PropertyFieldTemplate] (validator is set below)
    add_fields = bb.Attribute("add_fields", nullable=True)

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(UpdateTemplateArg, self)._process_custom_annotations(annotation_type, field_path, processor)

UpdateTemplateArg_validator = bv.Struct(UpdateTemplateArg)

class UpdateTemplateResult(bb.Struct):
    """
    :ivar file_properties.UpdateTemplateResult.template_id: An identifier for
        template added by route  See
        :meth:`dropbox.dropbox_client.Dropbox.file_properties_templates_add_for_user`
        or
        :meth:`dropbox.dropbox_client.Dropbox.file_properties_templates_add_for_team`.
    """

    __slots__ = [
        '_template_id_value',
    ]

    _has_required_fields = True

    def __init__(self,
                 template_id=None):
        self._template_id_value = bb.NOT_SET
        if template_id is not None:
            self.template_id = template_id

    # Instance attribute type: str (validator is set below)
    template_id = bb.Attribute("template_id")

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(UpdateTemplateResult, self)._process_custom_annotations(annotation_type, field_path, processor)

UpdateTemplateResult_validator = bv.Struct(UpdateTemplateResult)

Id_validator = bv.String(min_length=1)
PathOrId_validator = bv.String(pattern='/(.|[\\r\\n])*|id:.*|(ns:[0-9]+(/.*)?)')
PropertiesSearchCursor_validator = bv.String(min_length=1)
TemplateId_validator = bv.String(min_length=1, pattern='(/|ptid:).*')
AddPropertiesArg.path.validator = PathOrId_validator
AddPropertiesArg.property_groups.validator = bv.List(PropertyGroup_validator)
AddPropertiesArg._all_field_names_ = set([
    'path',
    'property_groups',
])
AddPropertiesArg._all_fields_ = [
    ('path', AddPropertiesArg.path.validator),
    ('property_groups', AddPropertiesArg.property_groups.validator),
]

TemplateError._template_not_found_validator = TemplateId_validator
TemplateError._restricted_content_validator = bv.Void()
TemplateError._other_validator = bv.Void()
TemplateError._tagmap = {
    'template_not_found': TemplateError._template_not_found_validator,
    'restricted_content': TemplateError._restricted_content_validator,
    'other': TemplateError._other_validator,
}

TemplateError.restricted_content = TemplateError('restricted_content')
TemplateError.other = TemplateError('other')

PropertiesError._path_validator = LookupError_validator
PropertiesError._unsupported_folder_validator = bv.Void()
PropertiesError._tagmap = {
    'path': PropertiesError._path_validator,
    'unsupported_folder': PropertiesError._unsupported_folder_validator,
}
PropertiesError._tagmap.update(TemplateError._tagmap)

PropertiesError.unsupported_folder = PropertiesError('unsupported_folder')

InvalidPropertyGroupError._property_field_too_large_validator = bv.Void()
InvalidPropertyGroupError._does_not_fit_template_validator = bv.Void()
InvalidPropertyGroupError._duplicate_property_groups_validator = bv.Void()
InvalidPropertyGroupError._tagmap = {
    'property_field_too_large': InvalidPropertyGroupError._property_field_too_large_validator,
    'does_not_fit_template': InvalidPropertyGroupError._does_not_fit_template_validator,
    'duplicate_property_groups': InvalidPropertyGroupError._duplicate_property_groups_validator,
}
InvalidPropertyGroupError._tagmap.update(PropertiesError._tagmap)

InvalidPropertyGroupError.property_field_too_large = InvalidPropertyGroupError('property_field_too_large')
InvalidPropertyGroupError.does_not_fit_template = InvalidPropertyGroupError('does_not_fit_template')
InvalidPropertyGroupError.duplicate_property_groups = InvalidPropertyGroupError('duplicate_property_groups')

AddPropertiesError._property_group_already_exists_validator = bv.Void()
AddPropertiesError._tagmap = {
    'property_group_already_exists': AddPropertiesError._property_group_already_exists_validator,
}
AddPropertiesError._tagmap.update(InvalidPropertyGroupError._tagmap)

AddPropertiesError.property_group_already_exists = AddPropertiesError('property_group_already_exists')

PropertyGroupTemplate.name.validator = bv.String()
PropertyGroupTemplate.description.validator = bv.String()
PropertyGroupTemplate.fields.validator = bv.List(PropertyFieldTemplate_validator)
PropertyGroupTemplate._all_field_names_ = set([
    'name',
    'description',
    'fields',
])
PropertyGroupTemplate._all_fields_ = [
    ('name', PropertyGroupTemplate.name.validator),
    ('description', PropertyGroupTemplate.description.validator),
    ('fields', PropertyGroupTemplate.fields.validator),
]

AddTemplateArg._all_field_names_ = PropertyGroupTemplate._all_field_names_.union(set([]))
AddTemplateArg._all_fields_ = PropertyGroupTemplate._all_fields_ + []

AddTemplateResult.template_id.validator = TemplateId_validator
AddTemplateResult._all_field_names_ = set(['template_id'])
AddTemplateResult._all_fields_ = [('template_id', AddTemplateResult.template_id.validator)]

GetTemplateArg.template_id.validator = TemplateId_validator
GetTemplateArg._all_field_names_ = set(['template_id'])
GetTemplateArg._all_fields_ = [('template_id', GetTemplateArg.template_id.validator)]

GetTemplateResult._all_field_names_ = PropertyGroupTemplate._all_field_names_.union(set([]))
GetTemplateResult._all_fields_ = PropertyGroupTemplate._all_fields_ + []

ListTemplateResult.template_ids.validator = bv.List(TemplateId_validator)
ListTemplateResult._all_field_names_ = set(['template_ids'])
ListTemplateResult._all_fields_ = [('template_ids', ListTemplateResult.template_ids.validator)]

LogicalOperator._or_operator_validator = bv.Void()
LogicalOperator._other_validator = bv.Void()
LogicalOperator._tagmap = {
    'or_operator': LogicalOperator._or_operator_validator,
    'other': LogicalOperator._other_validator,
}

LogicalOperator.or_operator = LogicalOperator('or_operator')
LogicalOperator.other = LogicalOperator('other')

LookUpPropertiesError._property_group_not_found_validator = bv.Void()
LookUpPropertiesError._other_validator = bv.Void()
LookUpPropertiesError._tagmap = {
    'property_group_not_found': LookUpPropertiesError._property_group_not_found_validator,
    'other': LookUpPropertiesError._other_validator,
}

LookUpPropertiesError.property_group_not_found = LookUpPropertiesError('property_group_not_found')
LookUpPropertiesError.other = LookUpPropertiesError('other')

LookupError._malformed_path_validator = bv.String()
LookupError._not_found_validator = bv.Void()
LookupError._not_file_validator = bv.Void()
LookupError._not_folder_validator = bv.Void()
LookupError._restricted_content_validator = bv.Void()
LookupError._other_validator = bv.Void()
LookupError._tagmap = {
    'malformed_path': LookupError._malformed_path_validator,
    'not_found': LookupError._not_found_validator,
    'not_file': LookupError._not_file_validator,
    'not_folder': LookupError._not_folder_validator,
    'restricted_content': LookupError._restricted_content_validator,
    'other': LookupError._other_validator,
}

LookupError.not_found = LookupError('not_found')
LookupError.not_file = LookupError('not_file')
LookupError.not_folder = LookupError('not_folder')
LookupError.restricted_content = LookupError('restricted_content')
LookupError.other = LookupError('other')

ModifyTemplateError._conflicting_property_names_validator = bv.Void()
ModifyTemplateError._too_many_properties_validator = bv.Void()
ModifyTemplateError._too_many_templates_validator = bv.Void()
ModifyTemplateError._template_attribute_too_large_validator = bv.Void()
ModifyTemplateError._tagmap = {
    'conflicting_property_names': ModifyTemplateError._conflicting_property_names_validator,
    'too_many_properties': ModifyTemplateError._too_many_properties_validator,
    'too_many_templates': ModifyTemplateError._too_many_templates_validator,
    'template_attribute_too_large': ModifyTemplateError._template_attribute_too_large_validator,
}
ModifyTemplateError._tagmap.update(TemplateError._tagmap)

ModifyTemplateError.conflicting_property_names = ModifyTemplateError('conflicting_property_names')
ModifyTemplateError.too_many_properties = ModifyTemplateError('too_many_properties')
ModifyTemplateError.too_many_templates = ModifyTemplateError('too_many_templates')
ModifyTemplateError.template_attribute_too_large = ModifyTemplateError('template_attribute_too_large')

OverwritePropertyGroupArg.path.validator = PathOrId_validator
OverwritePropertyGroupArg.property_groups.validator = bv.List(PropertyGroup_validator, min_items=1)
OverwritePropertyGroupArg._all_field_names_ = set([
    'path',
    'property_groups',
])
OverwritePropertyGroupArg._all_fields_ = [
    ('path', OverwritePropertyGroupArg.path.validator),
    ('property_groups', OverwritePropertyGroupArg.property_groups.validator),
]

PropertiesSearchArg.queries.validator = bv.List(PropertiesSearchQuery_validator, min_items=1)
PropertiesSearchArg.template_filter.validator = TemplateFilter_validator
PropertiesSearchArg._all_field_names_ = set([
    'queries',
    'template_filter',
])
PropertiesSearchArg._all_fields_ = [
    ('queries', PropertiesSearchArg.queries.validator),
    ('template_filter', PropertiesSearchArg.template_filter.validator),
]

PropertiesSearchContinueArg.cursor.validator = PropertiesSearchCursor_validator
PropertiesSearchContinueArg._all_field_names_ = set(['cursor'])
PropertiesSearchContinueArg._all_fields_ = [('cursor', PropertiesSearchContinueArg.cursor.validator)]

PropertiesSearchContinueError._reset_validator = bv.Void()
PropertiesSearchContinueError._other_validator = bv.Void()
PropertiesSearchContinueError._tagmap = {
    'reset': PropertiesSearchContinueError._reset_validator,
    'other': PropertiesSearchContinueError._other_validator,
}

PropertiesSearchContinueError.reset = PropertiesSearchContinueError('reset')
PropertiesSearchContinueError.other = PropertiesSearchContinueError('other')

PropertiesSearchError._property_group_lookup_validator = LookUpPropertiesError_validator
PropertiesSearchError._other_validator = bv.Void()
PropertiesSearchError._tagmap = {
    'property_group_lookup': PropertiesSearchError._property_group_lookup_validator,
    'other': PropertiesSearchError._other_validator,
}

PropertiesSearchError.other = PropertiesSearchError('other')

PropertiesSearchMatch.id.validator = Id_validator
PropertiesSearchMatch.path.validator = bv.String()
PropertiesSearchMatch.is_deleted.validator = bv.Boolean()
PropertiesSearchMatch.property_groups.validator = bv.List(PropertyGroup_validator)
PropertiesSearchMatch._all_field_names_ = set([
    'id',
    'path',
    'is_deleted',
    'property_groups',
])
PropertiesSearchMatch._all_fields_ = [
    ('id', PropertiesSearchMatch.id.validator),
    ('path', PropertiesSearchMatch.path.validator),
    ('is_deleted', PropertiesSearchMatch.is_deleted.validator),
    ('property_groups', PropertiesSearchMatch.property_groups.validator),
]

PropertiesSearchMode._field_name_validator = bv.String()
PropertiesSearchMode._other_validator = bv.Void()
PropertiesSearchMode._tagmap = {
    'field_name': PropertiesSearchMode._field_name_validator,
    'other': PropertiesSearchMode._other_validator,
}

PropertiesSearchMode.other = PropertiesSearchMode('other')

PropertiesSearchQuery.query.validator = bv.String()
PropertiesSearchQuery.mode.validator = PropertiesSearchMode_validator
PropertiesSearchQuery.logical_operator.validator = LogicalOperator_validator
PropertiesSearchQuery._all_field_names_ = set([
    'query',
    'mode',
    'logical_operator',
])
PropertiesSearchQuery._all_fields_ = [
    ('query', PropertiesSearchQuery.query.validator),
    ('mode', PropertiesSearchQuery.mode.validator),
    ('logical_operator', PropertiesSearchQuery.logical_operator.validator),
]

PropertiesSearchResult.matches.validator = bv.List(PropertiesSearchMatch_validator)
PropertiesSearchResult.cursor.validator = bv.Nullable(PropertiesSearchCursor_validator)
PropertiesSearchResult._all_field_names_ = set([
    'matches',
    'cursor',
])
PropertiesSearchResult._all_fields_ = [
    ('matches', PropertiesSearchResult.matches.validator),
    ('cursor', PropertiesSearchResult.cursor.validator),
]

PropertyField.name.validator = bv.String()
PropertyField.value.validator = bv.String()
PropertyField._all_field_names_ = set([
    'name',
    'value',
])
PropertyField._all_fields_ = [
    ('name', PropertyField.name.validator),
    ('value', PropertyField.value.validator),
]

PropertyFieldTemplate.name.validator = bv.String()
PropertyFieldTemplate.description.validator = bv.String()
PropertyFieldTemplate.type.validator = PropertyType_validator
PropertyFieldTemplate._all_field_names_ = set([
    'name',
    'description',
    'type',
])
PropertyFieldTemplate._all_fields_ = [
    ('name', PropertyFieldTemplate.name.validator),
    ('description', PropertyFieldTemplate.description.validator),
    ('type', PropertyFieldTemplate.type.validator),
]

PropertyGroup.template_id.validator = TemplateId_validator
PropertyGroup.fields.validator = bv.List(PropertyField_validator)
PropertyGroup._all_field_names_ = set([
    'template_id',
    'fields',
])
PropertyGroup._all_fields_ = [
    ('template_id', PropertyGroup.template_id.validator),
    ('fields', PropertyGroup.fields.validator),
]

PropertyGroupUpdate.template_id.validator = TemplateId_validator
PropertyGroupUpdate.add_or_update_fields.validator = bv.Nullable(bv.List(PropertyField_validator))
PropertyGroupUpdate.remove_fields.validator = bv.Nullable(bv.List(bv.String()))
PropertyGroupUpdate._all_field_names_ = set([
    'template_id',
    'add_or_update_fields',
    'remove_fields',
])
PropertyGroupUpdate._all_fields_ = [
    ('template_id', PropertyGroupUpdate.template_id.validator),
    ('add_or_update_fields', PropertyGroupUpdate.add_or_update_fields.validator),
    ('remove_fields', PropertyGroupUpdate.remove_fields.validator),
]

PropertyType._string_validator = bv.Void()
PropertyType._other_validator = bv.Void()
PropertyType._tagmap = {
    'string': PropertyType._string_validator,
    'other': PropertyType._other_validator,
}

PropertyType.string = PropertyType('string')
PropertyType.other = PropertyType('other')

RemovePropertiesArg.path.validator = PathOrId_validator
RemovePropertiesArg.property_template_ids.validator = bv.List(TemplateId_validator)
RemovePropertiesArg._all_field_names_ = set([
    'path',
    'property_template_ids',
])
RemovePropertiesArg._all_fields_ = [
    ('path', RemovePropertiesArg.path.validator),
    ('property_template_ids', RemovePropertiesArg.property_template_ids.validator),
]

RemovePropertiesError._property_group_lookup_validator = LookUpPropertiesError_validator
RemovePropertiesError._tagmap = {
    'property_group_lookup': RemovePropertiesError._property_group_lookup_validator,
}
RemovePropertiesError._tagmap.update(PropertiesError._tagmap)

RemoveTemplateArg.template_id.validator = TemplateId_validator
RemoveTemplateArg._all_field_names_ = set(['template_id'])
RemoveTemplateArg._all_fields_ = [('template_id', RemoveTemplateArg.template_id.validator)]

TemplateFilterBase._filter_some_validator = bv.List(TemplateId_validator, min_items=1)
TemplateFilterBase._other_validator = bv.Void()
TemplateFilterBase._tagmap = {
    'filter_some': TemplateFilterBase._filter_some_validator,
    'other': TemplateFilterBase._other_validator,
}

TemplateFilterBase.other = TemplateFilterBase('other')

TemplateFilter._filter_none_validator = bv.Void()
TemplateFilter._tagmap = {
    'filter_none': TemplateFilter._filter_none_validator,
}
TemplateFilter._tagmap.update(TemplateFilterBase._tagmap)

TemplateFilter.filter_none = TemplateFilter('filter_none')

TemplateOwnerType._user_validator = bv.Void()
TemplateOwnerType._team_validator = bv.Void()
TemplateOwnerType._other_validator = bv.Void()
TemplateOwnerType._tagmap = {
    'user': TemplateOwnerType._user_validator,
    'team': TemplateOwnerType._team_validator,
    'other': TemplateOwnerType._other_validator,
}

TemplateOwnerType.user = TemplateOwnerType('user')
TemplateOwnerType.team = TemplateOwnerType('team')
TemplateOwnerType.other = TemplateOwnerType('other')

UpdatePropertiesArg.path.validator = PathOrId_validator
UpdatePropertiesArg.update_property_groups.validator = bv.List(PropertyGroupUpdate_validator)
UpdatePropertiesArg._all_field_names_ = set([
    'path',
    'update_property_groups',
])
UpdatePropertiesArg._all_fields_ = [
    ('path', UpdatePropertiesArg.path.validator),
    ('update_property_groups', UpdatePropertiesArg.update_property_groups.validator),
]

UpdatePropertiesError._property_group_lookup_validator = LookUpPropertiesError_validator
UpdatePropertiesError._tagmap = {
    'property_group_lookup': UpdatePropertiesError._property_group_lookup_validator,
}
UpdatePropertiesError._tagmap.update(InvalidPropertyGroupError._tagmap)

UpdateTemplateArg.template_id.validator = TemplateId_validator
UpdateTemplateArg.name.validator = bv.Nullable(bv.String())
UpdateTemplateArg.description.validator = bv.Nullable(bv.String())
UpdateTemplateArg.add_fields.validator = bv.Nullable(bv.List(PropertyFieldTemplate_validator))
UpdateTemplateArg._all_field_names_ = set([
    'template_id',
    'name',
    'description',
    'add_fields',
])
UpdateTemplateArg._all_fields_ = [
    ('template_id', UpdateTemplateArg.template_id.validator),
    ('name', UpdateTemplateArg.name.validator),
    ('description', UpdateTemplateArg.description.validator),
    ('add_fields', UpdateTemplateArg.add_fields.validator),
]

UpdateTemplateResult.template_id.validator = TemplateId_validator
UpdateTemplateResult._all_field_names_ = set(['template_id'])
UpdateTemplateResult._all_fields_ = [('template_id', UpdateTemplateResult.template_id.validator)]

PropertiesSearchArg.template_filter.default = TemplateFilter.filter_none
PropertiesSearchQuery.logical_operator.default = LogicalOperator.or_operator
properties_add = bb.Route(
    'properties/add',
    1,
    False,
    AddPropertiesArg_validator,
    bv.Void(),
    AddPropertiesError_validator,
    {'auth': 'user',
     'host': 'api',
     'style': 'rpc'},
)
properties_overwrite = bb.Route(
    'properties/overwrite',
    1,
    False,
    OverwritePropertyGroupArg_validator,
    bv.Void(),
    InvalidPropertyGroupError_validator,
    {'auth': 'user',
     'host': 'api',
     'style': 'rpc'},
)
properties_remove = bb.Route(
    'properties/remove',
    1,
    False,
    RemovePropertiesArg_validator,
    bv.Void(),
    RemovePropertiesError_validator,
    {'auth': 'user',
     'host': 'api',
     'style': 'rpc'},
)
properties_search = bb.Route(
    'properties/search',
    1,
    False,
    PropertiesSearchArg_validator,
    PropertiesSearchResult_validator,
    PropertiesSearchError_validator,
    {'auth': 'user',
     'host': 'api',
     'style': 'rpc'},
)
properties_search_continue = bb.Route(
    'properties/search/continue',
    1,
    False,
    PropertiesSearchContinueArg_validator,
    PropertiesSearchResult_validator,
    PropertiesSearchContinueError_validator,
    {'auth': 'user',
     'host': 'api',
     'style': 'rpc'},
)
properties_update = bb.Route(
    'properties/update',
    1,
    False,
    UpdatePropertiesArg_validator,
    bv.Void(),
    UpdatePropertiesError_validator,
    {'auth': 'user',
     'host': 'api',
     'style': 'rpc'},
)
templates_add_for_team = bb.Route(
    'templates/add_for_team',
    1,
    False,
    AddTemplateArg_validator,
    AddTemplateResult_validator,
    ModifyTemplateError_validator,
    {'auth': 'team',
     'host': 'api',
     'style': 'rpc'},
)
templates_add_for_user = bb.Route(
    'templates/add_for_user',
    1,
    False,
    AddTemplateArg_validator,
    AddTemplateResult_validator,
    ModifyTemplateError_validator,
    {'auth': 'user',
     'host': 'api',
     'style': 'rpc'},
)
templates_get_for_team = bb.Route(
    'templates/get_for_team',
    1,
    False,
    GetTemplateArg_validator,
    GetTemplateResult_validator,
    TemplateError_validator,
    {'auth': 'team',
     'host': 'api',
     'style': 'rpc'},
)
templates_get_for_user = bb.Route(
    'templates/get_for_user',
    1,
    False,
    GetTemplateArg_validator,
    GetTemplateResult_validator,
    TemplateError_validator,
    {'auth': 'user',
     'host': 'api',
     'style': 'rpc'},
)
templates_list_for_team = bb.Route(
    'templates/list_for_team',
    1,
    False,
    bv.Void(),
    ListTemplateResult_validator,
    TemplateError_validator,
    {'auth': 'team',
     'host': 'api',
     'style': 'rpc'},
)
templates_list_for_user = bb.Route(
    'templates/list_for_user',
    1,
    False,
    bv.Void(),
    ListTemplateResult_validator,
    TemplateError_validator,
    {'auth': 'user',
     'host': 'api',
     'style': 'rpc'},
)
templates_remove_for_team = bb.Route(
    'templates/remove_for_team',
    1,
    False,
    RemoveTemplateArg_validator,
    bv.Void(),
    TemplateError_validator,
    {'auth': 'team',
     'host': 'api',
     'style': 'rpc'},
)
templates_remove_for_user = bb.Route(
    'templates/remove_for_user',
    1,
    False,
    RemoveTemplateArg_validator,
    bv.Void(),
    TemplateError_validator,
    {'auth': 'user',
     'host': 'api',
     'style': 'rpc'},
)
templates_update_for_team = bb.Route(
    'templates/update_for_team',
    1,
    False,
    UpdateTemplateArg_validator,
    UpdateTemplateResult_validator,
    ModifyTemplateError_validator,
    {'auth': 'team',
     'host': 'api',
     'style': 'rpc'},
)
templates_update_for_user = bb.Route(
    'templates/update_for_user',
    1,
    False,
    UpdateTemplateArg_validator,
    UpdateTemplateResult_validator,
    ModifyTemplateError_validator,
    {'auth': 'user',
     'host': 'api',
     'style': 'rpc'},
)

ROUTES = {
    'properties/add': properties_add,
    'properties/overwrite': properties_overwrite,
    'properties/remove': properties_remove,
    'properties/search': properties_search,
    'properties/search/continue': properties_search_continue,
    'properties/update': properties_update,
    'templates/add_for_team': templates_add_for_team,
    'templates/add_for_user': templates_add_for_user,
    'templates/get_for_team': templates_get_for_team,
    'templates/get_for_user': templates_get_for_user,
    'templates/list_for_team': templates_list_for_team,
    'templates/list_for_user': templates_list_for_user,
    'templates/remove_for_team': templates_remove_for_team,
    'templates/remove_for_user': templates_remove_for_user,
    'templates/update_for_team': templates_update_for_team,
    'templates/update_for_user': templates_update_for_user,
}

