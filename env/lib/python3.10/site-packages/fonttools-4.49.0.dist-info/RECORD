../../../bin/fonttools,sha256=amQqCemYr3Evlmr8sVFSGj0YKsL5bxx-kIOCnUlcy7k,243
../../../bin/pyftmerge,sha256=MT1_UMO230Fl2rv3CVRuhwTwWibK8Fhz2pfKfc55NgU,240
../../../bin/pyftsubset,sha256=iCfFUY2t3qIGXszC6jQxhDtPqmTLzDG0Jh3n1aEmTrE,241
../../../bin/ttx,sha256=MS8YrUS2NFAJbImM4aHCEDG3wE7kx0HvXMYT4vsgok8,238
../../../share/man/man1/ttx.1,sha256=cLbm_pOOj1C76T2QXvDxzwDj9gk-GTd5RztvTMsouFw,5377
fontTools/__init__.py,sha256=RzZ0p_m7gjDfcye4ShXq4pipJgg0SYA15LxMUqblN5s,183
fontTools/__main__.py,sha256=VjkGh1UD-i1zTDA1dXo1uecSs6PxHdGQ5vlCk_mCCYs,925
fontTools/__pycache__/__init__.cpython-310.pyc,,
fontTools/__pycache__/__main__.cpython-310.pyc,,
fontTools/__pycache__/afmLib.cpython-310.pyc,,
fontTools/__pycache__/agl.cpython-310.pyc,,
fontTools/__pycache__/fontBuilder.cpython-310.pyc,,
fontTools/__pycache__/help.cpython-310.pyc,,
fontTools/__pycache__/tfmLib.cpython-310.pyc,,
fontTools/__pycache__/ttx.cpython-310.pyc,,
fontTools/__pycache__/unicode.cpython-310.pyc,,
fontTools/afmLib.py,sha256=1MagIItOzRV4vV5kKPxeDZbPJsfxLB3wdHLFkQvl0uk,13164
fontTools/agl.py,sha256=05bm8Uq45uVWW8nPbP6xbNgmFyxQr8sWhYAiP0VSjnI,112975
fontTools/cffLib/__init__.py,sha256=ndoZbmobnniaH9dQa4I7sIzKAjt0gpY9Z7PEdM0oM8A,114360
fontTools/cffLib/__pycache__/__init__.cpython-310.pyc,,
fontTools/cffLib/__pycache__/specializer.cpython-310.pyc,,
fontTools/cffLib/__pycache__/width.cpython-310.pyc,,
fontTools/cffLib/specializer.py,sha256=5wToOz7X9jIyz3SdvPmpABr5lHHDNNCC1qKUUkKTi8o,30652
fontTools/cffLib/width.py,sha256=pm1LKz28n27GHuGx4U9NYs3lnzfPBUleqbabRPq3VD8,6035
fontTools/colorLib/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
fontTools/colorLib/__pycache__/__init__.cpython-310.pyc,,
fontTools/colorLib/__pycache__/builder.cpython-310.pyc,,
fontTools/colorLib/__pycache__/errors.cpython-310.pyc,,
fontTools/colorLib/__pycache__/geometry.cpython-310.pyc,,
fontTools/colorLib/__pycache__/table_builder.cpython-310.pyc,,
fontTools/colorLib/__pycache__/unbuilder.cpython-310.pyc,,
fontTools/colorLib/builder.py,sha256=kmO7OuudQQb3fEOS7aLzgTDVjqS9i2xIQmk9p1uBe8A,23008
fontTools/colorLib/errors.py,sha256=CsaviiRxxrpgVX4blm7KCyK8553ljwL44xkJOeC5U7U,41
fontTools/colorLib/geometry.py,sha256=3ScySrR2YDJa7d5K5_xM5Yt1-3NCV-ry8ikYA5VwVbI,5518
fontTools/colorLib/table_builder.py,sha256=ZeltWY6n-YPiJv_hQ1iBXoEFAG70EKxZyScgsMKUFGU,7469
fontTools/colorLib/unbuilder.py,sha256=iW-E5I39WsV82K3NgCO4Cjzwm1WqzGrtypHt8epwbHM,2142
fontTools/config/__init__.py,sha256=Ti5jpozjMqp5qhnrmwNcWI6b9uvHzhZlbWXHTqVZlGI,2643
fontTools/config/__pycache__/__init__.cpython-310.pyc,,
fontTools/cu2qu/__init__.py,sha256=Cuc7Uglb0nSgaraTxXY5J8bReznH5wApW0uakN7MycY,618
fontTools/cu2qu/__main__.py,sha256=IWCEMB0jcIxofHb_8NvVLmNBVRK9ZU8TQhN6TNAGrfE,83
fontTools/cu2qu/__pycache__/__init__.cpython-310.pyc,,
fontTools/cu2qu/__pycache__/__main__.cpython-310.pyc,,
fontTools/cu2qu/__pycache__/benchmark.cpython-310.pyc,,
fontTools/cu2qu/__pycache__/cli.cpython-310.pyc,,
fontTools/cu2qu/__pycache__/cu2qu.cpython-310.pyc,,
fontTools/cu2qu/__pycache__/errors.cpython-310.pyc,,
fontTools/cu2qu/__pycache__/ufo.cpython-310.pyc,,
fontTools/cu2qu/benchmark.py,sha256=M3Dix_peO6d4Nsq4SAiAO2bQGeNnZNordYAOk8hkHkw,1349
fontTools/cu2qu/cli.py,sha256=LcwNAc4rh3ufJEfZZoO6j-XTtAgg9MiQ0acZi16gFXA,6075
fontTools/cu2qu/cu2qu.c,sha256=YH1MhaSY-gUHZ4186iRdGPah8t0Jv4PgRG-LJjtTuQs,594414
fontTools/cu2qu/cu2qu.cpython-310-x86_64-linux-gnu.so,sha256=LGo3aAPg7iaOICyNlkCL-2qjYuRYXNTkWm6hBXpM-gQ,975280
fontTools/cu2qu/cu2qu.py,sha256=UIFGlFq9X6Pj_NuaXg7KWIzLyR1jnx7nMCX-hFVG0SQ,16466
fontTools/cu2qu/errors.py,sha256=PyJNMy8lHDtKpfFkc0nkM8F4jNLZAC4lPQCN1Km4bpg,2441
fontTools/cu2qu/ufo.py,sha256=Da-NkcdY87ImmFIe4wRTFBR5SD-6EV4O7B78rHpdo3M,11768
fontTools/designspaceLib/__init__.py,sha256=qSce6J0xR0dOffmtODLS75UmxQV3CnseSJojt9T2Ugw,129250
fontTools/designspaceLib/__main__.py,sha256=xhtYXo1T1tsykhQDD0tcconSNYgWL5hoTBORpVDUYrc,103
fontTools/designspaceLib/__pycache__/__init__.cpython-310.pyc,,
fontTools/designspaceLib/__pycache__/__main__.cpython-310.pyc,,
fontTools/designspaceLib/__pycache__/split.cpython-310.pyc,,
fontTools/designspaceLib/__pycache__/statNames.cpython-310.pyc,,
fontTools/designspaceLib/__pycache__/types.cpython-310.pyc,,
fontTools/designspaceLib/split.py,sha256=FB1NuvhUO453UXveQZi9oyrW_caoCPM3RADp1rYWkDs,19239
fontTools/designspaceLib/statNames.py,sha256=lDqFxZAKSbpMuLsgbK6XtyHA5lqLyAK0t561wsSWmaM,9069
fontTools/designspaceLib/types.py,sha256=ofK65qXNADqcpl7zI72Pa5s07-cm7G41iEmLVV44-Es,5320
fontTools/encodings/MacRoman.py,sha256=4vEooUDm2gLCG8KIIDhRxm5-A64w7XrhP9cjDRr2Eo0,3576
fontTools/encodings/StandardEncoding.py,sha256=Eo3AGE8FE_p-IVYYuV097KouSsF3UrXoRRN0XyvYbrs,3581
fontTools/encodings/__init__.py,sha256=DJBWmoX_Haau7qlgmvWyfbhSzrX2qL636Rns7CG01pk,75
fontTools/encodings/__pycache__/MacRoman.cpython-310.pyc,,
fontTools/encodings/__pycache__/StandardEncoding.cpython-310.pyc,,
fontTools/encodings/__pycache__/__init__.cpython-310.pyc,,
fontTools/encodings/__pycache__/codecs.cpython-310.pyc,,
fontTools/encodings/codecs.py,sha256=u50ruwz9fcRsrUrRGpR17Cr55Ovn1fvCHCKrElVumDE,4721
fontTools/feaLib/__init__.py,sha256=jlIru2ghxvb1HhC5Je2BCXjFJmFQlYKpruorPoz3BvQ,213
fontTools/feaLib/__main__.py,sha256=Df2PA6LXwna98lSXiL7R4as_ZEdWCIk3egSM5w7GpvM,2240
fontTools/feaLib/__pycache__/__init__.cpython-310.pyc,,
fontTools/feaLib/__pycache__/__main__.cpython-310.pyc,,
fontTools/feaLib/__pycache__/ast.cpython-310.pyc,,
fontTools/feaLib/__pycache__/builder.cpython-310.pyc,,
fontTools/feaLib/__pycache__/error.cpython-310.pyc,,
fontTools/feaLib/__pycache__/lexer.cpython-310.pyc,,
fontTools/feaLib/__pycache__/location.cpython-310.pyc,,
fontTools/feaLib/__pycache__/lookupDebugInfo.cpython-310.pyc,,
fontTools/feaLib/__pycache__/parser.cpython-310.pyc,,
fontTools/feaLib/__pycache__/variableScalar.cpython-310.pyc,,
fontTools/feaLib/ast.py,sha256=_27skibzPidJtI5lUFeVjEv5NVaNPbuz4u8oZfMuxMk,73801
fontTools/feaLib/builder.py,sha256=1ND1iQvxHQn5eLU-5bwCq-dpeHiv1JPaQmX15bJS2Gg,69242
fontTools/feaLib/error.py,sha256=Tq2dZUlCOyLfjTr3qibsT2g9t-S_JEf6bKgyNX55oCE,643
fontTools/feaLib/lexer.c,sha256=7XJXAH6DjP964ktNvFRaLkhSa4V4KIblP_T-2SmFR68,752261
fontTools/feaLib/lexer.cpython-310-x86_64-linux-gnu.so,sha256=bqD7oLwjy8tFV_XF3ighqPiYUadOYnagv9sSlCOiYo8,1354208
fontTools/feaLib/lexer.py,sha256=vKJiI1RVDRmYmdbuXA2NmcAOn8vDJPtiZZ7SfNGdfJ0,11117
fontTools/feaLib/location.py,sha256=JXzHqGV56EHdcq823AwA5oaK05hf_1ySWpScbo3zGC0,234
fontTools/feaLib/lookupDebugInfo.py,sha256=gVRr5-APWfT_a5-25hRuawSVX8fEvXVsOSLWkH91T2w,304
fontTools/feaLib/parser.py,sha256=wbfG_-rqrn2RWMRQMlR3-uaiM9k4_mzCVF-wPLr00rQ,98466
fontTools/feaLib/variableScalar.py,sha256=Aqx6BVUtd-A8566igKQXn_DxS7KBaes9VAkUlvkTb8c,4008
fontTools/fontBuilder.py,sha256=ueiX043jDFF99mraL3awsD7JbJROohphrCBssfMaPBU,33489
fontTools/help.py,sha256=xaZTZsaLVQGv0_HGkRdEPiW6D6OwxOLY5tFHpS3aVlk,1027
fontTools/merge/__init__.py,sha256=ndfRXVdiSNuVXMLRP3z3wRHMKwQVxtvb5gj2AfZBao4,8249
fontTools/merge/__main__.py,sha256=hDx3gfbUBO83AJKumSEhiV-xqNTJNNgK2uFjazOGTmw,94
fontTools/merge/__pycache__/__init__.cpython-310.pyc,,
fontTools/merge/__pycache__/__main__.cpython-310.pyc,,
fontTools/merge/__pycache__/base.cpython-310.pyc,,
fontTools/merge/__pycache__/cmap.cpython-310.pyc,,
fontTools/merge/__pycache__/layout.cpython-310.pyc,,
fontTools/merge/__pycache__/options.cpython-310.pyc,,
fontTools/merge/__pycache__/tables.cpython-310.pyc,,
fontTools/merge/__pycache__/unicode.cpython-310.pyc,,
fontTools/merge/__pycache__/util.cpython-310.pyc,,
fontTools/merge/base.py,sha256=l0G1Px98E9ZdVuFLMUBKWdtr7Jb8JX8vxcjeaDUUnzY,2389
fontTools/merge/cmap.py,sha256=_oCBnZfm5M7ebYRJnOYw5wUEICFmdR6kMUe1w6jsVuM,5545
fontTools/merge/layout.py,sha256=fkMPGPLxEdxohS3scVM4W7LmNthSz-UPyocsffe2KqE,16075
fontTools/merge/options.py,sha256=xko_1-WErcNQkirECzIOOYxSJR_bRtdQYQYOtmgccYI,2501
fontTools/merge/tables.py,sha256=DGefbqrjRAWW9nyBidvf79H-FyeREegqUy1uAj3ni0w,10545
fontTools/merge/unicode.py,sha256=kb1Jrfuoq1KUcVhhSKnflAED_wMZxXDjVwB-CI9k05Y,4273
fontTools/merge/util.py,sha256=BH3bZWNFy-Tsj1cth7aSpGVJ18YXKXqDakPn6Wzku6U,3378
fontTools/misc/__init__.py,sha256=DJBWmoX_Haau7qlgmvWyfbhSzrX2qL636Rns7CG01pk,75
fontTools/misc/__pycache__/__init__.cpython-310.pyc,,
fontTools/misc/__pycache__/arrayTools.cpython-310.pyc,,
fontTools/misc/__pycache__/bezierTools.cpython-310.pyc,,
fontTools/misc/__pycache__/classifyTools.cpython-310.pyc,,
fontTools/misc/__pycache__/cliTools.cpython-310.pyc,,
fontTools/misc/__pycache__/configTools.cpython-310.pyc,,
fontTools/misc/__pycache__/cython.cpython-310.pyc,,
fontTools/misc/__pycache__/dictTools.cpython-310.pyc,,
fontTools/misc/__pycache__/eexec.cpython-310.pyc,,
fontTools/misc/__pycache__/encodingTools.cpython-310.pyc,,
fontTools/misc/__pycache__/etree.cpython-310.pyc,,
fontTools/misc/__pycache__/filenames.cpython-310.pyc,,
fontTools/misc/__pycache__/fixedTools.cpython-310.pyc,,
fontTools/misc/__pycache__/intTools.cpython-310.pyc,,
fontTools/misc/__pycache__/loggingTools.cpython-310.pyc,,
fontTools/misc/__pycache__/macCreatorType.cpython-310.pyc,,
fontTools/misc/__pycache__/macRes.cpython-310.pyc,,
fontTools/misc/__pycache__/psCharStrings.cpython-310.pyc,,
fontTools/misc/__pycache__/psLib.cpython-310.pyc,,
fontTools/misc/__pycache__/psOperators.cpython-310.pyc,,
fontTools/misc/__pycache__/py23.cpython-310.pyc,,
fontTools/misc/__pycache__/roundTools.cpython-310.pyc,,
fontTools/misc/__pycache__/sstruct.cpython-310.pyc,,
fontTools/misc/__pycache__/symfont.cpython-310.pyc,,
fontTools/misc/__pycache__/testTools.cpython-310.pyc,,
fontTools/misc/__pycache__/textTools.cpython-310.pyc,,
fontTools/misc/__pycache__/timeTools.cpython-310.pyc,,
fontTools/misc/__pycache__/transform.cpython-310.pyc,,
fontTools/misc/__pycache__/treeTools.cpython-310.pyc,,
fontTools/misc/__pycache__/vector.cpython-310.pyc,,
fontTools/misc/__pycache__/visitor.cpython-310.pyc,,
fontTools/misc/__pycache__/xmlReader.cpython-310.pyc,,
fontTools/misc/__pycache__/xmlWriter.cpython-310.pyc,,
fontTools/misc/arrayTools.py,sha256=jZk__GE-K9VViZE_H-LPPj0smWbKng-yfPE8BfGp8HI,11483
fontTools/misc/bezierTools.c,sha256=nbEfSr-vx9cOjNOEKvReFwy8A_pu5WAarl-dou_lUrc,1804844
fontTools/misc/bezierTools.cpython-310-x86_64-linux-gnu.so,sha256=8FzOm-WmAromywBh0f2bP9ckAZBThdVYTdI9gVYp0PI,4336704
fontTools/misc/bezierTools.py,sha256=eiA4zu-hYd06Ys3bEgRI7VS9GnKuJhgRIPmi_1dSDlY,44731
fontTools/misc/classifyTools.py,sha256=zcg3EM4GOerBW9c063ljaLllgeeZ772EpFZjp9CdgLI,5613
fontTools/misc/cliTools.py,sha256=qCznJMLCQu3ZHQD_4ctUnr3TkfAUdkGl-UuxZUrppy0,1862
fontTools/misc/configTools.py,sha256=YXBE_vL2dMWCnK4oY3vtU15B79q82DtKp7h7XRqJc1Q,11188
fontTools/misc/cython.py,sha256=eyLcL2Bw-SSToYro8f44dkkYRlQfiFbhcza0afS-qHE,682
fontTools/misc/dictTools.py,sha256=VxjarsGJuk_wa3z29FSCtKZNCFfXtMBiNEu0RPAlpDk,2417
fontTools/misc/eexec.py,sha256=GNn2OCRvO1HbbIeDPxk9i0glO7cux_AQaoVMXhBR8y8,3331
fontTools/misc/encodingTools.py,sha256=hCv5PFfnXQJVCZA8Wyn1vr3vzLBbUuEPtGk5CzWM9RY,2073
fontTools/misc/etree.py,sha256=EPldipUNNMvbPimNX7qOUwKkbpJMY4uyElhe-wqKWkM,17079
fontTools/misc/filenames.py,sha256=MMCO3xjk1pcDc-baobcKd8IdoFPt-bcGqu8t8HUGAkI,8223
fontTools/misc/fixedTools.py,sha256=gsotTCOJLyMis13M4_jQJ8-QPob2Gl2TtNJhW6FER1I,7647
fontTools/misc/intTools.py,sha256=l6pjk4UYlXcyLtfC0DdOC5RL6UJ8ihRR0zRiYow5xA8,586
fontTools/misc/loggingTools.py,sha256=2uXks8fEnBjdgJEcxMLvD77-lbOPto3neJ86bMqV_qM,19898
fontTools/misc/macCreatorType.py,sha256=Je9jtqUr7EPbpH3QxlVl3pizoQ-1AOPMBIctHIMTM3k,1593
fontTools/misc/macRes.py,sha256=GT_pnfPw2NCvvOF86nHLAnOtZ6SMHqEuLntaplXzvHM,8579
fontTools/misc/plistlib/__init__.py,sha256=1HfhHPt3As6u2eRSlFfl6XdnXv_ypQImeQdWIw6wK7Y,21113
fontTools/misc/plistlib/__pycache__/__init__.cpython-310.pyc,,
fontTools/misc/plistlib/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
fontTools/misc/psCharStrings.py,sha256=KAqcehT_iNMSpDEnAHh5aNSeyGJ5cErx9r8tOIECxjE,42212
fontTools/misc/psLib.py,sha256=ioIPm5x3MHkBXF2vzNkC4iVZYobrkWcyvFhmYsjOrPY,12099
fontTools/misc/psOperators.py,sha256=9SLl5PPBulLo0Xxg_dqlJMitNIBdiGKdkXhOWsNSYZE,15700
fontTools/misc/py23.py,sha256=aPVCEUz_deggwLBCeTSsccX6QgJavZqvdVtuhpzrPvA,2238
fontTools/misc/roundTools.py,sha256=1RSXZ0gyi1qW42tz6WSBMJD1FlPdtgqKfWixVN9bd78,3173
fontTools/misc/sstruct.py,sha256=y8EpmoblqYL37Ru9B5mH9MlXTznO5JUw3YUImB5wtA4,6725
fontTools/misc/symfont.py,sha256=SXbpqWEjH8AqVwMlBo5Ozfm1y-IXlmSZkeCrR-rODKE,7069
fontTools/misc/testTools.py,sha256=P0lianKHKQ1re3IrLW5JGfoLgUXdtVJJceaNO5stA3o,6933
fontTools/misc/textTools.py,sha256=pbhr6LVhm3J-0Z4saYnJfxBDzyoiw4BR9pAgwypiOw8,3377
fontTools/misc/timeTools.py,sha256=e9h5pgzL04tBDXmCv_8eRGB4boFV8GKXlS6dq3ggEpw,2234
fontTools/misc/transform.py,sha256=mQs68bQCNozv_79QiMAhXjTdiGYUuhkapcPxT2anuvo,14473
fontTools/misc/treeTools.py,sha256=tLWkwyDHeZUPVOGNnJeD4Pn7x2bQeZetwJKaEAW2J2M,1269
fontTools/misc/vector.py,sha256=6lqZcDjAgHJFQgjzD-ULQ_PrigAMfeZKaBZmAfcC0ig,4062
fontTools/misc/visitor.py,sha256=-iX1ECuI-han4JdcZT1PUjvOlV9nlT6i2pxdv7FzMvk,5266
fontTools/misc/xmlReader.py,sha256=igut4_d13RT4WarliqVvuuPybO1uSXVeoBOeW4j0_e4,6580
fontTools/misc/xmlWriter.py,sha256=CA1c-Ov5vFTF9tT4bGk-f3yBvaX7lVmSdLPYygUqlAE,6046
fontTools/mtiLib/__init__.py,sha256=vPgS5Ko7dE0GJX1aDmXSwLOaBENDUgdAAFvYVdQ4boo,46617
fontTools/mtiLib/__main__.py,sha256=gd8X89jnZOe-752k7uaR1lWoiju-2zIT5Yx35Kl0Xek,94
fontTools/mtiLib/__pycache__/__init__.cpython-310.pyc,,
fontTools/mtiLib/__pycache__/__main__.cpython-310.pyc,,
fontTools/otlLib/__init__.py,sha256=D2leUW-3gsUTOFcJYGC18edBYjIJ804ut4qitJYWsaQ,45
fontTools/otlLib/__pycache__/__init__.cpython-310.pyc,,
fontTools/otlLib/__pycache__/builder.cpython-310.pyc,,
fontTools/otlLib/__pycache__/error.cpython-310.pyc,,
fontTools/otlLib/__pycache__/maxContextCalc.cpython-310.pyc,,
fontTools/otlLib/builder.py,sha256=BFIdTKJYRTx0__yK0yd0eiUQzdPcvRtugR4YCyq9Uf4,119473
fontTools/otlLib/error.py,sha256=cthuhBuOwZYpkTLi5gFPupUxkXkCHe-L_YgkE7N1wCI,335
fontTools/otlLib/maxContextCalc.py,sha256=l3PeR9SrhzCTwMRuRzpGxBZJzIWppk1hUqGv6b3--AE,3187
fontTools/otlLib/optimize/__init__.py,sha256=UUQRpNkHU2RczCRt-Gz7sEiYE9AQq9BHLXZEOyvsnX4,1530
fontTools/otlLib/optimize/__main__.py,sha256=BvP472kA9KxBb9RMyyehPNevAfpmgW9MfdazkUiAO3M,104
fontTools/otlLib/optimize/__pycache__/__init__.cpython-310.pyc,,
fontTools/otlLib/optimize/__pycache__/__main__.cpython-310.pyc,,
fontTools/otlLib/optimize/__pycache__/gpos.cpython-310.pyc,,
fontTools/otlLib/optimize/gpos.py,sha256=NTDLwjo90L4GiqdIdWkBEycQ7VcT7cOxxype73mFz8c,18474
fontTools/pens/__init__.py,sha256=DJBWmoX_Haau7qlgmvWyfbhSzrX2qL636Rns7CG01pk,75
fontTools/pens/__pycache__/__init__.cpython-310.pyc,,
fontTools/pens/__pycache__/areaPen.cpython-310.pyc,,
fontTools/pens/__pycache__/basePen.cpython-310.pyc,,
fontTools/pens/__pycache__/boundsPen.cpython-310.pyc,,
fontTools/pens/__pycache__/cairoPen.cpython-310.pyc,,
fontTools/pens/__pycache__/cocoaPen.cpython-310.pyc,,
fontTools/pens/__pycache__/cu2quPen.cpython-310.pyc,,
fontTools/pens/__pycache__/explicitClosingLinePen.cpython-310.pyc,,
fontTools/pens/__pycache__/filterPen.cpython-310.pyc,,
fontTools/pens/__pycache__/freetypePen.cpython-310.pyc,,
fontTools/pens/__pycache__/hashPointPen.cpython-310.pyc,,
fontTools/pens/__pycache__/momentsPen.cpython-310.pyc,,
fontTools/pens/__pycache__/perimeterPen.cpython-310.pyc,,
fontTools/pens/__pycache__/pointInsidePen.cpython-310.pyc,,
fontTools/pens/__pycache__/pointPen.cpython-310.pyc,,
fontTools/pens/__pycache__/qtPen.cpython-310.pyc,,
fontTools/pens/__pycache__/qu2cuPen.cpython-310.pyc,,
fontTools/pens/__pycache__/quartzPen.cpython-310.pyc,,
fontTools/pens/__pycache__/recordingPen.cpython-310.pyc,,
fontTools/pens/__pycache__/reportLabPen.cpython-310.pyc,,
fontTools/pens/__pycache__/reverseContourPen.cpython-310.pyc,,
fontTools/pens/__pycache__/roundingPen.cpython-310.pyc,,
fontTools/pens/__pycache__/statisticsPen.cpython-310.pyc,,
fontTools/pens/__pycache__/svgPathPen.cpython-310.pyc,,
fontTools/pens/__pycache__/t2CharStringPen.cpython-310.pyc,,
fontTools/pens/__pycache__/teePen.cpython-310.pyc,,
fontTools/pens/__pycache__/transformPen.cpython-310.pyc,,
fontTools/pens/__pycache__/ttGlyphPen.cpython-310.pyc,,
fontTools/pens/__pycache__/wxPen.cpython-310.pyc,,
fontTools/pens/areaPen.py,sha256=Y1WkmqzcC4z_bpGAR0IZUKrtHFtxKUQBmr5-64_zCOk,1472
fontTools/pens/basePen.py,sha256=aEQg1iDrlmzht-UJvgP-UA-dqux2b50xWTpq7Dxk9mU,15712
fontTools/pens/boundsPen.py,sha256=wE3owOQA8DfhH-zBGC3lJvnVwp-oyIt0KZrEqXbmS9I,3129
fontTools/pens/cairoPen.py,sha256=wuuOJ1qQDSt_K3zscM2nukRyHZTZMwMzzCXCirfq_qQ,592
fontTools/pens/cocoaPen.py,sha256=IJRQcAxRuVOTQ90bB_Bgjnmz7px_ST5uLF9CW-Y0KPY,612
fontTools/pens/cu2quPen.py,sha256=gMUwFUsm_-WzBlDjTMQiNnEuI2heomGeOJBX81zYXPo,13007
fontTools/pens/explicitClosingLinePen.py,sha256=kKKtdZiwaf8Cj4_ytrIDdGB2GMpPPDXm5Nwbw5WDgwU,3219
fontTools/pens/filterPen.py,sha256=pT8NuXzvKlLdhVB8Fes36PwKR9OXoF1eLVVuge4E15g,4953
fontTools/pens/freetypePen.py,sha256=MsIjlwvd54qQoSe3fqqGm4ZyhrhQi3-9B6X1yv5_KuQ,19813
fontTools/pens/hashPointPen.py,sha256=gElrFyQoOQp3ZbpKHRWPwC61A9OgT2Js8crVUD8BQAY,3573
fontTools/pens/momentsPen.c,sha256=4C9CxzhfD4r9bvMnQgx-64ZgFD4MBJcPzCoEF0yPpvU,542796
fontTools/pens/momentsPen.cpython-310-x86_64-linux-gnu.so,sha256=oYclPgB5f4Fu_TA5UKABiP42fXxTJ69a1jPJ8jOU_Ns,1077384
fontTools/pens/momentsPen.py,sha256=kiSvVWLJQPmUlus8MbMGkj8TB_QoZYL9uUwUMhyAHCQ,25685
fontTools/pens/perimeterPen.py,sha256=lr6NzrIWxi4TXBJPbcJsKzqABWfQeil2Bgm9BgUD3N4,2153
fontTools/pens/pointInsidePen.py,sha256=AloaWABNZY0KHkjIiHhElXUqJ_kr1Slf3XgwNtH7vwU,6336
fontTools/pens/pointPen.py,sha256=rk1SIxpNmbAsemXhLtpesDVW68YX-wP_HxvBshdtZiw,19319
fontTools/pens/qtPen.py,sha256=QRNLIry2rQl4E_7ct2tu10-qLHneQp0XV7FfaZ-tcL8,634
fontTools/pens/qu2cuPen.py,sha256=pRST43-rUpzlOP83Z_Rr0IvIQBCx6RWI6nnNaitQcLk,3985
fontTools/pens/quartzPen.py,sha256=EH482Kz_xsqYhVRovv6N_T1CXaSvOzUKPLxTaN956tU,1287
fontTools/pens/recordingPen.py,sha256=FcD-cWcfzSPywH66GcgEisabBF3jpHbkmagbN5IJJuM,6897
fontTools/pens/reportLabPen.py,sha256=kpfMfOLXt2vOQ5smPsU82ft80FpCPWJzQLl7ENOH8Ew,2066
fontTools/pens/reverseContourPen.py,sha256=oz64ZRhLAvT7DYMAwGKoLzZXQK8l81jRiYnTZkW6a-Y,4022
fontTools/pens/roundingPen.py,sha256=Q4vvG0Esq_sLNODU0TITU4F3wcXcKWo4BA7DWdDaVcM,4649
fontTools/pens/statisticsPen.py,sha256=IhiLy_pssq9hYZz5ZwOP6WSy9mX_bTvb-6PatbT47kY,9641
fontTools/pens/svgPathPen.py,sha256=LyLip9W0rirJs3YfGgdFS_f41OLjYM6EJt54gwzw49Y,8488
fontTools/pens/t2CharStringPen.py,sha256=uq9KCOxrk5TEZGYpcOG-pgkWHYCe4dMwb2hx5uYOmWA,2391
fontTools/pens/teePen.py,sha256=P1ARJOCMJ6MxK-PB1yZ-ips3CUfnadWYnQ_do6VIasQ,1290
fontTools/pens/transformPen.py,sha256=Nax1C9GflG1DFUBQBV2Vzr3d4mIOF41uO0SA3V_FJDM,3970
fontTools/pens/ttGlyphPen.py,sha256=yLtB-E5pTQR59OKVYySttWBu1xC2vR8ezSaRhIMtVwg,11870
fontTools/pens/wxPen.py,sha256=W9RRHlBWHp-CVC4Exvk3ytBmRaB4-LgJPP5Bv7o9BA0,680
fontTools/qu2cu/__init__.py,sha256=Jfm1JljXbt91w4gyvZn6jzEmVnhRx50sh2fDongrOsE,618
fontTools/qu2cu/__main__.py,sha256=bYg7TzC9ZpCBLl91hIzkcghan9ESbb_j61lw708JHmY,84
fontTools/qu2cu/__pycache__/__init__.cpython-310.pyc,,
fontTools/qu2cu/__pycache__/__main__.cpython-310.pyc,,
fontTools/qu2cu/__pycache__/benchmark.cpython-310.pyc,,
fontTools/qu2cu/__pycache__/cli.cpython-310.pyc,,
fontTools/qu2cu/__pycache__/qu2cu.cpython-310.pyc,,
fontTools/qu2cu/benchmark.py,sha256=b-YjDXcbRr07rxwjJKUXxYhXznTyGffsavRW0pRuJwY,1453
fontTools/qu2cu/cli.py,sha256=undiO1TF_L4aJTaep1iDzYfYEAGN9KQpbXZMDPxxr3g,3713
fontTools/qu2cu/qu2cu.c,sha256=Fle6j1e-1WKALIqroAlQO3FCLa6hYbznAnF1eQg_pMQ,659761
fontTools/qu2cu/qu2cu.cpython-310-x86_64-linux-gnu.so,sha256=oqCDjmZ6AUeIDUrGwzl_JzO_exR75EO3o5g5YxZE5pc,1110240
fontTools/qu2cu/qu2cu.py,sha256=1RKhaMBBiDvo5PtkNqR5p0X2HQ4yel4TbWT8MFU6Hps,12315
fontTools/subset/__init__.py,sha256=MOmuPAP9_ucldTiSEZywvvNg0ppy74x1N3L6WnSidoY,129814
fontTools/subset/__main__.py,sha256=bhtfP2SqP4k799pxtksFgnC-XGNQDr3LcO4lc8T5e5g,95
fontTools/subset/__pycache__/__init__.cpython-310.pyc,,
fontTools/subset/__pycache__/__main__.cpython-310.pyc,,
fontTools/subset/__pycache__/cff.cpython-310.pyc,,
fontTools/subset/__pycache__/svg.cpython-310.pyc,,
fontTools/subset/__pycache__/util.cpython-310.pyc,,
fontTools/subset/cff.py,sha256=AG88Mj8uHtG481D-Al3QIJk3t9tjX8ZsN-Tbcz0bWo8,18864
fontTools/subset/svg.py,sha256=8dLBzQlnIt4_fOKEFDAVlKTucdHvcbCcyG9-a6UBZZ0,9384
fontTools/subset/util.py,sha256=9SXFYb5Ef9Z58uXmYPCQil8B2i3Q7aFB_1fFDFSppdU,754
fontTools/svgLib/__init__.py,sha256=IGCLwSbU8jLhq6HI2vSdPQgNs6zDUi5774TgX5MCXPY,75
fontTools/svgLib/__pycache__/__init__.cpython-310.pyc,,
fontTools/svgLib/path/__init__.py,sha256=S9TqNYjzbkboA451YQrOoFlBvfZP3YAUrjCYlX9_wc4,1954
fontTools/svgLib/path/__pycache__/__init__.cpython-310.pyc,,
fontTools/svgLib/path/__pycache__/arc.cpython-310.pyc,,
fontTools/svgLib/path/__pycache__/parser.cpython-310.pyc,,
fontTools/svgLib/path/__pycache__/shapes.cpython-310.pyc,,
fontTools/svgLib/path/arc.py,sha256=-f5Ym6q4tDWQ76sMNSTUTWgL_7AfgXojvBhtBS7bWwQ,5812
fontTools/svgLib/path/parser.py,sha256=OEVtWJwi0o_kDhKX2S4hfP_FAR7uEmAQ24pr6O5VvwY,10767
fontTools/svgLib/path/shapes.py,sha256=xvBUIckKyT9JLy7q_ZP50r6TjvZANyHdZP7wFDzErcI,5322
fontTools/t1Lib/__init__.py,sha256=p42y70wEIbuX0IIxZG7-b_I-gHto1VLy0gLsDvxCfkw,20865
fontTools/t1Lib/__pycache__/__init__.cpython-310.pyc,,
fontTools/tfmLib.py,sha256=UMbkM73JXRJVS9t2B-BJc13rSjImaWBuzCoehLwHFhs,14270
fontTools/ttLib/__init__.py,sha256=fjOFcwbRed9b_giTgJ7FLsqeJC8ndnx327WfJztW-Tc,553
fontTools/ttLib/__main__.py,sha256=O19nJTxsS3O2umaVbrYgZOjghGb4KcppHXn2Cs1v0Z4,3406
fontTools/ttLib/__pycache__/__init__.cpython-310.pyc,,
fontTools/ttLib/__pycache__/__main__.cpython-310.pyc,,
fontTools/ttLib/__pycache__/macUtils.cpython-310.pyc,,
fontTools/ttLib/__pycache__/removeOverlaps.cpython-310.pyc,,
fontTools/ttLib/__pycache__/scaleUpem.cpython-310.pyc,,
fontTools/ttLib/__pycache__/sfnt.cpython-310.pyc,,
fontTools/ttLib/__pycache__/standardGlyphOrder.cpython-310.pyc,,
fontTools/ttLib/__pycache__/ttCollection.cpython-310.pyc,,
fontTools/ttLib/__pycache__/ttFont.cpython-310.pyc,,
fontTools/ttLib/__pycache__/ttGlyphSet.cpython-310.pyc,,
fontTools/ttLib/__pycache__/ttVisitor.cpython-310.pyc,,
fontTools/ttLib/__pycache__/woff2.cpython-310.pyc,,
fontTools/ttLib/macUtils.py,sha256=lj3oeFpyjV7ko_JqnluneITmAtlc119J-vwTTg2s73A,1737
fontTools/ttLib/removeOverlaps.py,sha256=0eRyIBLjwoxgKnC98IjCJOXGSodhZHSbWAq-kJOpydg,8138
fontTools/ttLib/scaleUpem.py,sha256=VNUWyE8ZmbhTe8PpRFZ308a6394lkV2dYolW4Ix_cgw,12223
fontTools/ttLib/sfnt.py,sha256=KkLGD3SBKdZ1QCTEtTvmXP05-w1ZAM_8pKRfPUVg240,22829
fontTools/ttLib/standardGlyphOrder.py,sha256=7AY_fVWdtwZ4iv5uWdyKAUcbEQiSDt1lN4sqx9xXwE0,5785
fontTools/ttLib/tables/B_A_S_E_.py,sha256=fotjQyGgXXMrLeWH-eu_R-OJ_ZepQ3GHOzQ3auhZ82Y,88
fontTools/ttLib/tables/BitmapGlyphMetrics.py,sha256=9gcGPVzsxEYnVBO7YLWfeOuht9PaCl09GmbAqDYqKi0,1769
fontTools/ttLib/tables/C_B_D_T_.py,sha256=cmxOO93VXhtS_nS6-iG9K2UUKHqTTEiFThV2wPMi0vA,3331
fontTools/ttLib/tables/C_B_L_C_.py,sha256=2Qr_xPnZn6yKMgWU5LzKfPyOu-dUK7q6XtyKAOOJl-0,188
fontTools/ttLib/tables/C_F_F_.py,sha256=jFX4ClhxD57IxfYDkDDCq2oJqSdbgAp1ghNQw5AYU7M,1443
fontTools/ttLib/tables/C_F_F__2.py,sha256=TTX4_bKYGmFGt2lihlFfKw8LLc-wIr6uE2P45Rv4qW0,425
fontTools/ttLib/tables/C_O_L_R_.py,sha256=qmexaOF-RtKSzHmekBPQIOa4Q2bmFMV3X_ytaCZhwhc,5725
fontTools/ttLib/tables/C_P_A_L_.py,sha256=4bXVL-qFKQaQhW_llYQzXZQClL24aJkEy0ms0-Bh2gk,11631
fontTools/ttLib/tables/D_S_I_G_.py,sha256=U5OCCI0sjhK5HvhNKaEonD0wucXzHXdfz5l3sb4CB8U,5327
fontTools/ttLib/tables/D__e_b_g.py,sha256=vROIV3UTxbK9eN3rmHOu1ARwBiOXL6K5ihmq0QMToJQ,443
fontTools/ttLib/tables/DefaultTable.py,sha256=cOtgkLWPY9qmOH2BSPt4c4IUSdANWTKx2rK1CTxQ4h0,1487
fontTools/ttLib/tables/E_B_D_T_.py,sha256=8iakmy4PP8BNiem9ZT_P7ysu8BkV1gWFJD94K5ThVSo,32276
fontTools/ttLib/tables/E_B_L_C_.py,sha256=rKqNd_Hxg4kJvjRLiFYS8M1GUv6aoHhLrplZRx46nBU,29761
fontTools/ttLib/tables/F_F_T_M_.py,sha256=aq9FsyfMegjxRsAWF8U2a3OpxFCPHJjNiLlC63dmqnI,1354
fontTools/ttLib/tables/F__e_a_t.py,sha256=x3ryfFJPsGVWqy10a4ulXADBnsB2JEVpyx_DuWYqy8k,5380
fontTools/ttLib/tables/G_D_E_F_.py,sha256=xN2hcW8GPMOos7dTpXJSWNJxUbGzUrnQ_2i-vxlNT_E,88
fontTools/ttLib/tables/G_M_A_P_.py,sha256=S0KyulRo88aZ4YM8OJ_l8Mf0husmlI03IlXP6aa1C1w,4515
fontTools/ttLib/tables/G_P_K_G_.py,sha256=XbfsF-qCk9ortdZycw7r6DEo94lfg6TTb3fN7HPYCuM,4441
fontTools/ttLib/tables/G_P_O_S_.py,sha256=nVSjCI8k7-8aIkzIMc7bCmd2aHeVvjwPIh2jhwn9KY4,88
fontTools/ttLib/tables/G_S_U_B_.py,sha256=-e_9Jxihz6AUSzSBCdW3tycdu4QZUsL8hZI6A7lMt9Q,88
fontTools/ttLib/tables/G__l_a_t.py,sha256=rWcOEnv9GmNIvJu7y-cpnrAUkc82527LroBIYA7NQTI,8568
fontTools/ttLib/tables/G__l_o_c.py,sha256=_MFYx8IUuJseNrS65QN-P8oq4CcGZnSxdGXKyv92Kco,2598
fontTools/ttLib/tables/H_V_A_R_.py,sha256=bdU_ktJJ2-MQ_zFn1wWTtGpZar7OTFeOEnXyrzDhts8,88
fontTools/ttLib/tables/J_S_T_F_.py,sha256=d36nOt42I5EY-7JDOulBHKtv1StpxxuvLU7gSOC6OGw,88
fontTools/ttLib/tables/L_T_S_H_.py,sha256=DG559txp9zRwe5xlhhq8_HqkOvKrgbWUBw-11nKtw-o,1826
fontTools/ttLib/tables/M_A_T_H_.py,sha256=zXSUNz98761iTREcge-YQ4LcEGCFhp1VVWAZt8B4TTQ,88
fontTools/ttLib/tables/M_E_T_A_.py,sha256=0IZysRvZur6rhe4DP7P2JnKW0O9SgbxLBHBmAJMx5vA,11784
fontTools/ttLib/tables/M_V_A_R_.py,sha256=uMresSbbzC43VL8Lou2bHjNmN3aY8wxxrV3qa6SSmR4,88
fontTools/ttLib/tables/O_S_2f_2.py,sha256=4TN66vch-0lJnr-f-ErbfWbxuDF_JRTOt-qy84oDG2k,27752
fontTools/ttLib/tables/S_I_N_G_.py,sha256=73zv425wym8w3MndveArHsp1TzM6VOQAz1gvwB9GgoQ,3112
fontTools/ttLib/tables/S_T_A_T_.py,sha256=tPbD_6x4aJACOux8bKe_sFlk0PEat7aiZn8pnXoUGws,88
fontTools/ttLib/tables/S_V_G_.py,sha256=8h8arIl9gedLB3GRRNF8V0x2pq1GikF7If9e_srB69I,7463
fontTools/ttLib/tables/S__i_l_f.py,sha256=5hZ1ze12-tRyYIu-hEewRlgMWiuGHNf40om7Rs369_Q,34901
fontTools/ttLib/tables/S__i_l_l.py,sha256=KvjK_vrh_YyPHtYwLyrHLx33gcTYg5lBnvUYie6b06M,3104
fontTools/ttLib/tables/T_S_I_B_.py,sha256=CMcquVV86ug63Zk_yTB37DKqO91FZW14WtzwBI2aPjY,86
fontTools/ttLib/tables/T_S_I_C_.py,sha256=TjDKgGdFEaL4Affo9MTInuVKbYUHMa0pJX18pzgYxT0,88
fontTools/ttLib/tables/T_S_I_D_.py,sha256=OP_tHge02Fs7Y5lnVrgUGfr4FdIu-iv3GVtMEyH3Nrw,86
fontTools/ttLib/tables/T_S_I_J_.py,sha256=soJ3cf52aXLQTqvhQV2bHzyRSh6bsxxvZcpAV4Z9tlc,86
fontTools/ttLib/tables/T_S_I_P_.py,sha256=SvDvtRhxiC96WvZxNb2RoyTf0IXjeVMF_UP42ZD_vwU,86
fontTools/ttLib/tables/T_S_I_S_.py,sha256=IHJsyWONSgbg9hm5VnkCeq70SQcwnNJZZO_dBtJGZFc,86
fontTools/ttLib/tables/T_S_I_V_.py,sha256=Pqr8g0zrgCZl2sSJlxE5AYXazlZE29o1BO8oMVblBUs,655
fontTools/ttLib/tables/T_S_I__0.py,sha256=c0F4nKBKTeURqxCFv3nwxCu9Dl0mh7wr0PhOrLKMjho,2043
fontTools/ttLib/tables/T_S_I__1.py,sha256=N-BoLR5WWZv8tglokn5WZv8w_52jzKDG8jiZn5bS__k,6982
fontTools/ttLib/tables/T_S_I__2.py,sha256=ZV39h3SKtVSxKF9dKkI4sC0X5oXLkQDSPCcOeBTxUTM,420
fontTools/ttLib/tables/T_S_I__3.py,sha256=wQnwccPX3IaxGjzCdJHwtLh2ZqSsoAS-vWjhdI2h5dQ,467
fontTools/ttLib/tables/T_S_I__5.py,sha256=jB-P8RMFC3KOGdtTQH5uzvqEJDIWhRlDFsuvAix0cl0,1510
fontTools/ttLib/tables/T_T_F_A_.py,sha256=7wiKnyzrHiLgdtz6klG02flh8S7hm7GKarif7lw3IMc,81
fontTools/ttLib/tables/TupleVariation.py,sha256=alTazkxRcDtySxQFGeI4vnWGvKa_BFQ6otLetSF0E54,29507
fontTools/ttLib/tables/V_D_M_X_.py,sha256=dqE3G2Hg4ByQNteceOMctgFu2Er_DHh4_vOlAAaP5nM,10189
fontTools/ttLib/tables/V_O_R_G_.py,sha256=XasThyPjPNah6Yn0TCFVv9H5kmYDx5FIMaH8B9sA2oU,5762
fontTools/ttLib/tables/V_V_A_R_.py,sha256=X9C_r2HiSnI2mYqUQ93yK4zLpweRzobJ0Kh1J2lTsAw,88
fontTools/ttLib/tables/__init__.py,sha256=zCtd7rcQS5cIoj0cqePaZ6oTcTPCwnzDS90_VIIEwQ0,2601
fontTools/ttLib/tables/__pycache__/B_A_S_E_.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/BitmapGlyphMetrics.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/C_B_D_T_.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/C_B_L_C_.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/C_F_F_.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/C_F_F__2.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/C_O_L_R_.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/C_P_A_L_.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/D_S_I_G_.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/D__e_b_g.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/DefaultTable.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/E_B_D_T_.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/E_B_L_C_.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/F_F_T_M_.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/F__e_a_t.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/G_D_E_F_.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/G_M_A_P_.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/G_P_K_G_.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/G_P_O_S_.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/G_S_U_B_.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/G__l_a_t.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/G__l_o_c.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/H_V_A_R_.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/J_S_T_F_.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/L_T_S_H_.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/M_A_T_H_.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/M_E_T_A_.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/M_V_A_R_.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/O_S_2f_2.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/S_I_N_G_.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/S_T_A_T_.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/S_V_G_.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/S__i_l_f.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/S__i_l_l.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/T_S_I_B_.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/T_S_I_C_.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/T_S_I_D_.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/T_S_I_J_.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/T_S_I_P_.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/T_S_I_S_.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/T_S_I_V_.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/T_S_I__0.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/T_S_I__1.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/T_S_I__2.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/T_S_I__3.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/T_S_I__5.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/T_T_F_A_.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/TupleVariation.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/V_D_M_X_.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/V_O_R_G_.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/V_V_A_R_.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/__init__.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/_a_n_k_r.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/_a_v_a_r.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/_b_s_l_n.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/_c_i_d_g.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/_c_m_a_p.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/_c_v_a_r.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/_c_v_t.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/_f_e_a_t.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/_f_p_g_m.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/_f_v_a_r.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/_g_a_s_p.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/_g_c_i_d.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/_g_l_y_f.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/_g_v_a_r.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/_h_d_m_x.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/_h_e_a_d.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/_h_h_e_a.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/_h_m_t_x.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/_k_e_r_n.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/_l_c_a_r.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/_l_o_c_a.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/_l_t_a_g.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/_m_a_x_p.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/_m_e_t_a.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/_m_o_r_t.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/_m_o_r_x.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/_n_a_m_e.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/_o_p_b_d.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/_p_o_s_t.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/_p_r_e_p.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/_p_r_o_p.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/_s_b_i_x.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/_t_r_a_k.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/_v_h_e_a.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/_v_m_t_x.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/asciiTable.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/grUtils.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/otBase.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/otConverters.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/otData.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/otTables.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/otTraverse.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/sbixGlyph.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/sbixStrike.cpython-310.pyc,,
fontTools/ttLib/tables/__pycache__/ttProgram.cpython-310.pyc,,
fontTools/ttLib/tables/_a_n_k_r.py,sha256=DhIUAWnvXZZdC1jlh9ubcsobFahdtlJMsk7v_2s-WaM,462
fontTools/ttLib/tables/_a_v_a_r.py,sha256=gQi_aDfC-MXOgXDwOV-f_SsFZGTX8d6cFoYtLwI30wI,5376
fontTools/ttLib/tables/_b_s_l_n.py,sha256=D1tRo8TDAUxeCqVWsTma9u2VxRzxUkCpF84Lv_hy4rU,170
fontTools/ttLib/tables/_c_i_d_g.py,sha256=A6llfYvsJQl0Mj6fnrRxUGXUlBkyEowo1J2euUulHM4,787
fontTools/ttLib/tables/_c_m_a_p.py,sha256=OP0WuHxErqVIDEuGnJ20lel04jd9JeAYIYTENqKK--Y,61643
fontTools/ttLib/tables/_c_v_a_r.py,sha256=Nlf8etrchBixD7qxFgxuDZ51VHA0XtsHfABDSgPG2RU,3307
fontTools/ttLib/tables/_c_v_t.py,sha256=E_mDVniDspGjbBQk9CDEm8y3LJ5FbnHxZHRGbq-okHA,1361
fontTools/ttLib/tables/_f_e_a_t.py,sha256=cshl7jgxj2RgzE8kECCkQVAW2ibJqgKLpZdT1PwyvuM,560
fontTools/ttLib/tables/_f_p_g_m.py,sha256=-a5WYucI482KQ65rmbl8YwsD4q9BRyDIunJ_9MYAeyc,1170
fontTools/ttLib/tables/_f_v_a_r.py,sha256=9KKJjFu--gQavQLKwuDLMFi_T5SlfivlE6I2LmzIgD0,8479
fontTools/ttLib/tables/_g_a_s_p.py,sha256=Sp31uXdZyQO2Bbp4Qh5QBu75TvnDmxNQYhfMXf6PkCg,1916
fontTools/ttLib/tables/_g_c_i_d.py,sha256=4VWq2u6c21ZOQ5_EJ5EwtZXC-zDz6SOPYwDDRZWRczA,170
fontTools/ttLib/tables/_g_l_y_f.py,sha256=vsEjxw_3Q8j0TngkTO7veaJx6cJQIRvE1lW2fvE6n68,98623
fontTools/ttLib/tables/_g_v_a_r.py,sha256=d_2scSh_71bSwaVHdEAk05py7KJA5BQMiszOBnMAvdw,10236
fontTools/ttLib/tables/_h_d_m_x.py,sha256=BOadCwbQhtiwQZoduvkvt6rtevP7BQiyd5KYnfjE0Cc,4024
fontTools/ttLib/tables/_h_e_a_d.py,sha256=cWH7gPQdb7SoWH88eyHHv0HeJ-k7xyXWjorPVTMIMGs,4745
fontTools/ttLib/tables/_h_h_e_a.py,sha256=YSMaTvNp3CD4G6WgGLmYdJGv_TKghKkT-IHW5Gw0iio,4434
fontTools/ttLib/tables/_h_m_t_x.py,sha256=DEcruWWtBYNW6sHtuv17snMCUYkvdaVtx_lrZLLhBfc,5767
fontTools/ttLib/tables/_k_e_r_n.py,sha256=SXkBnwz39gd6YHrQizGqz1orFEETp02vLgxzJSCNdYQ,10437
fontTools/ttLib/tables/_l_c_a_r.py,sha256=SKmQ65spClbLnsYMDoqecsUOWWNyBDsFWut-Y6ahVhk,88
fontTools/ttLib/tables/_l_o_c_a.py,sha256=aAcaTyf4ntk-PGY_Ko_K3IVAqbNLKwNc_FmRYIPWPG4,1994
fontTools/ttLib/tables/_l_t_a_g.py,sha256=L1ekoPzh4pMdWGRr-cdjL3M2asf4CqeUHq7zh4wvwrw,2274
fontTools/ttLib/tables/_m_a_x_p.py,sha256=Xol-ByrHp_eysC7Kvc7c0KLHWEQZ9rY2L8L2mFGQ1wk,5056
fontTools/ttLib/tables/_m_e_t_a.py,sha256=MslEJ7E0oO-JNHyAhtkRsBCBp0kK4OXfAgRqtRF9GDA,3651
fontTools/ttLib/tables/_m_o_r_t.py,sha256=2p7PzPGzdOtFhg-Fxvdh0PO4yRs6_z_WjQegexeZCsw,170
fontTools/ttLib/tables/_m_o_r_x.py,sha256=UJhBbA3mgVQO1oGmu_2bNXUwQreVSztG85F9k7DpmiQ,170
fontTools/ttLib/tables/_n_a_m_e.py,sha256=djXx4Tzw4LOGTiIoCz72mj-jErlYG-qWwKHL1POX58I,40729
fontTools/ttLib/tables/_o_p_b_d.py,sha256=t3eqUkZPyaQbahEmKaqp7brDNbt4MQje2Vq1jBu-fEc,170
fontTools/ttLib/tables/_p_o_s_t.py,sha256=DusC5HkI4eJw9jw9idb0GA1Xr9YuhQMnmsz4GM36kVI,11284
fontTools/ttLib/tables/_p_r_e_p.py,sha256=97rDk0OiGoOD-foAIzqzYM1IKhB4gQuWyBrkH1PVvP0,115
fontTools/ttLib/tables/_p_r_o_p.py,sha256=3JHFloIJwg9n4dzoe4KLobHc75oJh6DLNe51sakfz8E,170
fontTools/ttLib/tables/_s_b_i_x.py,sha256=eHzNG4I8732aeW7iUNEEdYsxgsHT9sTtbaD2vvAxxR8,4443
fontTools/ttLib/tables/_t_r_a_k.py,sha256=fZV1pQrAilSNc0Yd3x0XoIGbqlNoDv67LB2gb_CejMo,11069
fontTools/ttLib/tables/_v_h_e_a.py,sha256=zHokAcH7CQ4tZPQAGmdTuv0_X-FHwyLWea1f9aFb1Gg,4130
fontTools/ttLib/tables/_v_m_t_x.py,sha256=oUrskRNAf3FLIZaYLuk03np_IsIWBGUWbMFcdjU3Sys,229
fontTools/ttLib/tables/asciiTable.py,sha256=4c69jsAirUnDEpylf9CYBoCKTzwbmfbtUAOrtPnpHjY,637
fontTools/ttLib/tables/grUtils.py,sha256=hcOJ5oJPOd2uJWnWA7qwR7AfL37YZ5zUT7g8o5BBV80,2270
fontTools/ttLib/tables/otBase.py,sha256=0Aik3BCMmUBUwsPMKHhAAHKYqXSaL7dIUuYP2qd1D0k,53158
fontTools/ttLib/tables/otConverters.py,sha256=utVgYEJOrVjjaVSgX0DqgRPwvYEDTaz50cuTZJUK1rY,69216
fontTools/ttLib/tables/otData.py,sha256=u4MduMik-MnGDZeqVTVQaBRerjeHKQXRdmXaHsGqmMY,192591
fontTools/ttLib/tables/otTables.py,sha256=87FTcd-g0YT1muO0nat93FjN66tfNG3x1ucb3WVcPcQ,83289
fontTools/ttLib/tables/otTraverse.py,sha256=oTr7nA7u7kEltLAhl4Kfl1RPD8O2_bKaoXa5l0hkRVA,5497
fontTools/ttLib/tables/sbixGlyph.py,sha256=tjEUPVRfx6gr5yme8UytGTtVrimKN5qmbzT1GZPjXiM,5796
fontTools/ttLib/tables/sbixStrike.py,sha256=gFyOlhRIGnd59y0SrhtsT2Ce4L3yaBrLoFJ_dK9u9mQ,6663
fontTools/ttLib/tables/table_API_readme.txt,sha256=eZlRTLUkLzc_9Ot3pdfhyMb3ahU0_Iipx0vSbzOVGy8,2748
fontTools/ttLib/tables/ttProgram.py,sha256=tgtxgd-EnOq-2PUlYEihp-6NHu_7HnE5rxeSAtmXOtU,35888
fontTools/ttLib/ttCollection.py,sha256=aRph2MkBK3kd9-JCLqhJ1EN9pffN_lVX6WWmOTTewc8,3963
fontTools/ttLib/ttFont.py,sha256=hMzW3Z_U3_vGkZiiYXbNZiTr6NUsCRWCxnewbHTINA8,41064
fontTools/ttLib/ttGlyphSet.py,sha256=VYd22RriGS87j_BBJVpi_azKJXDwe4rILoTlapxYes0,13192
fontTools/ttLib/ttVisitor.py,sha256=_tah4C42Tv6Pm9QeLNQwwVCxqI4VNEAqYCbmThp6cvY,1025
fontTools/ttLib/woff2.py,sha256=MapZb0Hcfs7tfUIngTdhRk-n-duMLMBTccawMjtkOYo,61133
fontTools/ttx.py,sha256=_Ka2OrU5EPEh1dLwQqC4TccdNbBJdjLVk-jEJnKatOI,16648
fontTools/ufoLib/__init__.py,sha256=FcF400e77rehKcGfEt15_CAWgfSc0GF2Er5NPZubZYg,93665
fontTools/ufoLib/__pycache__/__init__.cpython-310.pyc,,
fontTools/ufoLib/__pycache__/converters.cpython-310.pyc,,
fontTools/ufoLib/__pycache__/errors.cpython-310.pyc,,
fontTools/ufoLib/__pycache__/etree.cpython-310.pyc,,
fontTools/ufoLib/__pycache__/filenames.cpython-310.pyc,,
fontTools/ufoLib/__pycache__/glifLib.cpython-310.pyc,,
fontTools/ufoLib/__pycache__/kerning.cpython-310.pyc,,
fontTools/ufoLib/__pycache__/plistlib.cpython-310.pyc,,
fontTools/ufoLib/__pycache__/pointPen.cpython-310.pyc,,
fontTools/ufoLib/__pycache__/utils.cpython-310.pyc,,
fontTools/ufoLib/__pycache__/validators.cpython-310.pyc,,
fontTools/ufoLib/converters.py,sha256=EjuBkQxFltzeb-qnt2jzwieJH92f9ybcdZwAvQJi_Kw,10558
fontTools/ufoLib/errors.py,sha256=UULZ4h1i_Lb9lywjScgC6N-wC4yyPceTSin0BebbhJk,584
fontTools/ufoLib/etree.py,sha256=YQpCsRlLv0zfZUK8_i9cNFKBvyq1Gyy6HQbKyPLCoEY,224
fontTools/ufoLib/filenames.py,sha256=Trm8k9AzXYYaYo0VwAgLJKCtWgsA1QjBlirmgXdZhjg,7562
fontTools/ufoLib/glifLib.py,sha256=wpoSz624xqocPJbdzzElyCAgmEOjZVQeGr2KiZtHvAA,72053
fontTools/ufoLib/kerning.py,sha256=0jPFd7mti884yvPjvYcU8lAWDwvVsNOObeQvVmPRJ3k,2973
fontTools/ufoLib/plistlib.py,sha256=IpMh2FH9-6dxcvjSK4YR7L01HTIP1_RnQ8mWliyds1E,1499
fontTools/ufoLib/pointPen.py,sha256=QGg6b_UeosZodcqqfAIPyAPUbfT7KgCxDwYfSR0GlCI,233
fontTools/ufoLib/utils.py,sha256=8aqNHdFUd_imnawCQFY3UaXpF_s_4sHeinH0lqELTos,1893
fontTools/ufoLib/validators.py,sha256=zIcp2weAYLOJBCvxbqBqAy34TaJrqpAlXKshJIkdhWI,30805
fontTools/unicode.py,sha256=ZZ7OMmWvIyV1IL1k6ioTzaRAh3tUvm6gvK7QgFbOIHY,1237
fontTools/unicodedata/Blocks.py,sha256=8sfrqmUZYlWWwy2tnh7d9DBE0RiGtZmMa5H4ZBVfPCU,31360
fontTools/unicodedata/OTTags.py,sha256=wOPpbMsNcp_gdvPFeITtgVMnTN8TJSNAsVEdu_nuPXE,1196
fontTools/unicodedata/ScriptExtensions.py,sha256=mrNVubRG5A6K8ARPoUVuW9GY9G5_m4b4aRg3w2_gOO4,19443
fontTools/unicodedata/Scripts.py,sha256=8qddX0zmRy4eqQoAFVtum3RiD2EwQqcN_SxHZ43KxsQ,126086
fontTools/unicodedata/__init__.py,sha256=QWboow5NU0P6sTAezY__zE-eL6JFvBpwEReeH2dIOb4,8816
fontTools/unicodedata/__pycache__/Blocks.cpython-310.pyc,,
fontTools/unicodedata/__pycache__/OTTags.cpython-310.pyc,,
fontTools/unicodedata/__pycache__/ScriptExtensions.cpython-310.pyc,,
fontTools/unicodedata/__pycache__/Scripts.cpython-310.pyc,,
fontTools/unicodedata/__pycache__/__init__.cpython-310.pyc,,
fontTools/varLib/__init__.py,sha256=_tYAD-r5wrSuk5nUNsjyac3Yk4vwXosCBdIW8SZvz2c,53401
fontTools/varLib/__main__.py,sha256=wbdYC5bPjWCxA0I4SKcLO88gl-UMtsYS8MxdW9ySTkY,95
fontTools/varLib/__pycache__/__init__.cpython-310.pyc,,
fontTools/varLib/__pycache__/__main__.cpython-310.pyc,,
fontTools/varLib/__pycache__/avar.cpython-310.pyc,,
fontTools/varLib/__pycache__/avarPlanner.cpython-310.pyc,,
fontTools/varLib/__pycache__/builder.cpython-310.pyc,,
fontTools/varLib/__pycache__/cff.cpython-310.pyc,,
fontTools/varLib/__pycache__/errors.cpython-310.pyc,,
fontTools/varLib/__pycache__/featureVars.cpython-310.pyc,,
fontTools/varLib/__pycache__/interpolatable.cpython-310.pyc,,
fontTools/varLib/__pycache__/interpolatableHelpers.cpython-310.pyc,,
fontTools/varLib/__pycache__/interpolatablePlot.cpython-310.pyc,,
fontTools/varLib/__pycache__/interpolatableTestContourOrder.cpython-310.pyc,,
fontTools/varLib/__pycache__/interpolatableTestStartingPoint.cpython-310.pyc,,
fontTools/varLib/__pycache__/interpolate_layout.cpython-310.pyc,,
fontTools/varLib/__pycache__/iup.cpython-310.pyc,,
fontTools/varLib/__pycache__/merger.cpython-310.pyc,,
fontTools/varLib/__pycache__/models.cpython-310.pyc,,
fontTools/varLib/__pycache__/mutator.cpython-310.pyc,,
fontTools/varLib/__pycache__/mvar.cpython-310.pyc,,
fontTools/varLib/__pycache__/plot.cpython-310.pyc,,
fontTools/varLib/__pycache__/stat.cpython-310.pyc,,
fontTools/varLib/__pycache__/varStore.cpython-310.pyc,,
fontTools/varLib/avar.py,sha256=wQ9jbQblv8-5RslOJzJigV3dAXC-eaQdG5I2MF7w9TU,1895
fontTools/varLib/avarPlanner.py,sha256=uLMGsL6cBbEMq5YItwABG_vXlXV3bxquM93WGDJ1brA,27358
fontTools/varLib/builder.py,sha256=gIcF3wydIbSQG7xXalaxMRiHYw48Lq6T3ObAfDgmFRM,4859
fontTools/varLib/cff.py,sha256=ZnIbuyYjq_iOL2qbGF-699IZ2wIgqNi68cErtWAA8co,26122
fontTools/varLib/errors.py,sha256=dMo8eGj76I7H4hrBEiNbYrGs2J1K1SwdsUyTHpkVOrQ,6934
fontTools/varLib/featureVars.py,sha256=BCOBGjGUv2Rw_z0rlVi1ZYkTDcCMh0LyAUzDVJ2PYm4,25448
fontTools/varLib/instancer/__init__.py,sha256=jKP1So-HyuA_Fq_30fz_uhi4cnsfiKNDyZXmpzeXoAw,58373
fontTools/varLib/instancer/__main__.py,sha256=zfULwcP01FhplS1IlcMgNQnLxk5RVfmOuinWjqeid-g,104
fontTools/varLib/instancer/__pycache__/__init__.cpython-310.pyc,,
fontTools/varLib/instancer/__pycache__/__main__.cpython-310.pyc,,
fontTools/varLib/instancer/__pycache__/featureVars.cpython-310.pyc,,
fontTools/varLib/instancer/__pycache__/names.cpython-310.pyc,,
fontTools/varLib/instancer/__pycache__/solver.cpython-310.pyc,,
fontTools/varLib/instancer/featureVars.py,sha256=oPqSlnHLMDTtOsmQMi6gkzLox7ymCrqlRAkvC_EJ4bc,7110
fontTools/varLib/instancer/names.py,sha256=IPRqel_M8zVU0jl30WsfgufxUm9PBBQDQCY3VHapeHc,14950
fontTools/varLib/instancer/solver.py,sha256=uMePwX0BVT5F94kUvDglsI4_F0nEH67F7RFuJ6tQwQ0,11002
fontTools/varLib/interpolatable.py,sha256=QucGcQQQIglJhk4oNkVUJlYESWCvIz-Bo0dERRQSKO4,41959
fontTools/varLib/interpolatableHelpers.py,sha256=TN0nRv_CjuSh8hurgz4LQ5E6-HBQECWTLjTFKnidxsg,10810
fontTools/varLib/interpolatablePlot.py,sha256=w393P6mGLRhYkIjSxMww3qyoYxAUZzCXlmPBbI_84C0,44375
fontTools/varLib/interpolatableTestContourOrder.py,sha256=EmJ2jp4sHuSM5P-seYvOLk0HLdWyPOHeVWRKIGIKXx4,3033
fontTools/varLib/interpolatableTestStartingPoint.py,sha256=Hq3NtC8I-O5dFnFoBQ6qVvLCzlPWEZqOSLP15QeuSAw,4231
fontTools/varLib/interpolate_layout.py,sha256=22VjGZuV2YiAe2MpdTf0xPVz1x2G84bcOL0vOeBpGQM,3689
fontTools/varLib/iup.c,sha256=qbBO2KlGv_xwcwdAN7MHYOQ4SbZ-SKO0qmxs-4XpZKM,780417
fontTools/varLib/iup.cpython-310-x86_64-linux-gnu.so,sha256=TJp7YbfBhH-b-ttbhJZXMpfrtBainSkZDF2RGoHtJ70,1514152
fontTools/varLib/iup.py,sha256=bUk3O1QoFM8k_QEleHruT0biPoauX8AUJorbRuO21Vo,14675
fontTools/varLib/merger.py,sha256=E59oli4AwqWZ-FgnuStMSBvsB-FHe-55esXTYUqGeJ8,60802
fontTools/varLib/models.py,sha256=SQI0ipNdl1WX8ysszet58UPtSRAAr9R0K40800AdXQo,21961
fontTools/varLib/mutator.py,sha256=P1ukWRojb1p7kcfa37EPny_GOky-FkZy7wcAHHeF9jA,19226
fontTools/varLib/mvar.py,sha256=LTV77vH_3Ecg_qKBO5xQzjLOlJir_ppEr7mPVZRgad8,2449
fontTools/varLib/plot.py,sha256=NoSZkJ5ndxNcDvJIvd5pQ9_jX6X1oM1K2G_tR4sdPVs,7494
fontTools/varLib/stat.py,sha256=pNtU3Jebm8Gr5umrbF5xGj5yJQciFwSFpfePOcg37xY,4535
fontTools/varLib/varStore.py,sha256=gBR-gvddip1qukEcGQdI-Nra5eKXGHowTsVZuZmZ2B4,23689
fontTools/voltLib/__init__.py,sha256=ZZ1AsTx1VlDn40Kupce-fM3meOWugy3RZraBW9LG-9M,151
fontTools/voltLib/__pycache__/__init__.cpython-310.pyc,,
fontTools/voltLib/__pycache__/ast.cpython-310.pyc,,
fontTools/voltLib/__pycache__/error.cpython-310.pyc,,
fontTools/voltLib/__pycache__/lexer.cpython-310.pyc,,
fontTools/voltLib/__pycache__/parser.cpython-310.pyc,,
fontTools/voltLib/__pycache__/voltToFea.cpython-310.pyc,,
fontTools/voltLib/ast.py,sha256=sioOeSazmC8PxRMRql33I64JaCflu55UUZcikm9mwIY,13226
fontTools/voltLib/error.py,sha256=phcQOQj-xOspCXu9hBJQRhSOBDzxHRgZd3fWQOFNJzw,395
fontTools/voltLib/lexer.py,sha256=OvuETOSvlS6v7iCVeJ3IdH2Cg71n3OJoEyiB3-h6vhE,3368
fontTools/voltLib/parser.py,sha256=wBSUrjLT3fSPv9Mjx6_ULIf8IcGlwjtb4Auxjh5wqnc,24916
fontTools/voltLib/voltToFea.py,sha256=yi5cytZjCJbsKdJuM5k6HWSlqE_ZUv0l1rzp8K1vo3A,28465
fonttools-4.49.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
fonttools-4.49.0.dist-info/LICENSE,sha256=Z4cgj4P2Wcy8IiOy_elS_6b36KymLxqKK_W8UbsbI4M,1072
fonttools-4.49.0.dist-info/METADATA,sha256=o1dZF8oEMBIi_ZgEKR03nhja-DEqWDct-gcsk-UFtqA,159053
fonttools-4.49.0.dist-info/RECORD,,
fonttools-4.49.0.dist-info/WHEEL,sha256=1FEjxEYgybphwh9S0FO9IcZ0B-NIeM2ko8OzhFZeOeQ,152
fonttools-4.49.0.dist-info/entry_points.txt,sha256=8kVHddxfFWA44FSD4mBpmC-4uCynQnkoz_9aNJb227Y,147
fonttools-4.49.0.dist-info/top_level.txt,sha256=rRgRylrXzekqWOsrhygzib12pQ7WILf7UGjqEwkIFDM,10
