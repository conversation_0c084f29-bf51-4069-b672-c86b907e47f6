# WARNING: Do not edit by hand, this file was generated by Crank:
#
#   https://github.com/gocardless/crank
#

class Paginator(object):
    """Iterates through all records in a paginated collection, automatically
    loading each page until the last.
    """

    def __init__(self, service, params):
        self._service = service
        self._params = params

    def __iter__(self):
        return self._iterate()

    def _iterate(self):
        page = self._fetch_page(None)
        while True:
            for record in page.records:
                yield record

            if page.after is None:
                break

            page = self._fetch_page(page.after)

    def _fetch_page(self, after):
        params = self._params.copy()
        if after is not None:
            params.update({'after': after})
        return self._service.list(params=params)

