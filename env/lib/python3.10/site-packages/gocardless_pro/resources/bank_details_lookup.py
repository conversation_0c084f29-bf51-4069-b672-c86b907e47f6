# WARNING: Do not edit by hand, this file was generated by Crank:
#
#   https://github.com/gocardless/crank
#

class BankDetailsLookup(object):
    """A thin wrapper around a bank_details_lookup, providing easy access to its
    attributes.

    Example:
      bank_details_lookup = client.bank_details_lookups.get()
      bank_details_lookup.id
    """

    def __init__(self, attributes, api_response):
        self.attributes = attributes
        self.api_response = api_response

    @property
    def available_debit_schemes(self):
        return self.attributes.get('available_debit_schemes')
  

    @property
    def bank_name(self):
        return self.attributes.get('bank_name')
  

    @property
    def bic(self):
        return self.attributes.get('bic')
  


  

  

  

