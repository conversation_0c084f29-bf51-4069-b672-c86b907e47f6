# WARNING: Do not edit by hand, this file was generated by Crank:
#
#   https://github.com/gocardless/crank
#

class Creditor(object):
    """A thin wrapper around a creditor, providing easy access to its
    attributes.

    Example:
      creditor = client.creditors.get()
      creditor.id
    """

    def __init__(self, attributes, api_response):
        self.attributes = attributes
        self.api_response = api_response

    @property
    def address_line1(self):
        return self.attributes.get('address_line1')
  

    @property
    def address_line2(self):
        return self.attributes.get('address_line2')
  

    @property
    def address_line3(self):
        return self.attributes.get('address_line3')
  

    @property
    def can_create_refunds(self):
        return self.attributes.get('can_create_refunds')
  

    @property
    def city(self):
        return self.attributes.get('city')
  

    @property
    def country_code(self):
        return self.attributes.get('country_code')
  

    @property
    def created_at(self):
        return self.attributes.get('created_at')
  

    @property
    def custom_payment_pages_enabled(self):
        return self.attributes.get('custom_payment_pages_enabled')
  

    @property
    def fx_payout_currency(self):
        return self.attributes.get('fx_payout_currency')
  

    @property
    def id(self):
        return self.attributes.get('id')
  

    @property
    def links(self):
        return self.Links(self.attributes.get('links'))
  

    @property
    def logo_url(self):
        return self.attributes.get('logo_url')
  

    @property
    def mandate_imports_enabled(self):
        return self.attributes.get('mandate_imports_enabled')
  

    @property
    def merchant_responsible_for_notifications(self):
        return self.attributes.get('merchant_responsible_for_notifications')
  

    @property
    def name(self):
        return self.attributes.get('name')
  

    @property
    def postal_code(self):
        return self.attributes.get('postal_code')
  

    @property
    def region(self):
        return self.attributes.get('region')
  

    @property
    def scheme_identifiers(self):
        return self.attributes.get('scheme_identifiers')
  

    @property
    def verification_status(self):
        return self.attributes.get('verification_status')
  


  

  

  

  

  

  

  

  

  

  

  
    class Links(object):
        """Wrapper for the response's 'links' attribute."""

        def __init__(self, attributes):
            self.attributes = attributes
    
        @property
        def default_aud_payout_account(self):
            return self.attributes.get('default_aud_payout_account')
    
        @property
        def default_cad_payout_account(self):
            return self.attributes.get('default_cad_payout_account')
    
        @property
        def default_dkk_payout_account(self):
            return self.attributes.get('default_dkk_payout_account')
    
        @property
        def default_eur_payout_account(self):
            return self.attributes.get('default_eur_payout_account')
    
        @property
        def default_gbp_payout_account(self):
            return self.attributes.get('default_gbp_payout_account')
    
        @property
        def default_nzd_payout_account(self):
            return self.attributes.get('default_nzd_payout_account')
    
        @property
        def default_sek_payout_account(self):
            return self.attributes.get('default_sek_payout_account')
    
        @property
        def default_usd_payout_account(self):
            return self.attributes.get('default_usd_payout_account')
    
  

  

  

  

  

  

  

  

  

