# WARNING: Do not edit by hand, this file was generated by Crank:
#
#   https://github.com/gocardless/crank
#

class CurrencyExchangeRate(object):
    """A thin wrapper around a currency_exchange_rate, providing easy access to its
    attributes.

    Example:
      currency_exchange_rate = client.currency_exchange_rates.get()
      currency_exchange_rate.id
    """

    def __init__(self, attributes, api_response):
        self.attributes = attributes
        self.api_response = api_response

    @property
    def rate(self):
        return self.attributes.get('rate')
  

    @property
    def source(self):
        return self.attributes.get('source')
  

    @property
    def target(self):
        return self.attributes.get('target')
  

    @property
    def time(self):
        return self.attributes.get('time')
  


  

  

  

  

