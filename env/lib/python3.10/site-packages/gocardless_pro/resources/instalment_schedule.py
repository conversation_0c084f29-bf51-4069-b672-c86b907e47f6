# WARNING: Do not edit by hand, this file was generated by Crank:
#
#   https://github.com/gocardless/crank
#

class InstalmentSchedule(object):
    """A thin wrapper around a instalment_schedule, providing easy access to its
    attributes.

    Example:
      instalment_schedule = client.instalment_schedules.get()
      instalment_schedule.id
    """

    def __init__(self, attributes, api_response):
        self.attributes = attributes
        self.api_response = api_response

    @property
    def created_at(self):
        return self.attributes.get('created_at')
  

    @property
    def currency(self):
        return self.attributes.get('currency')
  

    @property
    def id(self):
        return self.attributes.get('id')
  

    @property
    def links(self):
        return self.Links(self.attributes.get('links'))
  

    @property
    def metadata(self):
        return self.attributes.get('metadata')
  

    @property
    def name(self):
        return self.attributes.get('name')
  

    @property
    def payment_errors(self):
        return self.attributes.get('payment_errors')
  

    @property
    def status(self):
        return self.attributes.get('status')
  

    @property
    def total_amount(self):
        return self.attributes.get('total_amount')
  


  

  

  

  
    class Links(object):
        """Wrapper for the response's 'links' attribute."""

        def __init__(self, attributes):
            self.attributes = attributes
    
        @property
        def customer(self):
            return self.attributes.get('customer')
    
        @property
        def mandate(self):
            return self.attributes.get('mandate')
    
        @property
        def payments(self):
            return self.attributes.get('payments')
    
  

  

  

  

  

  

