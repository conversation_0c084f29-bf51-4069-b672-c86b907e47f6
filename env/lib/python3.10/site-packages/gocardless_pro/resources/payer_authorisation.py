# WARNING: Do not edit by hand, this file was generated by Crank:
#
#   https://github.com/gocardless/crank
#

class PayerAuthorisation(object):
    """A thin wrapper around a payer_authorisation, providing easy access to its
    attributes.

    Example:
      payer_authorisation = client.payer_authorisations.get()
      payer_authorisation.id
    """

    def __init__(self, attributes, api_response):
        self.attributes = attributes
        self.api_response = api_response

    @property
    def bank_account(self):
        return self.BankAccount(self.attributes.get('bank_account'))
  

    @property
    def created_at(self):
        return self.attributes.get('created_at')
  

    @property
    def customer(self):
        return self.Customer(self.attributes.get('customer'))
  

    @property
    def id(self):
        return self.attributes.get('id')
  

    @property
    def incomplete_fields(self):
        return self.attributes.get('incomplete_fields')
  

    @property
    def links(self):
        return self.Links(self.attributes.get('links'))
  

    @property
    def mandate(self):
        return self.Mandate(self.attributes.get('mandate'))
  

    @property
    def status(self):
        return self.attributes.get('status')
  


  
    class BankAccount(object):
        """Wrapper for the response's 'bank_account' attribute."""

        def __init__(self, attributes):
            self.attributes = attributes
    
        @property
        def account_holder_name(self):
            return self.attributes.get('account_holder_name')
    
        @property
        def account_number(self):
            return self.attributes.get('account_number')
    
        @property
        def account_number_suffix(self):
            return self.attributes.get('account_number_suffix')
    
        @property
        def account_type(self):
            return self.attributes.get('account_type')
    
        @property
        def bank_code(self):
            return self.attributes.get('bank_code')
    
        @property
        def branch_code(self):
            return self.attributes.get('branch_code')
    
        @property
        def country_code(self):
            return self.attributes.get('country_code')
    
        @property
        def currency(self):
            return self.attributes.get('currency')
    
        @property
        def iban(self):
            return self.attributes.get('iban')
    
        @property
        def metadata(self):
            return self.attributes.get('metadata')
    
  

  

  
    class Customer(object):
        """Wrapper for the response's 'customer' attribute."""

        def __init__(self, attributes):
            self.attributes = attributes
    
        @property
        def address_line1(self):
            return self.attributes.get('address_line1')
    
        @property
        def address_line2(self):
            return self.attributes.get('address_line2')
    
        @property
        def address_line3(self):
            return self.attributes.get('address_line3')
    
        @property
        def city(self):
            return self.attributes.get('city')
    
        @property
        def company_name(self):
            return self.attributes.get('company_name')
    
        @property
        def country_code(self):
            return self.attributes.get('country_code')
    
        @property
        def danish_identity_number(self):
            return self.attributes.get('danish_identity_number')
    
        @property
        def email(self):
            return self.attributes.get('email')
    
        @property
        def family_name(self):
            return self.attributes.get('family_name')
    
        @property
        def given_name(self):
            return self.attributes.get('given_name')
    
        @property
        def locale(self):
            return self.attributes.get('locale')
    
        @property
        def metadata(self):
            return self.attributes.get('metadata')
    
        @property
        def postal_code(self):
            return self.attributes.get('postal_code')
    
        @property
        def region(self):
            return self.attributes.get('region')
    
        @property
        def swedish_identity_number(self):
            return self.attributes.get('swedish_identity_number')
    
  

  

  

  
    class Links(object):
        """Wrapper for the response's 'links' attribute."""

        def __init__(self, attributes):
            self.attributes = attributes
    
        @property
        def bank_account(self):
            return self.attributes.get('bank_account')
    
        @property
        def customer(self):
            return self.attributes.get('customer')
    
        @property
        def mandate(self):
            return self.attributes.get('mandate')
    
  

  
    class Mandate(object):
        """Wrapper for the response's 'mandate' attribute."""

        def __init__(self, attributes):
            self.attributes = attributes
    
        @property
        def metadata(self):
            return self.attributes.get('metadata')
    
        @property
        def payer_ip_address(self):
            return self.attributes.get('payer_ip_address')
    
        @property
        def reference(self):
            return self.attributes.get('reference')
    
        @property
        def scheme(self):
            return self.attributes.get('scheme')
    
  

  

