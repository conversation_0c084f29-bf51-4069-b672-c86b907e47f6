# WARNING: Do not edit by hand, this file was generated by Crank:
#
#   https://github.com/gocardless/crank
#

class PayoutItem(object):
    """A thin wrapper around a payout_item, providing easy access to its
    attributes.

    Example:
      payout_item = client.payout_items.get()
      payout_item.id
    """

    def __init__(self, attributes, api_response):
        self.attributes = attributes
        self.api_response = api_response

    @property
    def amount(self):
        return self.attributes.get('amount')
  

    @property
    def links(self):
        return self.Links(self.attributes.get('links'))
  

    @property
    def taxes(self):
        return self.attributes.get('taxes')
  

    @property
    def type(self):
        return self.attributes.get('type')
  


  

  
    class Links(object):
        """Wrapper for the response's 'links' attribute."""

        def __init__(self, attributes):
            self.attributes = attributes
    
        @property
        def mandate(self):
            return self.attributes.get('mandate')
    
        @property
        def payment(self):
            return self.attributes.get('payment')
    
        @property
        def refund(self):
            return self.attributes.get('refund')
    
  

  

  

