# WARNING: Do not edit by hand, this file was generated by Crank:
#
#   https://github.com/gocardless/crank
#

class Refund(object):
    """A thin wrapper around a refund, providing easy access to its
    attributes.

    Example:
      refund = client.refunds.get()
      refund.id
    """

    def __init__(self, attributes, api_response):
        self.attributes = attributes
        self.api_response = api_response

    @property
    def amount(self):
        return self.attributes.get('amount')
  

    @property
    def created_at(self):
        return self.attributes.get('created_at')
  

    @property
    def currency(self):
        return self.attributes.get('currency')
  

    @property
    def fx(self):
        return self.Fx(self.attributes.get('fx'))
  

    @property
    def id(self):
        return self.attributes.get('id')
  

    @property
    def links(self):
        return self.Links(self.attributes.get('links'))
  

    @property
    def metadata(self):
        return self.attributes.get('metadata')
  

    @property
    def reference(self):
        return self.attributes.get('reference')
  

    @property
    def status(self):
        return self.attributes.get('status')
  


  

  

  

  
    class Fx(object):
        """Wrapper for the response's 'fx' attribute."""

        def __init__(self, attributes):
            self.attributes = attributes
    
        @property
        def estimated_exchange_rate(self):
            return self.attributes.get('estimated_exchange_rate')
    
        @property
        def exchange_rate(self):
            return self.attributes.get('exchange_rate')
    
        @property
        def fx_amount(self):
            return self.attributes.get('fx_amount')
    
        @property
        def fx_currency(self):
            return self.attributes.get('fx_currency')
    
  

  

  
    class Links(object):
        """Wrapper for the response's 'links' attribute."""

        def __init__(self, attributes):
            self.attributes = attributes
    
        @property
        def mandate(self):
            return self.attributes.get('mandate')
    
        @property
        def payment(self):
            return self.attributes.get('payment')
    
  

  

  

  

