# WARNING: Do not edit by hand, this file was generated by Crank:
#
#   https://github.com/gocardless/crank
#

class TaxRate(object):
    """A thin wrapper around a tax_rate, providing easy access to its
    attributes.

    Example:
      tax_rate = client.tax_rates.get()
      tax_rate.id
    """

    def __init__(self, attributes, api_response):
        self.attributes = attributes
        self.api_response = api_response

    @property
    def end_date(self):
        return self.attributes.get('end_date')
  

    @property
    def id(self):
        return self.attributes.get('id')
  

    @property
    def jurisdiction(self):
        return self.attributes.get('jurisdiction')
  

    @property
    def percentage(self):
        return self.attributes.get('percentage')
  

    @property
    def start_date(self):
        return self.attributes.get('start_date')
  

    @property
    def type(self):
        return self.attributes.get('type')
  


  

  

  

  

  

  

