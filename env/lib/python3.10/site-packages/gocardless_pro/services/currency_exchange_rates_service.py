# WARNING: Do not edit by hand, this file was generated by Crank:
#
#   https://github.com/gocardless/crank
#

from . import base_service
from .. import resources
from ..paginator import Paginator
from .. import errors

class CurrencyExchangeRatesService(base_service.BaseService):
    """Service class that provides access to the currency_exchange_rates
    endpoints of the GoCardless Pro API.
    """

    RESOURCE_CLASS = resources.CurrencyExchangeRate
    RESOURCE_NAME = 'currency_exchange_rates'


    def list(self,params=None, headers=None):
        """List exchange rates.

        Returns a [cursor-paginated](#api-usage-cursor-pagination) list of all
        exchange rates.

        Args:
              params (dict, optional): Query string parameters.

        Returns:
              CurrencyExchangeRate
        """
        path = '/currency_exchange_rates'
        

        response = self._perform_request('GET', path, params, headers,
                                         retry_failures=True)
        return self._resource_for(response)

    def all(self, params=None):
        if params is None:
            params = {}
        return Paginator(self, params)
    
  
