# WARNING: Do not edit by hand, this file was generated by Crank:
#
#   https://github.com/gocardless/crank
#

from . import base_service
from .. import resources
from ..paginator import Paginator
from .. import errors

class CustomerNotificationsService(base_service.BaseService):
    """Service class that provides access to the customer_notifications
    endpoints of the GoCardless Pro API.
    """

    RESOURCE_CLASS = resources.CustomerNotification
    RESOURCE_NAME = 'customer_notifications'


    def handle(self,identity,params=None, headers=None):
        """Handle a notification.

        "Handling" a notification means that you have sent the notification
        yourself (and
        don't want GoCardless to send it).
        If the notification has already been actioned, or the deadline to
        notify has passed,
        this endpoint will return an `already_actioned` error and you should
        not take
        further action. This endpoint takes no additional parameters.
        

        Args:
              identity (string): The id of the notification.
              params (dict, optional): Request body.

        Returns:
              ListResponse of CustomerNotification instances
        """
        path = self._sub_url_params('/customer_notifications/:identity/actions/handle', {
          
            'identity': identity,
          })
        
        if params is not None:
            params = {'data': params}
        response = self._perform_request('POST', path, params, headers,
                                         retry_failures=False)
        return self._resource_for(response)
  
