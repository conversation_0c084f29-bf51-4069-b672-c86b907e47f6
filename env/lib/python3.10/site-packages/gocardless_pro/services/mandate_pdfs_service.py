# WARNING: Do not edit by hand, this file was generated by Crank:
#
#   https://github.com/gocardless/crank
#

from . import base_service
from .. import resources
from ..paginator import Paginator
from .. import errors

class MandatePdfsService(base_service.BaseService):
    """Service class that provides access to the mandate_pdfs
    endpoints of the GoCardless Pro API.
    """

    RESOURCE_CLASS = resources.MandatePdf
    RESOURCE_NAME = 'mandate_pdfs'


    def create(self,params=None, headers=None):
        """Create a mandate PDF.

        Generates a PDF mandate and returns its temporary URL.
        
        Customer and bank account details can be left blank (for a blank
        mandate), provided manually, or inferred from the ID of an existing
        [mandate](#core-endpoints-mandates).
        
        By default, we'll generate PDF mandates in English.
        
        To generate a PDF mandate in another language, set the
        `Accept-Language` header when creating the PDF mandate to the relevant
        [ISO 639-1](http://en.wikipedia.org/wiki/List_of_ISO_639-1_codes)
        language code supported for the scheme.
        
        | Scheme           | Supported languages                               
                                                                               
                          |
        | :--------------- |
        :-------------------------------------------------------------------------------------------------------------------------------------------
        |
        | ACH              | English (`en`)                                    
                                                                               
                          |
        | Autogiro         | English (`en`), Swedish (`sv`)                    
                                                                               
                          |
        | Bacs             | English (`en`)                                    
                                                                               
                          |
        | BECS             | English (`en`)                                    
                                                                               
                          |
        | BECS NZ          | English (`en`)                                    
                                                                               
                          |
        | Betalingsservice | Danish (`da`), English (`en`)                     
                                                                               
                          |
        | PAD              | English (`en`)                                    
                                                                               
                          |
        | SEPA Core        | Danish (`da`), Dutch (`nl`), English (`en`),
        French (`fr`), German (`de`), Italian (`it`), Portuguese (`pt`),
        Spanish (`es`), Swedish (`sv`) |

        Args:
              params (dict, optional): Request body.

        Returns:
              ListResponse of MandatePdf instances
        """
        path = '/mandate_pdfs'
        
        if params is not None:
            params = {self._envelope_key(): params}

        response = self._perform_request('POST', path, params, headers,
                                         retry_failures=True)
        return self._resource_for(response)
  
