# WARNING: Do not edit by hand, this file was generated by Crank:
#
#   https://github.com/gocardless/crank
#

from . import base_service
from .. import resources
from ..paginator import Paginator
from .. import errors

class PaymentsService(base_service.BaseService):
    """Service class that provides access to the payments
    endpoints of the GoCardless Pro API.
    """

    RESOURCE_CLASS = resources.Payment
    RESOURCE_NAME = 'payments'


    def create(self,params=None, headers=None):
        """Create a payment.

        <a name="mandate_is_inactive"></a>Creates a new payment object.
        
        This fails with a `mandate_is_inactive` error if the linked
        [mandate](#core-endpoints-mandates) is cancelled or has failed.
        Payments can be created against mandates with status of:
        `pending_customer_approval`, `pending_submission`, `submitted`, and
        `active`.

        Args:
              params (dict, optional): Request body.

        Returns:
              ListResponse of Payment instances
        """
        path = '/payments'
        
        if params is not None:
            params = {self._envelope_key(): params}

        try:
          response = self._perform_request('POST', path, params, headers,
                                            retry_failures=True)
        except errors.IdempotentCreationConflictError as err:
          if self.raise_on_idempotency_conflict:
            raise err
          return self.get(identity=err.conflicting_resource_id,
                          params=params,
                          headers=headers)
        return self._resource_for(response)
  

    def list(self,params=None, headers=None):
        """List payments.

        Returns a [cursor-paginated](#api-usage-cursor-pagination) list of your
        payments.

        Args:
              params (dict, optional): Query string parameters.

        Returns:
              Payment
        """
        path = '/payments'
        

        response = self._perform_request('GET', path, params, headers,
                                         retry_failures=True)
        return self._resource_for(response)

    def all(self, params=None):
        if params is None:
            params = {}
        return Paginator(self, params)
    
  

    def get(self,identity,params=None, headers=None):
        """Get a single payment.

        Retrieves the details of a single existing payment.

        Args:
              identity (string): Unique identifier, beginning with "PM".
              params (dict, optional): Query string parameters.

        Returns:
              ListResponse of Payment instances
        """
        path = self._sub_url_params('/payments/:identity', {
          
            'identity': identity,
          })
        

        response = self._perform_request('GET', path, params, headers,
                                         retry_failures=True)
        return self._resource_for(response)
  

    def update(self,identity,params=None, headers=None):
        """Update a payment.

        Updates a payment object. This accepts only the metadata parameter.

        Args:
              identity (string): Unique identifier, beginning with "PM".
              params (dict, optional): Request body.

        Returns:
              ListResponse of Payment instances
        """
        path = self._sub_url_params('/payments/:identity', {
          
            'identity': identity,
          })
        
        if params is not None:
            params = {self._envelope_key(): params}

        response = self._perform_request('PUT', path, params, headers,
                                         retry_failures=True)
        return self._resource_for(response)
  

    def cancel(self,identity,params=None, headers=None):
        """Cancel a payment.

        Cancels the payment if it has not already been submitted to the banks.
        Any metadata supplied to this endpoint will be stored on the payment
        cancellation event it causes.
        
        This will fail with a `cancellation_failed` error unless the payment's
        status is `pending_submission`.

        Args:
              identity (string): Unique identifier, beginning with "PM".
              params (dict, optional): Request body.

        Returns:
              ListResponse of Payment instances
        """
        path = self._sub_url_params('/payments/:identity/actions/cancel', {
          
            'identity': identity,
          })
        
        if params is not None:
            params = {'data': params}
        response = self._perform_request('POST', path, params, headers,
                                         retry_failures=False)
        return self._resource_for(response)
  

    def retry(self,identity,params=None, headers=None):
        """Retry a payment.

        <a name="retry_failed"></a>Retries a failed payment if the underlying
        mandate is active. You will receive a `resubmission_requested` webhook,
        but after that retrying the payment follows the same process as its
        initial creation, so you will receive a `submitted` webhook, followed
        by a `confirmed` or `failed` event. Any metadata supplied to this
        endpoint will be stored against the payment submission event it causes.
        
        This will return a `retry_failed` error if the payment has not failed.
        
        Payments can be retried up to 3 times.

        Args:
              identity (string): Unique identifier, beginning with "PM".
              params (dict, optional): Request body.

        Returns:
              ListResponse of Payment instances
        """
        path = self._sub_url_params('/payments/:identity/actions/retry', {
          
            'identity': identity,
          })
        
        if params is not None:
            params = {'data': params}
        response = self._perform_request('POST', path, params, headers,
                                         retry_failures=False)
        return self._resource_for(response)
  
