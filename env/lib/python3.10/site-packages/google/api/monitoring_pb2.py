# -*- coding: utf-8 -*-

# Copyright 2020 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/monitoring.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\x1bgoogle/api/monitoring.proto\x12\ngoogle.api"\xec\x01\n\nMonitoring\x12K\n\x15producer_destinations\x18\x01 \x03(\x0b\x32,.google.api.Monitoring.MonitoringDestination\x12K\n\x15\x63onsumer_destinations\x18\x02 \x03(\x0b\x32,.google.api.Monitoring.MonitoringDestination\x1a\x44\n\x15MonitoringDestination\x12\x1a\n\x12monitored_resource\x18\x01 \x01(\t\x12\x0f\n\x07metrics\x18\x02 \x03(\tBq\n\x0e\x63om.google.apiB\x0fMonitoringProtoP\x01ZEgoogle.golang.org/genproto/googleapis/api/serviceconfig;serviceconfig\xa2\x02\x04GAPIb\x06proto3'
)


_MONITORING = DESCRIPTOR.message_types_by_name["Monitoring"]
_MONITORING_MONITORINGDESTINATION = _MONITORING.nested_types_by_name[
    "MonitoringDestination"
]
Monitoring = _reflection.GeneratedProtocolMessageType(
    "Monitoring",
    (_message.Message,),
    {
        "MonitoringDestination": _reflection.GeneratedProtocolMessageType(
            "MonitoringDestination",
            (_message.Message,),
            {
                "DESCRIPTOR": _MONITORING_MONITORINGDESTINATION,
                "__module__": "google.api.monitoring_pb2"
                # @@protoc_insertion_point(class_scope:google.api.Monitoring.MonitoringDestination)
            },
        ),
        "DESCRIPTOR": _MONITORING,
        "__module__": "google.api.monitoring_pb2"
        # @@protoc_insertion_point(class_scope:google.api.Monitoring)
    },
)
_sym_db.RegisterMessage(Monitoring)
_sym_db.RegisterMessage(Monitoring.MonitoringDestination)

if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\n\016com.google.apiB\017MonitoringProtoP\001ZEgoogle.golang.org/genproto/googleapis/api/serviceconfig;serviceconfig\242\002\004GAPI"
    _MONITORING._serialized_start = 44
    _MONITORING._serialized_end = 280
    _MONITORING_MONITORINGDESTINATION._serialized_start = 212
    _MONITORING_MONITORINGDESTINATION._serialized_end = 280
# @@protoc_insertion_point(module_scope)
