# -*- coding: utf-8 -*-

# Copyright 2020 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/policy.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import descriptor_pb2 as google_dot_protobuf_dot_descriptor__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\x17google/api/policy.proto\x12\ngoogle.api\x1a google/protobuf/descriptor.proto"S\n\x0b\x46ieldPolicy\x12\x10\n\x08selector\x18\x01 \x01(\t\x12\x1b\n\x13resource_permission\x18\x02 \x01(\t\x12\x15\n\rresource_type\x18\x03 \x01(\t"S\n\x0cMethodPolicy\x12\x10\n\x08selector\x18\t \x01(\t\x12\x31\n\x10request_policies\x18\x02 \x03(\x0b\x32\x17.google.api.FieldPolicy:O\n\x0c\x66ield_policy\x12\x1d.google.protobuf.FieldOptions\x18\xe8\xce\xc1K \x01(\x0b\x32\x17.google.api.FieldPolicy:R\n\rmethod_policy\x12\x1e.google.protobuf.MethodOptions\x18\xb5\x97\x99M \x01(\x0b\x32\x18.google.api.MethodPolicyBp\n\x0e\x63om.google.apiB\x0bPolicyProtoP\x01ZEgoogle.golang.org/genproto/googleapis/api/serviceconfig;serviceconfig\xf8\x01\x01\xa2\x02\x04GAPIb\x06proto3'
)


FIELD_POLICY_FIELD_NUMBER = 158361448
field_policy = DESCRIPTOR.extensions_by_name["field_policy"]
METHOD_POLICY_FIELD_NUMBER = 161893301
method_policy = DESCRIPTOR.extensions_by_name["method_policy"]

_FIELDPOLICY = DESCRIPTOR.message_types_by_name["FieldPolicy"]
_METHODPOLICY = DESCRIPTOR.message_types_by_name["MethodPolicy"]
FieldPolicy = _reflection.GeneratedProtocolMessageType(
    "FieldPolicy",
    (_message.Message,),
    {
        "DESCRIPTOR": _FIELDPOLICY,
        "__module__": "google.api.policy_pb2"
        # @@protoc_insertion_point(class_scope:google.api.FieldPolicy)
    },
)
_sym_db.RegisterMessage(FieldPolicy)

MethodPolicy = _reflection.GeneratedProtocolMessageType(
    "MethodPolicy",
    (_message.Message,),
    {
        "DESCRIPTOR": _METHODPOLICY,
        "__module__": "google.api.policy_pb2"
        # @@protoc_insertion_point(class_scope:google.api.MethodPolicy)
    },
)
_sym_db.RegisterMessage(MethodPolicy)

if _descriptor._USE_C_DESCRIPTORS == False:
    google_dot_protobuf_dot_descriptor__pb2.FieldOptions.RegisterExtension(field_policy)
    google_dot_protobuf_dot_descriptor__pb2.MethodOptions.RegisterExtension(
        method_policy
    )

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\n\016com.google.apiB\013PolicyProtoP\001ZEgoogle.golang.org/genproto/googleapis/api/serviceconfig;serviceconfig\370\001\001\242\002\004GAPI"
    _FIELDPOLICY._serialized_start = 73
    _FIELDPOLICY._serialized_end = 156
    _METHODPOLICY._serialized_start = 158
    _METHODPOLICY._serialized_end = 241
# @@protoc_insertion_point(module_scope)
