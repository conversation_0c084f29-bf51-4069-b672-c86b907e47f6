# -*- coding: utf-8 -*-

# Copyright 2020 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/usage.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\x16google/api/usage.proto\x12\ngoogle.api"j\n\x05Usage\x12\x14\n\x0crequirements\x18\x01 \x03(\t\x12$\n\x05rules\x18\x06 \x03(\x0b\x32\x15.google.api.UsageRule\x12%\n\x1dproducer_notification_channel\x18\x07 \x01(\t"]\n\tUsageRule\x12\x10\n\x08selector\x18\x01 \x01(\t\x12 \n\x18\x61llow_unregistered_calls\x18\x02 \x01(\x08\x12\x1c\n\x14skip_service_control\x18\x03 \x01(\x08\x42l\n\x0e\x63om.google.apiB\nUsageProtoP\x01ZEgoogle.golang.org/genproto/googleapis/api/serviceconfig;serviceconfig\xa2\x02\x04GAPIb\x06proto3'
)


_USAGE = DESCRIPTOR.message_types_by_name["Usage"]
_USAGERULE = DESCRIPTOR.message_types_by_name["UsageRule"]
Usage = _reflection.GeneratedProtocolMessageType(
    "Usage",
    (_message.Message,),
    {
        "DESCRIPTOR": _USAGE,
        "__module__": "google.api.usage_pb2"
        # @@protoc_insertion_point(class_scope:google.api.Usage)
    },
)
_sym_db.RegisterMessage(Usage)

UsageRule = _reflection.GeneratedProtocolMessageType(
    "UsageRule",
    (_message.Message,),
    {
        "DESCRIPTOR": _USAGERULE,
        "__module__": "google.api.usage_pb2"
        # @@protoc_insertion_point(class_scope:google.api.UsageRule)
    },
)
_sym_db.RegisterMessage(UsageRule)

if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\n\016com.google.apiB\nUsageProtoP\001ZEgoogle.golang.org/genproto/googleapis/api/serviceconfig;serviceconfig\242\002\004GAPI"
    _USAGE._serialized_start = 38
    _USAGE._serialized_end = 144
    _USAGERULE._serialized_start = 146
    _USAGERULE._serialized_end = 239
# @@protoc_insertion_point(module_scope)
