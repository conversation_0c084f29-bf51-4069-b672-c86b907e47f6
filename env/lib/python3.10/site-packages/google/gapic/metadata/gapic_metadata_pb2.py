# -*- coding: utf-8 -*-

# Copyright 2020 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/gapic/metadata/gapic_metadata.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n*google/gapic/metadata/gapic_metadata.proto\x12\x15google.gapic.metadata"\xf0\x05\n\rGapicMetadata\x12\x0e\n\x06schema\x18\x01 \x01(\t\x12\x0f\n\x07\x63omment\x18\x02 \x01(\t\x12\x10\n\x08language\x18\x03 \x01(\t\x12\x15\n\rproto_package\x18\x04 \x01(\t\x12\x17\n\x0flibrary_package\x18\x05 \x01(\t\x12\x44\n\x08services\x18\x06 \x03(\x0b\x32\x32.google.gapic.metadata.GapicMetadata.ServicesEntry\x1ai\n\rServicesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12G\n\x05value\x18\x02 \x01(\x0b\x32\x38.google.gapic.metadata.GapicMetadata.ServiceForTransport:\x02\x38\x01\x1a\xd3\x01\n\x13ServiceForTransport\x12V\n\x07\x63lients\x18\x01 \x03(\x0b\x32\x45.google.gapic.metadata.GapicMetadata.ServiceForTransport.ClientsEntry\x1a\x64\n\x0c\x43lientsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x43\n\x05value\x18\x02 \x01(\x0b\x32\x34.google.gapic.metadata.GapicMetadata.ServiceAsClient:\x02\x38\x01\x1a\xd5\x01\n\x0fServiceAsClient\x12\x16\n\x0elibrary_client\x18\x01 \x01(\t\x12L\n\x04rpcs\x18\x02 \x03(\x0b\x32>.google.gapic.metadata.GapicMetadata.ServiceAsClient.RpcsEntry\x1a\\\n\tRpcsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12>\n\x05value\x18\x02 \x01(\x0b\x32/.google.gapic.metadata.GapicMetadata.MethodList:\x02\x38\x01\x1a\x1d\n\nMethodList\x12\x0f\n\x07methods\x18\x01 \x03(\tB\xba\x01\n\x19\x63om.google.gapic.metadataB\x12GapicMetadataProtoP\x01Z=google.golang.org/genproto/googleapis/gapic/metadata;metadata\xaa\x02\x15Google.Gapic.Metadata\xca\x02\x15Google\\Gapic\\Metadata\xea\x02\x17Google::Gapic::Metadatab\x06proto3'
)


_GAPICMETADATA = DESCRIPTOR.message_types_by_name["GapicMetadata"]
_GAPICMETADATA_SERVICESENTRY = _GAPICMETADATA.nested_types_by_name["ServicesEntry"]
_GAPICMETADATA_SERVICEFORTRANSPORT = _GAPICMETADATA.nested_types_by_name[
    "ServiceForTransport"
]
_GAPICMETADATA_SERVICEFORTRANSPORT_CLIENTSENTRY = (
    _GAPICMETADATA_SERVICEFORTRANSPORT.nested_types_by_name["ClientsEntry"]
)
_GAPICMETADATA_SERVICEASCLIENT = _GAPICMETADATA.nested_types_by_name["ServiceAsClient"]
_GAPICMETADATA_SERVICEASCLIENT_RPCSENTRY = (
    _GAPICMETADATA_SERVICEASCLIENT.nested_types_by_name["RpcsEntry"]
)
_GAPICMETADATA_METHODLIST = _GAPICMETADATA.nested_types_by_name["MethodList"]
GapicMetadata = _reflection.GeneratedProtocolMessageType(
    "GapicMetadata",
    (_message.Message,),
    {
        "ServicesEntry": _reflection.GeneratedProtocolMessageType(
            "ServicesEntry",
            (_message.Message,),
            {
                "DESCRIPTOR": _GAPICMETADATA_SERVICESENTRY,
                "__module__": "google.gapic.metadata.gapic_metadata_pb2"
                # @@protoc_insertion_point(class_scope:google.gapic.metadata.GapicMetadata.ServicesEntry)
            },
        ),
        "ServiceForTransport": _reflection.GeneratedProtocolMessageType(
            "ServiceForTransport",
            (_message.Message,),
            {
                "ClientsEntry": _reflection.GeneratedProtocolMessageType(
                    "ClientsEntry",
                    (_message.Message,),
                    {
                        "DESCRIPTOR": _GAPICMETADATA_SERVICEFORTRANSPORT_CLIENTSENTRY,
                        "__module__": "google.gapic.metadata.gapic_metadata_pb2"
                        # @@protoc_insertion_point(class_scope:google.gapic.metadata.GapicMetadata.ServiceForTransport.ClientsEntry)
                    },
                ),
                "DESCRIPTOR": _GAPICMETADATA_SERVICEFORTRANSPORT,
                "__module__": "google.gapic.metadata.gapic_metadata_pb2"
                # @@protoc_insertion_point(class_scope:google.gapic.metadata.GapicMetadata.ServiceForTransport)
            },
        ),
        "ServiceAsClient": _reflection.GeneratedProtocolMessageType(
            "ServiceAsClient",
            (_message.Message,),
            {
                "RpcsEntry": _reflection.GeneratedProtocolMessageType(
                    "RpcsEntry",
                    (_message.Message,),
                    {
                        "DESCRIPTOR": _GAPICMETADATA_SERVICEASCLIENT_RPCSENTRY,
                        "__module__": "google.gapic.metadata.gapic_metadata_pb2"
                        # @@protoc_insertion_point(class_scope:google.gapic.metadata.GapicMetadata.ServiceAsClient.RpcsEntry)
                    },
                ),
                "DESCRIPTOR": _GAPICMETADATA_SERVICEASCLIENT,
                "__module__": "google.gapic.metadata.gapic_metadata_pb2"
                # @@protoc_insertion_point(class_scope:google.gapic.metadata.GapicMetadata.ServiceAsClient)
            },
        ),
        "MethodList": _reflection.GeneratedProtocolMessageType(
            "MethodList",
            (_message.Message,),
            {
                "DESCRIPTOR": _GAPICMETADATA_METHODLIST,
                "__module__": "google.gapic.metadata.gapic_metadata_pb2"
                # @@protoc_insertion_point(class_scope:google.gapic.metadata.GapicMetadata.MethodList)
            },
        ),
        "DESCRIPTOR": _GAPICMETADATA,
        "__module__": "google.gapic.metadata.gapic_metadata_pb2"
        # @@protoc_insertion_point(class_scope:google.gapic.metadata.GapicMetadata)
    },
)
_sym_db.RegisterMessage(GapicMetadata)
_sym_db.RegisterMessage(GapicMetadata.ServicesEntry)
_sym_db.RegisterMessage(GapicMetadata.ServiceForTransport)
_sym_db.RegisterMessage(GapicMetadata.ServiceForTransport.ClientsEntry)
_sym_db.RegisterMessage(GapicMetadata.ServiceAsClient)
_sym_db.RegisterMessage(GapicMetadata.ServiceAsClient.RpcsEntry)
_sym_db.RegisterMessage(GapicMetadata.MethodList)

if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\n\031com.google.gapic.metadataB\022GapicMetadataProtoP\001Z=google.golang.org/genproto/googleapis/gapic/metadata;metadata\252\002\025Google.Gapic.Metadata\312\002\025Google\\Gapic\\Metadata\352\002\027Google::Gapic::Metadata"
    _GAPICMETADATA_SERVICESENTRY._options = None
    _GAPICMETADATA_SERVICESENTRY._serialized_options = b"8\001"
    _GAPICMETADATA_SERVICEFORTRANSPORT_CLIENTSENTRY._options = None
    _GAPICMETADATA_SERVICEFORTRANSPORT_CLIENTSENTRY._serialized_options = b"8\001"
    _GAPICMETADATA_SERVICEASCLIENT_RPCSENTRY._options = None
    _GAPICMETADATA_SERVICEASCLIENT_RPCSENTRY._serialized_options = b"8\001"
    _GAPICMETADATA._serialized_start = 70
    _GAPICMETADATA._serialized_end = 822
    _GAPICMETADATA_SERVICESENTRY._serialized_start = 256
    _GAPICMETADATA_SERVICESENTRY._serialized_end = 361
    _GAPICMETADATA_SERVICEFORTRANSPORT._serialized_start = 364
    _GAPICMETADATA_SERVICEFORTRANSPORT._serialized_end = 575
    _GAPICMETADATA_SERVICEFORTRANSPORT_CLIENTSENTRY._serialized_start = 475
    _GAPICMETADATA_SERVICEFORTRANSPORT_CLIENTSENTRY._serialized_end = 575
    _GAPICMETADATA_SERVICEASCLIENT._serialized_start = 578
    _GAPICMETADATA_SERVICEASCLIENT._serialized_end = 791
    _GAPICMETADATA_SERVICEASCLIENT_RPCSENTRY._serialized_start = 699
    _GAPICMETADATA_SERVICEASCLIENT_RPCSENTRY._serialized_end = 791
    _GAPICMETADATA_METHODLIST._serialized_start = 793
    _GAPICMETADATA_METHODLIST._serialized_end = 822
# @@protoc_insertion_point(module_scope)
