# -*- coding: utf-8 -*-

# Copyright 2020 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/type/phone_number.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\x1egoogle/type/phone_number.proto\x12\x0bgoogle.type"\xab\x01\n\x0bPhoneNumber\x12\x15\n\x0b\x65\x31\x36\x34_number\x18\x01 \x01(\tH\x00\x12\x38\n\nshort_code\x18\x02 \x01(\x0b\x32".google.type.PhoneNumber.ShortCodeH\x00\x12\x11\n\textension\x18\x03 \x01(\t\x1a\x30\n\tShortCode\x12\x13\n\x0bregion_code\x18\x01 \x01(\t\x12\x0e\n\x06number\x18\x02 \x01(\tB\x06\n\x04kindBt\n\x0f\x63om.google.typeB\x10PhoneNumberProtoP\x01ZDgoogle.golang.org/genproto/googleapis/type/phone_number;phone_number\xf8\x01\x01\xa2\x02\x03GTPb\x06proto3'
)


_PHONENUMBER = DESCRIPTOR.message_types_by_name["PhoneNumber"]
_PHONENUMBER_SHORTCODE = _PHONENUMBER.nested_types_by_name["ShortCode"]
PhoneNumber = _reflection.GeneratedProtocolMessageType(
    "PhoneNumber",
    (_message.Message,),
    {
        "ShortCode": _reflection.GeneratedProtocolMessageType(
            "ShortCode",
            (_message.Message,),
            {
                "DESCRIPTOR": _PHONENUMBER_SHORTCODE,
                "__module__": "google.type.phone_number_pb2"
                # @@protoc_insertion_point(class_scope:google.type.PhoneNumber.ShortCode)
            },
        ),
        "DESCRIPTOR": _PHONENUMBER,
        "__module__": "google.type.phone_number_pb2"
        # @@protoc_insertion_point(class_scope:google.type.PhoneNumber)
    },
)
_sym_db.RegisterMessage(PhoneNumber)
_sym_db.RegisterMessage(PhoneNumber.ShortCode)

if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\n\017com.google.typeB\020PhoneNumberProtoP\001ZDgoogle.golang.org/genproto/googleapis/type/phone_number;phone_number\370\001\001\242\002\003GTP"
    _PHONENUMBER._serialized_start = 48
    _PHONENUMBER._serialized_end = 219
    _PHONENUMBER_SHORTCODE._serialized_start = 163
    _PHONENUMBER_SHORTCODE._serialized_end = 211
# @@protoc_insertion_point(module_scope)
