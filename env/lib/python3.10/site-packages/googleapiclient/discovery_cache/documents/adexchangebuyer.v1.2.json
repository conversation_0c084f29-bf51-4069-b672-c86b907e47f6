{"kind": "discovery#restDescription", "etag": "\"J3WqvAcMk4eQjJXvfSI4Yr8VouA/F_9WJQnvLgGfABacvXvQLtkcuXw\"", "discoveryVersion": "v1", "id": "adexchangebuyer:v1.2", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "canonicalName": "Ad Exchange Buyer", "version": "v1.2", "revision": "********", "title": "Ad Exchange Buyer API", "description": "Accesses your bidding-account information, submits creatives for validation, finds available direct deals, and retrieves performance reports.", "ownerDomain": "google.com", "ownerName": "Google", "icons": {"x16": "https://www.google.com/images/icons/product/doubleclick-16.gif", "x32": "https://www.google.com/images/icons/product/doubleclick-32.gif"}, "documentationLink": "https://developers.google.com/ad-exchange/buyer-rest", "protocol": "rest", "baseUrl": "https://www.googleapis.com/adexchangebuyer/v1.2/", "basePath": "/adexchangebuyer/v1.2/", "rootUrl": "https://www.googleapis.com/", "servicePath": "adexchangebuyer/v1.2/", "batchPath": "batch/adexchangebuyer/v1.2", "parameters": {"alt": {"type": "string", "description": "Data format for the response.", "default": "json", "enum": ["json"], "enumDescriptions": ["Responses with Content-Type of application/json"], "location": "query"}, "fields": {"type": "string", "description": "Selector specifying which fields to include in a partial response.", "location": "query"}, "key": {"type": "string", "description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query"}, "oauth_token": {"type": "string", "description": "OAuth 2.0 token for the current user.", "location": "query"}, "prettyPrint": {"type": "boolean", "description": "Returns response with indentations and line breaks.", "default": "true", "location": "query"}, "quotaUser": {"type": "string", "description": "An opaque string that represents a user for quota purposes. Must not exceed 40 characters.", "location": "query"}, "userIp": {"type": "string", "description": "Deprecated. Please use quotaUser instead.", "location": "query"}}, "auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/adexchange.buyer": {"description": "Manage your Ad Exchange buyer account configuration"}}}}, "schemas": {"Account": {"id": "Account", "type": "object", "description": "Configuration data for an Ad Exchange buyer account.", "properties": {"bidderLocation": {"type": "array", "description": "Your bidder locations that have distinct URLs.", "items": {"type": "object", "properties": {"maximumQps": {"type": "integer", "description": "The maximum queries per second the Ad Exchange will send.", "format": "int32"}, "region": {"type": "string", "description": "The geographical region the Ad Exchange should send requests from. Only used by some quota systems, but always setting the value is recommended. Allowed values:  \n- ASIA \n- EUROPE \n- US_EAST \n- US_WEST"}, "url": {"type": "string", "description": "The URL to which the Ad Exchange will send bid requests."}}}}, "cookieMatchingNid": {"type": "string", "description": "The nid parameter value used in cookie match requests. Please contact your technical account manager if you need to change this."}, "cookieMatchingUrl": {"type": "string", "description": "The base URL used in cookie match requests."}, "id": {"type": "integer", "description": "Account id.", "format": "int32"}, "kind": {"type": "string", "description": "Resource type.", "default": "adexchangebuyer#account"}, "maximumActiveCreatives": {"type": "integer", "description": "The maximum number of active creatives that an account can have, where a creative is active if it was inserted or bid with in the last 30 days. Please contact your technical account manager if you need to change this.", "format": "int32"}, "maximumTotalQps": {"type": "integer", "description": "The sum of all bidderLocation.maximumQps values cannot exceed this. Please contact your technical account manager if you need to change this.", "format": "int32"}, "numberActiveCreatives": {"type": "integer", "description": "The number of creatives that this account inserted or bid with in the last 30 days.", "format": "int32"}}}, "AccountsList": {"id": "AccountsList", "type": "object", "description": "An account feed lists Ad Exchange buyer accounts that the user has access to. Each entry in the feed corresponds to a single buyer account.", "properties": {"items": {"type": "array", "description": "A list of accounts.", "items": {"$ref": "Account"}}, "kind": {"type": "string", "description": "Resource type.", "default": "adexchangebuyer#accountsList"}}}, "Creative": {"id": "Creative", "type": "object", "description": "A creative and its classification data.", "properties": {"HTMLSnippet": {"type": "string", "description": "The HTML snippet that displays the ad when inserted in the web page. If set, videoURL should not be set."}, "accountId": {"type": "integer", "description": "Account id.", "format": "int32", "annotations": {"required": ["adexchangebuyer.creatives.insert"]}}, "advertiserId": {"type": "array", "description": "Detected advertiser id, if any. Read-only. This field should not be set in requests.", "items": {"type": "string", "format": "int64"}}, "advertiserName": {"type": "string", "description": "The name of the company being advertised in the creative.", "annotations": {"required": ["adexchangebuyer.creatives.insert"]}}, "agencyId": {"type": "string", "description": "The agency id for this creative.", "format": "int64"}, "apiUploadTimestamp": {"type": "string", "description": "The last upload timestamp of this creative if it was uploaded via API. Read-only. The value of this field is generated, and will be ignored for uploads. (formatted RFC 3339 timestamp).", "format": "date-time"}, "attribute": {"type": "array", "description": "All attributes for the ads that may be shown from this snippet.", "items": {"type": "integer", "format": "int32"}}, "buyerCreativeId": {"type": "string", "description": "A buyer-specific id identifying the creative in this ad.", "annotations": {"required": ["adexchangebuyer.creatives.insert"]}}, "clickThroughUrl": {"type": "array", "description": "The set of destination urls for the snippet.", "items": {"type": "string"}, "annotations": {"required": ["adexchangebuyer.creatives.insert"]}}, "corrections": {"type": "array", "description": "Shows any corrections that were applied to this creative. Read-only. This field should not be set in requests.", "items": {"type": "object", "properties": {"details": {"type": "array", "description": "Additional details about the correction.", "items": {"type": "string"}}, "reason": {"type": "string", "description": "The type of correction that was applied to the creative."}}}}, "disapprovalReasons": {"type": "array", "description": "The reasons for disapproval, if any. Note that not all disapproval reasons may be categorized, so it is possible for the creative to have a status of DISAPPROVED with an empty list for disapproval_reasons. In this case, please reach out to your TAM to help debug the issue. Read-only. This field should not be set in requests.", "items": {"type": "object", "properties": {"details": {"type": "array", "description": "Additional details about the reason for disapproval.", "items": {"type": "string"}}, "reason": {"type": "string", "description": "The categorized reason for disapproval."}}}}, "filteringReasons": {"type": "object", "description": "The filtering reasons for the creative. Read-only. This field should not be set in requests.", "properties": {"date": {"type": "string", "description": "The date in ISO 8601 format for the data. The data is collected from 00:00:00 to 23:59:59 in PST."}, "reasons": {"type": "array", "description": "The filtering reasons.", "items": {"type": "object", "properties": {"filteringCount": {"type": "string", "description": "The number of times the creative was filtered for the status. The count is aggregated across all publishers on the exchange.", "format": "int64"}, "filteringStatus": {"type": "integer", "description": "The filtering status code. Please refer to the creative-status-codes.txt file for different statuses.", "format": "int32"}}}}}}, "height": {"type": "integer", "description": "Ad height.", "format": "int32", "annotations": {"required": ["adexchangebuyer.creatives.insert"]}}, "impressionTrackingUrl": {"type": "array", "description": "The set of urls to be called to record an impression.", "items": {"type": "string"}}, "kind": {"type": "string", "description": "Resource type.", "default": "adexchangebuyer#creative"}, "productCategories": {"type": "array", "description": "Detected product categories, if any. Read-only. This field should not be set in requests.", "items": {"type": "integer", "format": "int32"}}, "restrictedCategories": {"type": "array", "description": "All restricted categories for the ads that may be shown from this snippet.", "items": {"type": "integer", "format": "int32"}}, "sensitiveCategories": {"type": "array", "description": "Detected sensitive categories, if any. Read-only. This field should not be set in requests.", "items": {"type": "integer", "format": "int32"}}, "status": {"type": "string", "description": "Creative serving status. Read-only. This field should not be set in requests."}, "vendorType": {"type": "array", "description": "All vendor types for the ads that may be shown from this snippet.", "items": {"type": "integer", "format": "int32"}}, "version": {"type": "integer", "description": "The version for this creative. Read-only. This field should not be set in requests.", "format": "int32"}, "videoURL": {"type": "string", "description": "The url to fetch a video ad. If set, HTMLSnippet should not be set."}, "width": {"type": "integer", "description": "Ad width.", "format": "int32", "annotations": {"required": ["adexchangebuyer.creatives.insert"]}}}}, "CreativesList": {"id": "CreativesList", "type": "object", "description": "The creatives feed lists the active creatives for the Ad Exchange buyer accounts that the user has access to. Each entry in the feed corresponds to a single creative.", "properties": {"items": {"type": "array", "description": "A list of creatives.", "items": {"$ref": "Creative"}}, "kind": {"type": "string", "description": "Resource type.", "default": "adexchangebuyer#creativesList"}, "nextPageToken": {"type": "string", "description": "Continuation token used to page through creatives. To retrieve the next page of results, set the next request's \"pageToken\" value to this."}}}}, "resources": {"accounts": {"methods": {"get": {"id": "adexchangebuyer.accounts.get", "path": "accounts/{id}", "httpMethod": "GET", "description": "Gets one account by ID.", "parameters": {"id": {"type": "integer", "description": "The account id", "required": true, "format": "int32", "location": "path"}}, "parameterOrder": ["id"], "response": {"$ref": "Account"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "list": {"id": "adexchangebuyer.accounts.list", "path": "accounts", "httpMethod": "GET", "description": "Retrieves the authenticated user's list of accounts.", "response": {"$ref": "AccountsList"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "patch": {"id": "adexchangebuyer.accounts.patch", "path": "accounts/{id}", "httpMethod": "PATCH", "description": "Updates an existing account. This method supports patch semantics.", "parameters": {"id": {"type": "integer", "description": "The account id", "required": true, "format": "int32", "location": "path"}}, "parameterOrder": ["id"], "request": {"$ref": "Account"}, "response": {"$ref": "Account"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "update": {"id": "adexchangebuyer.accounts.update", "path": "accounts/{id}", "httpMethod": "PUT", "description": "Updates an existing account.", "parameters": {"id": {"type": "integer", "description": "The account id", "required": true, "format": "int32", "location": "path"}}, "parameterOrder": ["id"], "request": {"$ref": "Account"}, "response": {"$ref": "Account"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}, "creatives": {"methods": {"get": {"id": "adexchangebuyer.creatives.get", "path": "creatives/{accountId}/{buyerCreativeId}", "httpMethod": "GET", "description": "Gets the status for a single creative. A creative will be available 30-40 minutes after submission.", "parameters": {"accountId": {"type": "integer", "description": "The id for the account that will serve this creative.", "required": true, "format": "int32", "location": "path"}, "buyerCreativeId": {"type": "string", "description": "The buyer-specific id for this creative.", "required": true, "location": "path"}}, "parameterOrder": ["accountId", "buyerCreativeId"], "response": {"$ref": "Creative"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "insert": {"id": "adexchangebuyer.creatives.insert", "path": "creatives", "httpMethod": "POST", "description": "Submit a new creative.", "request": {"$ref": "Creative"}, "response": {"$ref": "Creative"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "list": {"id": "adexchangebuyer.creatives.list", "path": "creatives", "httpMethod": "GET", "description": "Retrieves a list of the authenticated user's active creatives. A creative will be available 30-40 minutes after submission.", "parameters": {"maxResults": {"type": "integer", "description": "Maximum number of entries returned on one result page. If not set, the default is 100. Optional.", "format": "uint32", "minimum": "1", "maximum": "1000", "location": "query"}, "pageToken": {"type": "string", "description": "A continuation token, used to page through ad clients. To retrieve the next page, set this parameter to the value of \"nextPageToken\" from the previous response. Optional.", "location": "query"}, "statusFilter": {"type": "string", "description": "When specified, only creatives having the given status are returned.", "enum": ["approved", "disapproved", "not_checked"], "enumDescriptions": ["Creatives which have been approved.", "Creatives which have been disapproved.", "Creatives whose status is not yet checked."], "location": "query"}}, "response": {"$ref": "CreativesList"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}}}