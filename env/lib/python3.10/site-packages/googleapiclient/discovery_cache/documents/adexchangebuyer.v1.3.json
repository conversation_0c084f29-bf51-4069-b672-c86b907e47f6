{"kind": "discovery#restDescription", "etag": "\"F5McR9eEaw0XRpaO3M9gbIugkbs/SfJ_2QNA8p2fWGlW3-bjoNlrlZ0\"", "discoveryVersion": "v1", "id": "adexchangebuyer:v1.3", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "canonicalName": "Ad Exchange Buyer", "version": "v1.3", "revision": "********", "title": "Ad Exchange Buyer API", "description": "Accesses your bidding-account information, submits creatives for validation, finds available direct deals, and retrieves performance reports.", "ownerDomain": "google.com", "ownerName": "Google", "icons": {"x16": "https://www.google.com/images/icons/product/doubleclick-16.gif", "x32": "https://www.google.com/images/icons/product/doubleclick-32.gif"}, "documentationLink": "https://developers.google.com/ad-exchange/buyer-rest", "protocol": "rest", "baseUrl": "https://www.googleapis.com/adexchangebuyer/v1.3/", "basePath": "/adexchangebuyer/v1.3/", "rootUrl": "https://www.googleapis.com/", "servicePath": "adexchangebuyer/v1.3/", "batchPath": "batch/adexchangebuyer/v1.3", "parameters": {"alt": {"type": "string", "description": "Data format for the response.", "default": "json", "enum": ["json"], "enumDescriptions": ["Responses with Content-Type of application/json"], "location": "query"}, "fields": {"type": "string", "description": "Selector specifying which fields to include in a partial response.", "location": "query"}, "key": {"type": "string", "description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query"}, "oauth_token": {"type": "string", "description": "OAuth 2.0 token for the current user.", "location": "query"}, "prettyPrint": {"type": "boolean", "description": "Returns response with indentations and line breaks.", "default": "true", "location": "query"}, "quotaUser": {"type": "string", "description": "An opaque string that represents a user for quota purposes. Must not exceed 40 characters.", "location": "query"}, "userIp": {"type": "string", "description": "Deprecated. Please use quotaUser instead.", "location": "query"}}, "auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/adexchange.buyer": {"description": "Manage your Ad Exchange buyer account configuration"}}}}, "schemas": {"Account": {"id": "Account", "type": "object", "description": "Configuration data for an Ad Exchange buyer account.", "properties": {"bidderLocation": {"type": "array", "description": "Your bidder locations that have distinct URLs.", "items": {"type": "object", "properties": {"maximumQps": {"type": "integer", "description": "The maximum queries per second the Ad Exchange will send.", "format": "int32"}, "region": {"type": "string", "description": "The geographical region the Ad Exchange should send requests from. Only used by some quota systems, but always setting the value is recommended. Allowed values:  \n- ASIA \n- EUROPE \n- US_EAST \n- US_WEST"}, "url": {"type": "string", "description": "The URL to which the Ad Exchange will send bid requests."}}}}, "cookieMatchingNid": {"type": "string", "description": "The nid parameter value used in cookie match requests. Please contact your technical account manager if you need to change this."}, "cookieMatchingUrl": {"type": "string", "description": "The base URL used in cookie match requests."}, "id": {"type": "integer", "description": "Account id.", "format": "int32"}, "kind": {"type": "string", "description": "Resource type.", "default": "adexchangebuyer#account"}, "maximumActiveCreatives": {"type": "integer", "description": "The maximum number of active creatives that an account can have, where a creative is active if it was inserted or bid with in the last 30 days. Please contact your technical account manager if you need to change this.", "format": "int32"}, "maximumTotalQps": {"type": "integer", "description": "The sum of all bidderLocation.maximumQps values cannot exceed this. Please contact your technical account manager if you need to change this.", "format": "int32"}, "numberActiveCreatives": {"type": "integer", "description": "The number of creatives that this account inserted or bid with in the last 30 days.", "format": "int32"}}}, "AccountsList": {"id": "AccountsList", "type": "object", "description": "An account feed lists Ad Exchange buyer accounts that the user has access to. Each entry in the feed corresponds to a single buyer account.", "properties": {"items": {"type": "array", "description": "A list of accounts.", "items": {"$ref": "Account"}}, "kind": {"type": "string", "description": "Resource type.", "default": "adexchangebuyer#accountsList"}}}, "BillingInfo": {"id": "BillingInfo", "type": "object", "description": "The configuration data for an Ad Exchange billing info.", "properties": {"accountId": {"type": "integer", "description": "Account id.", "format": "int32"}, "accountName": {"type": "string", "description": "Account name."}, "billingId": {"type": "array", "description": "A list of adgroup IDs associated with this particular account. These IDs may show up as part of a realtime bidding BidRequest, which indicates a bid request for this account.", "items": {"type": "string"}}, "kind": {"type": "string", "description": "Resource type.", "default": "adexchangebuyer#billingInfo"}}}, "BillingInfoList": {"id": "BillingInfoList", "type": "object", "description": "A billing info feed lists Billing Info the Ad Exchange buyer account has access to. Each entry in the feed corresponds to a single billing info.", "properties": {"items": {"type": "array", "description": "A list of billing info relevant for your account.", "items": {"$ref": "BillingInfo"}}, "kind": {"type": "string", "description": "Resource type.", "default": "adexchangebuyer#billingInfoList"}}}, "Budget": {"id": "Budget", "type": "object", "description": "The configuration data for Ad Exchange RTB - Budget API.", "properties": {"accountId": {"type": "string", "description": "The id of the account. This is required for get and update requests.", "format": "int64"}, "billingId": {"type": "string", "description": "The billing id to determine which adgroup to provide budget information for. This is required for get and update requests.", "format": "int64"}, "budgetAmount": {"type": "string", "description": "The daily budget amount in unit amount of the account currency to apply for the billingId provided. This is required for update requests.", "format": "int64"}, "currencyCode": {"type": "string", "description": "The currency code for the buyer. This cannot be altered here."}, "id": {"type": "string", "description": "The unique id that describes this item."}, "kind": {"type": "string", "description": "The kind of the resource, i.e. \"adexchangebuyer#budget\".", "default": "adexchangebuyer#budget"}}}, "Creative": {"id": "Creative", "type": "object", "description": "A creative and its classification data.", "properties": {"HTMLSnippet": {"type": "string", "description": "The HTML snippet that displays the ad when inserted in the web page. If set, videoURL should not be set."}, "accountId": {"type": "integer", "description": "Account id.", "format": "int32", "annotations": {"required": ["adexchangebuyer.creatives.insert"]}}, "adTechnologyProviders": {"type": "object", "properties": {"detectedProviderIds": {"type": "array", "description": "The detected ad technology provider IDs for this creative. See https://storage.googleapis.com/adx-rtb-dictionaries/providers.csv for mapping of provider ID to provided name, a privacy policy URL, and a list of domains which can be attributed to the provider. If this creative contains provider IDs that are outside of those listed in the `BidRequest.adslot.consented_providers_settings.consented_providers` field on the  Authorized Buyers Real-Time Bidding protocol or the `BidRequest.user.ext.consented_providers_settings.consented_providers` field on the OpenRTB protocol, a bid submitted for a European Economic Area (EEA) user with this creative is not compliant with the GDPR policies as mentioned in the \"Third-party Ad Technology Vendors\" section of Authorized Buyers Program Guidelines.", "items": {"type": "string", "format": "int64"}}, "hasUnidentifiedProvider": {"type": "boolean", "description": "Whether the creative contains an unidentified ad technology provider. If true, a bid submitted for a European Economic Area (EEA) user with this creative is not compliant with the GDPR policies as mentioned in the \"Third-party Ad Technology Vendors\" section of Authorized Buyers Program Guidelines."}}}, "advertiserId": {"type": "array", "description": "Detected advertiser id, if any. Read-only. This field should not be set in requests.", "items": {"type": "string", "format": "int64"}}, "advertiserName": {"type": "string", "description": "The name of the company being advertised in the creative.", "annotations": {"required": ["adexchangebuyer.creatives.insert"]}}, "agencyId": {"type": "string", "description": "The agency id for this creative.", "format": "int64"}, "apiUploadTimestamp": {"type": "string", "description": "The last upload timestamp of this creative if it was uploaded via API. Read-only. The value of this field is generated, and will be ignored for uploads. (formatted RFC 3339 timestamp).", "format": "date-time"}, "attribute": {"type": "array", "description": "All attributes for the ads that may be shown from this snippet.", "items": {"type": "integer", "format": "int32"}}, "buyerCreativeId": {"type": "string", "description": "A buyer-specific id identifying the creative in this ad.", "annotations": {"required": ["adexchangebuyer.creatives.insert"]}}, "clickThroughUrl": {"type": "array", "description": "The set of destination urls for the snippet.", "items": {"type": "string"}, "annotations": {"required": ["adexchangebuyer.creatives.insert"]}}, "corrections": {"type": "array", "description": "Shows any corrections that were applied to this creative. Read-only. This field should not be set in requests.", "items": {"type": "object", "properties": {"details": {"type": "array", "description": "Additional details about the correction.", "items": {"type": "string"}}, "reason": {"type": "string", "description": "The type of correction that was applied to the creative."}}}}, "disapprovalReasons": {"type": "array", "description": "The reasons for disapproval, if any. Note that not all disapproval reasons may be categorized, so it is possible for the creative to have a status of DISAPPROVED with an empty list for disapproval_reasons. In this case, please reach out to your TAM to help debug the issue. Read-only. This field should not be set in requests.", "items": {"type": "object", "properties": {"details": {"type": "array", "description": "Additional details about the reason for disapproval.", "items": {"type": "string"}}, "reason": {"type": "string", "description": "The categorized reason for disapproval."}}}}, "filteringReasons": {"type": "object", "description": "The filtering reasons for the creative. Read-only. This field should not be set in requests.", "properties": {"date": {"type": "string", "description": "The date in ISO 8601 format for the data. The data is collected from 00:00:00 to 23:59:59 in PST."}, "reasons": {"type": "array", "description": "The filtering reasons.", "items": {"type": "object", "properties": {"filteringCount": {"type": "string", "description": "The number of times the creative was filtered for the status. The count is aggregated across all publishers on the exchange.", "format": "int64"}, "filteringStatus": {"type": "integer", "description": "The filtering status code. Please refer to the creative-status-codes.txt file for different statuses.", "format": "int32"}}}}}}, "height": {"type": "integer", "description": "Ad height.", "format": "int32", "annotations": {"required": ["adexchangebuyer.creatives.insert"]}}, "impressionTrackingUrl": {"type": "array", "description": "The set of urls to be called to record an impression.", "items": {"type": "string"}}, "kind": {"type": "string", "description": "Resource type.", "default": "adexchangebuyer#creative"}, "nativeAd": {"type": "object", "description": "If nativeAd is set, HTMLSnippet and videoURL should not be set.", "properties": {"advertiser": {"type": "string"}, "appIcon": {"type": "object", "description": "The app icon, for app download ads.", "properties": {"height": {"type": "integer", "format": "int32"}, "url": {"type": "string"}, "width": {"type": "integer", "format": "int32"}}}, "body": {"type": "string", "description": "A long description of the ad."}, "callToAction": {"type": "string", "description": "A label for the button that the user is supposed to click."}, "clickTrackingUrl": {"type": "string", "description": "The URL to use for click tracking."}, "headline": {"type": "string", "description": "A short title for the ad."}, "image": {"type": "object", "description": "A large image.", "properties": {"height": {"type": "integer", "format": "int32"}, "url": {"type": "string"}, "width": {"type": "integer", "format": "int32"}}}, "impressionTrackingUrl": {"type": "array", "description": "The URLs are called when the impression is rendered.", "items": {"type": "string"}}, "logo": {"type": "object", "description": "A smaller image, for the advertiser logo.", "properties": {"height": {"type": "integer", "format": "int32"}, "url": {"type": "string"}, "width": {"type": "integer", "format": "int32"}}}, "price": {"type": "string", "description": "The price of the promoted app including the currency info."}, "starRating": {"type": "number", "description": "The app rating in the app store. Must be in the range [0-5].", "format": "double"}}}, "productCategories": {"type": "array", "description": "Detected product categories, if any. Read-only. This field should not be set in requests.", "items": {"type": "integer", "format": "int32"}}, "restrictedCategories": {"type": "array", "description": "All restricted categories for the ads that may be shown from this snippet.", "items": {"type": "integer", "format": "int32"}}, "sensitiveCategories": {"type": "array", "description": "Detected sensitive categories, if any. Read-only. This field should not be set in requests.", "items": {"type": "integer", "format": "int32"}}, "status": {"type": "string", "description": "Creative serving status. Read-only. This field should not be set in requests."}, "vendorType": {"type": "array", "description": "All vendor types for the ads that may be shown from this snippet.", "items": {"type": "integer", "format": "int32"}}, "version": {"type": "integer", "description": "The version for this creative. Read-only. This field should not be set in requests.", "format": "int32"}, "videoURL": {"type": "string", "description": "The URL to fetch a video ad. If set, HTMLSnippet and the nativeAd should not be set."}, "width": {"type": "integer", "description": "Ad width.", "format": "int32", "annotations": {"required": ["adexchangebuyer.creatives.insert"]}}}}, "CreativesList": {"id": "CreativesList", "type": "object", "description": "The creatives feed lists the active creatives for the Ad Exchange buyer accounts that the user has access to. Each entry in the feed corresponds to a single creative.", "properties": {"items": {"type": "array", "description": "A list of creatives.", "items": {"$ref": "Creative"}}, "kind": {"type": "string", "description": "Resource type.", "default": "adexchangebuyer#creativesList"}, "nextPageToken": {"type": "string", "description": "Continuation token used to page through creatives. To retrieve the next page of results, set the next request's \"pageToken\" value to this."}}}, "DirectDeal": {"id": "DirectDeal", "type": "object", "description": "The configuration data for an Ad Exchange direct deal.", "properties": {"accountId": {"type": "integer", "description": "The account id of the buyer this deal is for.", "format": "int32"}, "advertiser": {"type": "string", "description": "The name of the advertiser this deal is for."}, "allowsAlcohol": {"type": "boolean", "description": "Whether the publisher for this deal is eligible for alcohol ads."}, "buyerAccountId": {"type": "string", "description": "The account id that this deal was negotiated for. It is either the buyer or the client that this deal was negotiated on behalf of.", "format": "int64"}, "currencyCode": {"type": "string", "description": "The currency code that applies to the fixed_cpm value. If not set then assumed to be USD."}, "dealTier": {"type": "string", "description": "The deal type such as programmatic reservation or fixed price and so on."}, "endTime": {"type": "string", "description": "End time for when this deal stops being active. If not set then this deal is valid until manually disabled by the publisher. In seconds since the epoch.", "format": "int64"}, "fixedCpm": {"type": "string", "description": "The fixed price for this direct deal. In cpm micros of currency according to currency_code. If set, then this deal is eligible for the fixed price tier of buying (highest priority, pay exactly the configured fixed price).", "format": "int64"}, "id": {"type": "string", "description": "Deal id.", "format": "int64"}, "kind": {"type": "string", "description": "Resource type.", "default": "adexchangebuyer#directDeal"}, "name": {"type": "string", "description": "Deal name."}, "privateExchangeMinCpm": {"type": "string", "description": "The minimum price for this direct deal. In cpm micros of currency according to currency_code. If set, then this deal is eligible for the private exchange tier of buying (below fixed price priority, run as a second price auction).", "format": "int64"}, "publisherBlocksOverriden": {"type": "boolean", "description": "If true, the publisher has opted to have their blocks ignored when a creative is bid with for this deal."}, "sellerNetwork": {"type": "string", "description": "The name of the publisher offering this direct deal."}, "startTime": {"type": "string", "description": "Start time for when this deal becomes active. If not set then this deal is active immediately upon creation. In seconds since the epoch.", "format": "int64"}}}, "DirectDealsList": {"id": "DirectDealsList", "type": "object", "description": "A direct deals feed lists Direct Deals the Ad Exchange buyer account has access to. This includes direct deals set up for the buyer account as well as its merged stream seats.", "properties": {"directDeals": {"type": "array", "description": "A list of direct deals relevant for your account.", "items": {"$ref": "DirectDeal"}}, "kind": {"type": "string", "description": "Resource type.", "default": "adexchangebuyer#directDealsList"}}}, "PerformanceReport": {"id": "PerformanceReport", "type": "object", "description": "The configuration data for an Ad Exchange performance report list.", "properties": {"bidRate": {"type": "number", "description": "The number of bid responses with an ad.", "format": "double"}, "bidRequestRate": {"type": "number", "description": "The number of bid requests sent to your bidder.", "format": "double"}, "calloutStatusRate": {"type": "array", "description": "Rate of various prefiltering statuses per match. Please refer to the callout-status-codes.txt file for different statuses.", "items": {"type": "any"}}, "cookieMatcherStatusRate": {"type": "array", "description": "Average QPS for cookie matcher operations.", "items": {"type": "any"}}, "creativeStatusRate": {"type": "array", "description": "Rate of ads with a given status. Please refer to the creative-status-codes.txt file for different statuses.", "items": {"type": "any"}}, "filteredBidRate": {"type": "number", "description": "The number of bid responses that were filtered due to a policy violation or other errors.", "format": "double"}, "hostedMatchStatusRate": {"type": "array", "description": "Average QPS for hosted match operations.", "items": {"type": "any"}}, "inventoryMatchRate": {"type": "number", "description": "The number of potential queries based on your pretargeting settings.", "format": "double"}, "kind": {"type": "string", "description": "Resource type.", "default": "adexchangebuyer#performanceReport"}, "latency50thPercentile": {"type": "number", "description": "The 50th percentile round trip latency(ms) as perceived from Google servers for the duration period covered by the report.", "format": "double"}, "latency85thPercentile": {"type": "number", "description": "The 85th percentile round trip latency(ms) as perceived from Google servers for the duration period covered by the report.", "format": "double"}, "latency95thPercentile": {"type": "number", "description": "The 95th percentile round trip latency(ms) as perceived from Google servers for the duration period covered by the report.", "format": "double"}, "noQuotaInRegion": {"type": "number", "description": "Rate of various quota account statuses per quota check.", "format": "double"}, "outOfQuota": {"type": "number", "description": "Rate of various quota account statuses per quota check.", "format": "double"}, "pixelMatchRequests": {"type": "number", "description": "Average QPS for pixel match requests from clients.", "format": "double"}, "pixelMatchResponses": {"type": "number", "description": "Average QPS for pixel match responses from clients.", "format": "double"}, "quotaConfiguredLimit": {"type": "number", "description": "The configured quota limits for this account.", "format": "double"}, "quotaThrottledLimit": {"type": "number", "description": "The throttled quota limits for this account.", "format": "double"}, "region": {"type": "string", "description": "The trading location of this data."}, "successfulRequestRate": {"type": "number", "description": "The number of properly formed bid responses received by our servers within the deadline.", "format": "double"}, "timestamp": {"type": "string", "description": "The unix timestamp of the starting time of this performance data.", "format": "int64"}, "unsuccessfulRequestRate": {"type": "number", "description": "The number of bid responses that were unsuccessful due to timeouts, incorrect formatting, etc.", "format": "double"}}}, "PerformanceReportList": {"id": "PerformanceReportList", "type": "object", "description": "The configuration data for an Ad Exchange performance report list.", "properties": {"kind": {"type": "string", "description": "Resource type.", "default": "adexchangebuyer#performanceReportList"}, "performanceReport": {"type": "array", "description": "A list of performance reports relevant for the account.", "items": {"$ref": "PerformanceReport"}}}}, "PretargetingConfig": {"id": "PretargetingConfig", "type": "object", "properties": {"billingId": {"type": "string", "description": "The id for billing purposes, provided for reference. Leave this field blank for insert requests; the id will be generated automatically.", "format": "int64"}, "configId": {"type": "string", "description": "The config id; generated automatically. Leave this field blank for insert requests.", "format": "int64"}, "configName": {"type": "string", "description": "The name of the config. Must be unique. Required for all requests."}, "creativeType": {"type": "array", "description": "List must contain exactly one of PRETARGETING_CREATIVE_TYPE_HTML or PRETARGETING_CREATIVE_TYPE_VIDEO.", "items": {"type": "string"}}, "dimensions": {"type": "array", "description": "Requests which allow one of these (width, height) pairs will match. All pairs must be supported ad dimensions.", "items": {"type": "object", "properties": {"height": {"type": "string", "description": "Height in pixels.", "format": "int64"}, "width": {"type": "string", "description": "Width in pixels.", "format": "int64"}}}}, "excludedContentLabels": {"type": "array", "description": "Requests with any of these content labels will not match. Values are from content-labels.txt in the downloadable files section.", "items": {"type": "string", "format": "int64"}}, "excludedGeoCriteriaIds": {"type": "array", "description": "Requests containing any of these geo criteria ids will not match.", "items": {"type": "string", "format": "int64"}}, "excludedPlacements": {"type": "array", "description": "Requests containing any of these placements will not match.", "items": {"type": "object", "properties": {"token": {"type": "string", "description": "The value of the placement. Interpretation depends on the placement type, e.g. URL for a site placement, channel name for a channel placement, app id for a mobile app placement."}, "type": {"type": "string", "description": "The type of the placement."}}}}, "excludedUserLists": {"type": "array", "description": "Requests containing any of these users list ids will not match.", "items": {"type": "string", "format": "int64"}}, "excludedVerticals": {"type": "array", "description": "Requests containing any of these vertical ids will not match. Values are from the publisher-verticals.txt file in the downloadable files section.", "items": {"type": "string", "format": "int64"}}, "geoCriteriaIds": {"type": "array", "description": "Requests containing any of these geo criteria ids will match.", "items": {"type": "string", "format": "int64"}}, "isActive": {"type": "boolean", "description": "Whether this config is active. Required for all requests."}, "kind": {"type": "string", "description": "The kind of the resource, i.e. \"adexchangebuyer#pretargetingConfig\".", "default": "adexchangebuyer#pretargetingConfig"}, "languages": {"type": "array", "description": "Request containing any of these language codes will match.", "items": {"type": "string"}}, "maximumQps": {"type": "string", "description": "The maximum QPS allocated to this pretargeting configuration, used for pretargeting-level QPS limits. By default, this is not set, which indicates that there is no QPS limit at the configuration level (a global or account-level limit may still be imposed).", "format": "int64"}, "mobileCarriers": {"type": "array", "description": "Requests containing any of these mobile carrier ids will match. Values are from mobile-carriers.csv in the downloadable files section.", "items": {"type": "string", "format": "int64"}}, "mobileDevices": {"type": "array", "description": "Requests containing any of these mobile device ids will match. Values are from mobile-devices.csv in the downloadable files section.", "items": {"type": "string", "format": "int64"}}, "mobileOperatingSystemVersions": {"type": "array", "description": "Requests containing any of these mobile operating system version ids will match. Values are from mobile-os.csv in the downloadable files section.", "items": {"type": "string", "format": "int64"}}, "placements": {"type": "array", "description": "Requests containing any of these placements will match.", "items": {"type": "object", "properties": {"token": {"type": "string", "description": "The value of the placement. Interpretation depends on the placement type, e.g. URL for a site placement, channel name for a channel placement, app id for a mobile app placement."}, "type": {"type": "string", "description": "The type of the placement."}}}}, "platforms": {"type": "array", "description": "Requests matching any of these platforms will match. Possible values are PRETARGETING_PLATFORM_MOBILE, PRETARGETING_PLATFORM_DESKTOP, and PRETARGETING_PLATFORM_TABLET.", "items": {"type": "string"}}, "supportedCreativeAttributes": {"type": "array", "description": "Creative attributes should be declared here if all creatives corresponding to this pretargeting configuration have that creative attribute. Values are from pretargetable-creative-attributes.txt in the downloadable files section.", "items": {"type": "string", "format": "int64"}}, "userLists": {"type": "array", "description": "Requests containing any of these user list ids will match.", "items": {"type": "string", "format": "int64"}}, "vendorTypes": {"type": "array", "description": "Requests that allow any of these vendor ids will match. Values are from vendors.txt in the downloadable files section.", "items": {"type": "string", "format": "int64"}}, "verticals": {"type": "array", "description": "Requests containing any of these vertical ids will match.", "items": {"type": "string", "format": "int64"}}}}, "PretargetingConfigList": {"id": "PretargetingConfigList", "type": "object", "properties": {"items": {"type": "array", "description": "A list of pretargeting configs", "items": {"$ref": "PretargetingConfig"}}, "kind": {"type": "string", "description": "Resource type.", "default": "adexchangebuyer#pretargetingConfigList"}}}}, "resources": {"accounts": {"methods": {"get": {"id": "adexchangebuyer.accounts.get", "path": "accounts/{id}", "httpMethod": "GET", "description": "Gets one account by ID.", "parameters": {"id": {"type": "integer", "description": "The account id", "required": true, "format": "int32", "location": "path"}}, "parameterOrder": ["id"], "response": {"$ref": "Account"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "list": {"id": "adexchangebuyer.accounts.list", "path": "accounts", "httpMethod": "GET", "description": "Retrieves the authenticated user's list of accounts.", "response": {"$ref": "AccountsList"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "patch": {"id": "adexchangebuyer.accounts.patch", "path": "accounts/{id}", "httpMethod": "PATCH", "description": "Updates an existing account. This method supports patch semantics.", "parameters": {"id": {"type": "integer", "description": "The account id", "required": true, "format": "int32", "location": "path"}}, "parameterOrder": ["id"], "request": {"$ref": "Account"}, "response": {"$ref": "Account"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "update": {"id": "adexchangebuyer.accounts.update", "path": "accounts/{id}", "httpMethod": "PUT", "description": "Updates an existing account.", "parameters": {"id": {"type": "integer", "description": "The account id", "required": true, "format": "int32", "location": "path"}}, "parameterOrder": ["id"], "request": {"$ref": "Account"}, "response": {"$ref": "Account"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}, "billingInfo": {"methods": {"get": {"id": "adexchangebuyer.billingInfo.get", "path": "billinginfo/{accountId}", "httpMethod": "GET", "description": "Returns the billing information for one account specified by account ID.", "parameters": {"accountId": {"type": "integer", "description": "The account id.", "required": true, "format": "int32", "location": "path"}}, "parameterOrder": ["accountId"], "response": {"$ref": "BillingInfo"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "list": {"id": "adexchangebuyer.billingInfo.list", "path": "billinginfo", "httpMethod": "GET", "description": "Retrieves a list of billing information for all accounts of the authenticated user.", "response": {"$ref": "BillingInfoList"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}, "budget": {"methods": {"get": {"id": "adexchangebuyer.budget.get", "path": "billinginfo/{accountId}/{billingId}", "httpMethod": "GET", "description": "Returns the budget information for the adgroup specified by the accountId and billingId.", "parameters": {"accountId": {"type": "string", "description": "The account id to get the budget information for.", "required": true, "format": "int64", "location": "path"}, "billingId": {"type": "string", "description": "The billing id to get the budget information for.", "required": true, "format": "int64", "location": "path"}}, "parameterOrder": ["accountId", "billingId"], "response": {"$ref": "Budget"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "patch": {"id": "adexchangebuyer.budget.patch", "path": "billinginfo/{accountId}/{billingId}", "httpMethod": "PATCH", "description": "Updates the budget amount for the budget of the adgroup specified by the accountId and billingId, with the budget amount in the request. This method supports patch semantics.", "parameters": {"accountId": {"type": "string", "description": "The account id associated with the budget being updated.", "required": true, "format": "int64", "location": "path"}, "billingId": {"type": "string", "description": "The billing id associated with the budget being updated.", "required": true, "format": "int64", "location": "path"}}, "parameterOrder": ["accountId", "billingId"], "request": {"$ref": "Budget"}, "response": {"$ref": "Budget"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "update": {"id": "adexchangebuyer.budget.update", "path": "billinginfo/{accountId}/{billingId}", "httpMethod": "PUT", "description": "Updates the budget amount for the budget of the adgroup specified by the accountId and billingId, with the budget amount in the request.", "parameters": {"accountId": {"type": "string", "description": "The account id associated with the budget being updated.", "required": true, "format": "int64", "location": "path"}, "billingId": {"type": "string", "description": "The billing id associated with the budget being updated.", "required": true, "format": "int64", "location": "path"}}, "parameterOrder": ["accountId", "billingId"], "request": {"$ref": "Budget"}, "response": {"$ref": "Budget"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}, "creatives": {"methods": {"get": {"id": "adexchangebuyer.creatives.get", "path": "creatives/{accountId}/{buyerCreativeId}", "httpMethod": "GET", "description": "Gets the status for a single creative. A creative will be available 30-40 minutes after submission.", "parameters": {"accountId": {"type": "integer", "description": "The id for the account that will serve this creative.", "required": true, "format": "int32", "location": "path"}, "buyerCreativeId": {"type": "string", "description": "The buyer-specific id for this creative.", "required": true, "location": "path"}}, "parameterOrder": ["accountId", "buyerCreativeId"], "response": {"$ref": "Creative"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "insert": {"id": "adexchangebuyer.creatives.insert", "path": "creatives", "httpMethod": "POST", "description": "Submit a new creative.", "request": {"$ref": "Creative"}, "response": {"$ref": "Creative"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "list": {"id": "adexchangebuyer.creatives.list", "path": "creatives", "httpMethod": "GET", "description": "Retrieves a list of the authenticated user's active creatives. A creative will be available 30-40 minutes after submission.", "parameters": {"accountId": {"type": "integer", "description": "When specified, only creatives for the given account ids are returned.", "format": "int32", "repeated": true, "location": "query"}, "buyerCreativeId": {"type": "string", "description": "When specified, only creatives for the given buyer creative ids are returned.", "repeated": true, "location": "query"}, "maxResults": {"type": "integer", "description": "Maximum number of entries returned on one result page. If not set, the default is 100. Optional.", "format": "uint32", "minimum": "1", "maximum": "1000", "location": "query"}, "pageToken": {"type": "string", "description": "A continuation token, used to page through ad clients. To retrieve the next page, set this parameter to the value of \"nextPageToken\" from the previous response. Optional.", "location": "query"}, "statusFilter": {"type": "string", "description": "When specified, only creatives having the given status are returned.", "enum": ["approved", "disapproved", "not_checked"], "enumDescriptions": ["Creatives which have been approved.", "Creatives which have been disapproved.", "Creatives whose status is not yet checked."], "location": "query"}}, "response": {"$ref": "CreativesList"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}, "directDeals": {"methods": {"get": {"id": "adexchangebuyer.directDeals.get", "path": "directdeals/{id}", "httpMethod": "GET", "description": "Gets one direct deal by ID.", "parameters": {"id": {"type": "string", "description": "The direct deal id", "required": true, "format": "int64", "location": "path"}}, "parameterOrder": ["id"], "response": {"$ref": "DirectDeal"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "list": {"id": "adexchangebuyer.directDeals.list", "path": "directdeals", "httpMethod": "GET", "description": "Retrieves the authenticated user's list of direct deals.", "response": {"$ref": "DirectDealsList"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}, "performanceReport": {"methods": {"list": {"id": "adexchangebuyer.performanceReport.list", "path": "performancereport", "httpMethod": "GET", "description": "Retrieves the authenticated user's list of performance metrics.", "parameters": {"accountId": {"type": "string", "description": "The account id to get the reports.", "required": true, "format": "int64", "location": "query"}, "endDateTime": {"type": "string", "description": "The end time of the report in ISO 8601 timestamp format using UTC.", "required": true, "location": "query"}, "maxResults": {"type": "integer", "description": "Maximum number of entries returned on one result page. If not set, the default is 100. Optional.", "format": "uint32", "minimum": "1", "maximum": "1000", "location": "query"}, "pageToken": {"type": "string", "description": "A continuation token, used to page through performance reports. To retrieve the next page, set this parameter to the value of \"nextPageToken\" from the previous response. Optional.", "location": "query"}, "startDateTime": {"type": "string", "description": "The start time of the report in ISO 8601 timestamp format using UTC.", "required": true, "location": "query"}}, "parameterOrder": ["accountId", "endDateTime", "startDateTime"], "response": {"$ref": "PerformanceReportList"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}, "pretargetingConfig": {"methods": {"delete": {"id": "adexchangebuyer.pretargetingConfig.delete", "path": "pretargetingconfigs/{accountId}/{configId}", "httpMethod": "DELETE", "description": "Deletes an existing pretargeting config.", "parameters": {"accountId": {"type": "string", "description": "The account id to delete the pretargeting config for.", "required": true, "format": "int64", "location": "path"}, "configId": {"type": "string", "description": "The specific id of the configuration to delete.", "required": true, "format": "int64", "location": "path"}}, "parameterOrder": ["accountId", "configId"], "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "get": {"id": "adexchangebuyer.pretargetingConfig.get", "path": "pretargetingconfigs/{accountId}/{configId}", "httpMethod": "GET", "description": "Gets a specific pretargeting configuration", "parameters": {"accountId": {"type": "string", "description": "The account id to get the pretargeting config for.", "required": true, "format": "int64", "location": "path"}, "configId": {"type": "string", "description": "The specific id of the configuration to retrieve.", "required": true, "format": "int64", "location": "path"}}, "parameterOrder": ["accountId", "configId"], "response": {"$ref": "PretargetingConfig"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "insert": {"id": "adexchangebuyer.pretargetingConfig.insert", "path": "pretargetingconfigs/{accountId}", "httpMethod": "POST", "description": "Inserts a new pretargeting configuration.", "parameters": {"accountId": {"type": "string", "description": "The account id to insert the pretargeting config for.", "required": true, "format": "int64", "location": "path"}}, "parameterOrder": ["accountId"], "request": {"$ref": "PretargetingConfig"}, "response": {"$ref": "PretargetingConfig"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "list": {"id": "adexchangebuyer.pretargetingConfig.list", "path": "pretargetingconfigs/{accountId}", "httpMethod": "GET", "description": "Retrieves a list of the authenticated user's pretargeting configurations.", "parameters": {"accountId": {"type": "string", "description": "The account id to get the pretargeting configs for.", "required": true, "format": "int64", "location": "path"}}, "parameterOrder": ["accountId"], "response": {"$ref": "PretargetingConfigList"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "patch": {"id": "adexchangebuyer.pretargetingConfig.patch", "path": "pretargetingconfigs/{accountId}/{configId}", "httpMethod": "PATCH", "description": "Updates an existing pretargeting config. This method supports patch semantics.", "parameters": {"accountId": {"type": "string", "description": "The account id to update the pretargeting config for.", "required": true, "format": "int64", "location": "path"}, "configId": {"type": "string", "description": "The specific id of the configuration to update.", "required": true, "format": "int64", "location": "path"}}, "parameterOrder": ["accountId", "configId"], "request": {"$ref": "PretargetingConfig"}, "response": {"$ref": "PretargetingConfig"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "update": {"id": "adexchangebuyer.pretargetingConfig.update", "path": "pretargetingconfigs/{accountId}/{configId}", "httpMethod": "PUT", "description": "Updates an existing pretargeting config.", "parameters": {"accountId": {"type": "string", "description": "The account id to update the pretargeting config for.", "required": true, "format": "int64", "location": "path"}, "configId": {"type": "string", "description": "The specific id of the configuration to update.", "required": true, "format": "int64", "location": "path"}}, "parameterOrder": ["accountId", "configId"], "request": {"$ref": "PretargetingConfig"}, "response": {"$ref": "PretargetingConfig"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}}}