{"kind": "discovery#restDescription", "canonicalName": "AdSense", "protocol": "rest", "id": "adsense:v1.4", "revision": "********", "resources": {"payments": {"methods": {"list": {"id": "adsense.payments.list", "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"], "response": {"$ref": "Payments"}, "description": "List the payments for this AdSense account.", "httpMethod": "GET", "path": "payments"}}}, "savedadstyles": {"methods": {"list": {"description": "List all saved ad styles in the user's account.", "parameters": {"pageToken": {"location": "query", "description": "A continuation token, used to page through saved ad styles. To retrieve the next page, set this parameter to the value of \"nextPageToken\" from the previous response.", "type": "string"}, "maxResults": {"type": "integer", "location": "query", "format": "int32", "description": "The maximum number of saved ad styles to include in the response, used for paging.", "maximum": "10000", "minimum": "0"}}, "id": "adsense.savedadstyles.list", "path": "savedadstyles", "httpMethod": "GET", "response": {"$ref": "SavedAdStyles"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"]}, "get": {"httpMethod": "GET", "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"], "response": {"$ref": "SavedAdStyle"}, "parameterOrder": ["savedAdStyleId"], "path": "savedadstyles/{savedAdStyleId}", "parameters": {"savedAdStyleId": {"location": "path", "description": "Saved ad style to retrieve.", "required": true, "type": "string"}}, "id": "adsense.savedadstyles.get", "description": "Get a specific saved ad style from the user's account."}}}, "adclients": {"methods": {"list": {"httpMethod": "GET", "parameters": {"pageToken": {"description": "A continuation token, used to page through ad clients. To retrieve the next page, set this parameter to the value of \"nextPageToken\" from the previous response.", "type": "string", "location": "query"}, "maxResults": {"minimum": "0", "type": "integer", "description": "The maximum number of ad clients to include in the response, used for paging.", "location": "query", "format": "int32", "maximum": "10000"}}, "path": "adclients", "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"], "id": "adsense.adclients.list", "response": {"$ref": "AdClients"}, "description": "List all ad clients in this AdSense account."}}}, "customchannels": {"methods": {"get": {"parameterOrder": ["adClientId", "customChannelId"], "path": "adclients/{adClientId}/customchannels/{customChannelId}", "httpMethod": "GET", "parameters": {"adClientId": {"required": true, "type": "string", "location": "path", "description": "Ad client which contains the custom channel."}, "customChannelId": {"location": "path", "description": "Custom channel to retrieve.", "required": true, "type": "string"}}, "description": "Get the specified custom channel from the specified ad client.", "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"], "response": {"$ref": "CustomChannel"}, "id": "adsense.customchannels.get"}, "list": {"parameters": {"pageToken": {"location": "query", "description": "A continuation token, used to page through custom channels. To retrieve the next page, set this parameter to the value of \"nextPageToken\" from the previous response.", "type": "string"}, "maxResults": {"maximum": "10000", "description": "The maximum number of custom channels to include in the response, used for paging.", "type": "integer", "minimum": "0", "location": "query", "format": "int32"}, "adClientId": {"description": "Ad client for which to list custom channels.", "required": true, "location": "path", "type": "string"}}, "id": "adsense.customchannels.list", "parameterOrder": ["adClientId"], "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"], "httpMethod": "GET", "description": "List all custom channels in the specified ad client for this AdSense account.", "path": "adclients/{adClientId}/customchannels", "response": {"$ref": "CustomChannels"}}}, "resources": {"adunits": {"methods": {"list": {"path": "adclients/{adClientId}/customchannels/{customChannelId}/adunits", "id": "adsense.customchannels.adunits.list", "parameterOrder": ["adClientId", "customChannelId"], "httpMethod": "GET", "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"], "description": "List all ad units in the specified custom channel.", "response": {"$ref": "AdUnits"}, "parameters": {"maxResults": {"type": "integer", "minimum": "0", "description": "The maximum number of ad units to include in the response, used for paging.", "maximum": "10000", "format": "int32", "location": "query"}, "includeInactive": {"description": "Whether to include inactive ad units. Default: true.", "type": "boolean", "location": "query"}, "pageToken": {"description": "A continuation token, used to page through ad units. To retrieve the next page, set this parameter to the value of \"nextPageToken\" from the previous response.", "location": "query", "type": "string"}, "adClientId": {"required": true, "type": "string", "location": "path", "description": "Ad client which contains the custom channel."}, "customChannelId": {"required": true, "type": "string", "description": "Custom channel for which to list ad units.", "location": "path"}}}}}}}, "reports": {"resources": {"saved": {"methods": {"generate": {"httpMethod": "GET", "response": {"$ref": "AdsenseReportsGenerateResponse"}, "description": "Generate an AdSense report based on the saved report ID sent in the query parameters.", "path": "reports/{savedReportId}", "parameters": {"savedReportId": {"location": "path", "required": true, "type": "string", "description": "The saved report to retrieve."}, "locale": {"pattern": "[a-zA-Z_]+", "location": "query", "type": "string", "description": "Optional locale to use for translating report output to a local language. Defaults to \"en_US\" if not specified."}, "maxResults": {"type": "integer", "format": "int32", "maximum": "50000", "location": "query", "description": "The maximum number of rows of report data to return.", "minimum": "0"}, "startIndex": {"format": "int32", "location": "query", "minimum": "0", "type": "integer", "maximum": "5000", "description": "Index of the first row of report data to return."}}, "parameterOrder": ["savedReportId"], "id": "adsense.reports.saved.generate", "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"]}, "list": {"response": {"$ref": "SavedReports"}, "httpMethod": "GET", "parameters": {"maxResults": {"type": "integer", "location": "query", "minimum": "0", "format": "int32", "description": "The maximum number of saved reports to include in the response, used for paging.", "maximum": "100"}, "pageToken": {"description": "A continuation token, used to page through saved reports. To retrieve the next page, set this parameter to the value of \"nextPageToken\" from the previous response.", "type": "string", "location": "query"}}, "description": "List all saved reports in this AdSense account.", "path": "reports/saved", "id": "adsense.reports.saved.list", "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"]}}}}, "methods": {"generate": {"id": "adsense.reports.generate", "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"], "description": "Generate an AdSense report based on the report request sent in the query parameters. Returns the result as JSON; to retrieve output in CSV format specify \"alt=csv\" as a query parameter.", "response": {"$ref": "AdsenseReportsGenerateResponse"}, "supportsMediaDownload": true, "parameters": {"startIndex": {"minimum": "0", "format": "int32", "type": "integer", "maximum": "5000", "location": "query", "description": "Index of the first row of report data to return."}, "useTimezoneReporting": {"type": "boolean", "description": "Whether the report should be generated in the AdSense account's local timezone. If false default PST/PDT timezone will be used.", "location": "query"}, "filter": {"pattern": "[a-zA-Z_]+(==|=@).+", "type": "string", "location": "query", "repeated": true, "description": "Filters to be run on the report."}, "accountId": {"type": "string", "location": "query", "repeated": true, "description": "Accounts upon which to report."}, "currency": {"description": "Optional currency to use when reporting on monetary metrics. Defaults to the account's currency if not set.", "type": "string", "location": "query", "pattern": "[a-zA-Z]+"}, "locale": {"type": "string", "location": "query", "description": "Optional locale to use for translating report output to a local language. Defaults to \"en_US\" if not specified.", "pattern": "[a-zA-Z_]+"}, "dimension": {"type": "string", "pattern": "[a-zA-Z_]+", "repeated": true, "location": "query", "description": "Dimensions to base the report on."}, "maxResults": {"minimum": "0", "maximum": "50000", "location": "query", "type": "integer", "description": "The maximum number of rows of report data to return.", "format": "int32"}, "startDate": {"type": "string", "pattern": "\\d{4}-\\d{2}-\\d{2}|(today|startOfMonth|startOfYear)(([\\-\\+]\\d+[dwmy]){0,3}?)|(latest-(\\d{2})-(\\d{2})(-\\d+y)?)|(latest-latest-(\\d{2})(-\\d+m)?)", "location": "query", "required": true, "description": "Start of the date range to report on in \"YYYY-MM-DD\" format, inclusive."}, "metric": {"description": "Numeric columns to include in the report.", "location": "query", "type": "string", "pattern": "[a-zA-Z_]+", "repeated": true}, "endDate": {"type": "string", "required": true, "location": "query", "pattern": "\\d{4}-\\d{2}-\\d{2}|(today|startOfMonth|startOfYear)(([\\-\\+]\\d+[dwmy]){0,3}?)|(latest-(\\d{2})-(\\d{2})(-\\d+y)?)|(latest-latest-(\\d{2})(-\\d+m)?)", "description": "End of the date range to report on in \"YYYY-MM-DD\" format, inclusive."}, "sort": {"location": "query", "pattern": "(\\+|-)?[a-zA-Z_]+", "repeated": true, "type": "string", "description": "The name of a dimension or metric to sort the resulting report on, optionally prefixed with \"+\" to sort ascending or \"-\" to sort descending. If no prefix is specified, the column is sorted ascending."}}, "httpMethod": "GET", "parameterOrder": ["startDate", "endDate"], "path": "reports"}}}, "alerts": {"methods": {"list": {"id": "adsense.alerts.list", "response": {"$ref": "<PERSON><PERSON><PERSON>"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"], "parameters": {"locale": {"location": "query", "type": "string", "description": "The locale to use for translating alert messages. The account locale will be used if this is not supplied. The AdSense default (English) will be used if the supplied locale is invalid or unsupported."}}, "httpMethod": "GET", "description": "List the alerts for this AdSense account.", "path": "alerts"}, "delete": {"description": "Dismiss (delete) the specified alert from the publisher's AdSense account.", "id": "adsense.alerts.delete", "path": "alerts/{alertId}", "parameters": {"alertId": {"required": true, "description": "<PERSON><PERSON> to delete.", "location": "path", "type": "string"}}, "scopes": ["https://www.googleapis.com/auth/adsense"], "parameterOrder": ["alertId"], "httpMethod": "DELETE"}}}, "accounts": {"methods": {"list": {"path": "accounts", "parameters": {"pageToken": {"type": "string", "description": "A continuation token, used to page through accounts. To retrieve the next page, set this parameter to the value of \"nextPageToken\" from the previous response.", "location": "query"}, "maxResults": {"format": "int32", "maximum": "10000", "minimum": "0", "type": "integer", "description": "The maximum number of accounts to include in the response, used for paging.", "location": "query"}}, "httpMethod": "GET", "id": "adsense.accounts.list", "description": "List all accounts available to this AdSense account.", "response": {"$ref": "Accounts"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"]}, "get": {"scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"], "httpMethod": "GET", "response": {"$ref": "Account"}, "id": "adsense.accounts.get", "parameters": {"tree": {"type": "boolean", "location": "query", "description": "Whether the tree of sub accounts should be returned."}, "accountId": {"description": "Account to get information about.", "location": "path", "type": "string", "required": true}}, "path": "accounts/{accountId}", "description": "Get information about the selected AdSense account.", "parameterOrder": ["accountId"]}}, "resources": {"customchannels": {"resources": {"adunits": {"methods": {"list": {"parameterOrder": ["accountId", "adClientId", "customChannelId"], "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"], "description": "List all ad units in the specified custom channel.", "path": "accounts/{accountId}/adclients/{adClientId}/customchannels/{customChannelId}/adunits", "id": "adsense.accounts.customchannels.adunits.list", "parameters": {"includeInactive": {"type": "boolean", "description": "Whether to include inactive ad units. Default: true.", "location": "query"}, "accountId": {"type": "string", "required": true, "location": "path", "description": "Account to which the ad client belongs."}, "customChannelId": {"description": "Custom channel for which to list ad units.", "location": "path", "required": true, "type": "string"}, "pageToken": {"type": "string", "description": "A continuation token, used to page through ad units. To retrieve the next page, set this parameter to the value of \"nextPageToken\" from the previous response.", "location": "query"}, "maxResults": {"minimum": "0", "maximum": "10000", "description": "The maximum number of ad units to include in the response, used for paging.", "format": "int32", "type": "integer", "location": "query"}, "adClientId": {"location": "path", "required": true, "description": "Ad client which contains the custom channel.", "type": "string"}}, "httpMethod": "GET", "response": {"$ref": "AdUnits"}}}}}, "methods": {"list": {"id": "adsense.accounts.customchannels.list", "path": "accounts/{accountId}/adclients/{adClientId}/customchannels", "parameters": {"accountId": {"required": true, "location": "path", "type": "string", "description": "Account to which the ad client belongs."}, "pageToken": {"description": "A continuation token, used to page through custom channels. To retrieve the next page, set this parameter to the value of \"nextPageToken\" from the previous response.", "location": "query", "type": "string"}, "adClientId": {"description": "Ad client for which to list custom channels.", "required": true, "location": "path", "type": "string"}, "maxResults": {"location": "query", "minimum": "0", "description": "The maximum number of custom channels to include in the response, used for paging.", "maximum": "10000", "type": "integer", "format": "int32"}}, "httpMethod": "GET", "parameterOrder": ["accountId", "adClientId"], "description": "List all custom channels in the specified ad client for the specified account.", "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"], "response": {"$ref": "CustomChannels"}}, "get": {"scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"], "id": "adsense.accounts.customchannels.get", "parameterOrder": ["accountId", "adClientId", "customChannelId"], "httpMethod": "GET", "parameters": {"customChannelId": {"required": true, "description": "Custom channel to retrieve.", "type": "string", "location": "path"}, "accountId": {"required": true, "type": "string", "location": "path", "description": "Account to which the ad client belongs."}, "adClientId": {"type": "string", "required": true, "location": "path", "description": "Ad client which contains the custom channel."}}, "response": {"$ref": "CustomChannel"}, "description": "Get the specified custom channel from the specified ad client for the specified account.", "path": "accounts/{accountId}/adclients/{adClientId}/customchannels/{customChannelId}"}}}, "adunits": {"resources": {"customchannels": {"methods": {"list": {"response": {"$ref": "CustomChannels"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"], "description": "List all custom channels which the specified ad unit belongs to.", "parameterOrder": ["accountId", "adClientId", "adUnitId"], "path": "accounts/{accountId}/adclients/{adClientId}/adunits/{adUnitId}/customchannels", "id": "adsense.accounts.adunits.customchannels.list", "parameters": {"adUnitId": {"description": "Ad unit for which to list custom channels.", "required": true, "location": "path", "type": "string"}, "adClientId": {"description": "Ad client which contains the ad unit.", "location": "path", "required": true, "type": "string"}, "maxResults": {"location": "query", "minimum": "0", "format": "int32", "description": "The maximum number of custom channels to include in the response, used for paging.", "type": "integer", "maximum": "10000"}, "accountId": {"location": "path", "required": true, "description": "Account to which the ad client belongs.", "type": "string"}, "pageToken": {"type": "string", "location": "query", "description": "A continuation token, used to page through custom channels. To retrieve the next page, set this parameter to the value of \"nextPageToken\" from the previous response."}}, "httpMethod": "GET"}}}}, "methods": {"getAdCode": {"path": "accounts/{accountId}/adclients/{adClientId}/adunits/{adUnitId}/adcode", "httpMethod": "GET", "parameterOrder": ["accountId", "adClientId", "adUnitId"], "response": {"$ref": "AdCode"}, "id": "adsense.accounts.adunits.getAdCode", "description": "Get ad code for the specified ad unit.", "parameters": {"accountId": {"description": "Account which contains the ad client.", "required": true, "type": "string", "location": "path"}, "adClientId": {"description": "Ad client with contains the ad unit.", "required": true, "location": "path", "type": "string"}, "adUnitId": {"type": "string", "required": true, "location": "path", "description": "Ad unit to get the code for."}}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"]}, "list": {"parameterOrder": ["accountId", "adClientId"], "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"], "httpMethod": "GET", "description": "List all ad units in the specified ad client for the specified account.", "path": "accounts/{accountId}/adclients/{adClientId}/adunits", "id": "adsense.accounts.adunits.list", "response": {"$ref": "AdUnits"}, "parameters": {"adClientId": {"required": true, "description": "Ad client for which to list ad units.", "location": "path", "type": "string"}, "accountId": {"required": true, "type": "string", "description": "Account to which the ad client belongs.", "location": "path"}, "pageToken": {"type": "string", "location": "query", "description": "A continuation token, used to page through ad units. To retrieve the next page, set this parameter to the value of \"nextPageToken\" from the previous response."}, "includeInactive": {"description": "Whether to include inactive ad units. Default: true.", "type": "boolean", "location": "query"}, "maxResults": {"type": "integer", "location": "query", "maximum": "10000", "description": "The maximum number of ad units to include in the response, used for paging.", "format": "int32", "minimum": "0"}}}, "get": {"response": {"$ref": "AdUnit"}, "description": "Gets the specified ad unit in the specified ad client for the specified account.", "parameters": {"adClientId": {"description": "Ad client for which to get the ad unit.", "type": "string", "location": "path", "required": true}, "adUnitId": {"location": "path", "type": "string", "required": true, "description": "Ad unit to retrieve."}, "accountId": {"location": "path", "description": "Account to which the ad client belongs.", "type": "string", "required": true}}, "httpMethod": "GET", "parameterOrder": ["accountId", "adClientId", "adUnitId"], "id": "adsense.accounts.adunits.get", "path": "accounts/{accountId}/adclients/{adClientId}/adunits/{adUnitId}", "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"]}}}, "alerts": {"methods": {"list": {"scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"], "path": "accounts/{accountId}/alerts", "description": "List the alerts for the specified AdSense account.", "parameters": {"accountId": {"location": "path", "required": true, "type": "string", "description": "Account for which to retrieve the alerts."}, "locale": {"location": "query", "description": "The locale to use for translating alert messages. The account locale will be used if this is not supplied. The AdSense default (English) will be used if the supplied locale is invalid or unsupported.", "type": "string"}}, "httpMethod": "GET", "response": {"$ref": "<PERSON><PERSON><PERSON>"}, "id": "adsense.accounts.alerts.list", "parameterOrder": ["accountId"]}, "delete": {"description": "Dismiss (delete) the specified alert from the specified publisher AdSense account.", "parameters": {"accountId": {"location": "path", "required": true, "description": "Account which contains the ad unit.", "type": "string"}, "alertId": {"location": "path", "type": "string", "description": "<PERSON><PERSON> to delete.", "required": true}}, "path": "accounts/{accountId}/alerts/{alertId}", "scopes": ["https://www.googleapis.com/auth/adsense"], "parameterOrder": ["accountId", "alertId"], "httpMethod": "DELETE", "id": "adsense.accounts.alerts.delete"}}}, "savedadstyles": {"methods": {"get": {"id": "adsense.accounts.savedadstyles.get", "response": {"$ref": "SavedAdStyle"}, "path": "accounts/{accountId}/savedadstyles/{savedAdStyleId}", "description": "List a specific saved ad style for the specified account.", "parameters": {"savedAdStyleId": {"type": "string", "required": true, "description": "Saved ad style to retrieve.", "location": "path"}, "accountId": {"required": true, "location": "path", "type": "string", "description": "Account for which to get the saved ad style."}}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"], "httpMethod": "GET", "parameterOrder": ["accountId", "savedAdStyleId"]}, "list": {"description": "List all saved ad styles in the specified account.", "parameterOrder": ["accountId"], "httpMethod": "GET", "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"], "parameters": {"maxResults": {"minimum": "0", "type": "integer", "location": "query", "maximum": "10000", "description": "The maximum number of saved ad styles to include in the response, used for paging.", "format": "int32"}, "accountId": {"location": "path", "type": "string", "description": "Account for which to list saved ad styles.", "required": true}, "pageToken": {"location": "query", "type": "string", "description": "A continuation token, used to page through saved ad styles. To retrieve the next page, set this parameter to the value of \"nextPageToken\" from the previous response."}}, "id": "adsense.accounts.savedadstyles.list", "path": "accounts/{accountId}/savedadstyles", "response": {"$ref": "SavedAdStyles"}}}}, "reports": {"resources": {"saved": {"methods": {"generate": {"parameters": {"savedReportId": {"type": "string", "location": "path", "description": "The saved report to retrieve.", "required": true}, "accountId": {"description": "Account to which the saved reports belong.", "location": "path", "required": true, "type": "string"}, "startIndex": {"maximum": "5000", "format": "int32", "minimum": "0", "description": "Index of the first row of report data to return.", "location": "query", "type": "integer"}, "locale": {"pattern": "[a-zA-Z_]+", "type": "string", "location": "query", "description": "Optional locale to use for translating report output to a local language. Defaults to \"en_US\" if not specified."}, "maxResults": {"type": "integer", "location": "query", "format": "int32", "description": "The maximum number of rows of report data to return.", "maximum": "50000", "minimum": "0"}}, "parameterOrder": ["accountId", "savedReportId"], "description": "Generate an AdSense report based on the saved report ID sent in the query parameters.", "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"], "httpMethod": "GET", "id": "adsense.accounts.reports.saved.generate", "response": {"$ref": "AdsenseReportsGenerateResponse"}, "path": "accounts/{accountId}/reports/{savedReportId}"}, "list": {"httpMethod": "GET", "parameters": {"accountId": {"type": "string", "required": true, "location": "path", "description": "Account to which the saved reports belong."}, "maxResults": {"format": "int32", "minimum": "0", "maximum": "100", "type": "integer", "location": "query", "description": "The maximum number of saved reports to include in the response, used for paging."}, "pageToken": {"location": "query", "description": "A continuation token, used to page through saved reports. To retrieve the next page, set this parameter to the value of \"nextPageToken\" from the previous response.", "type": "string"}}, "parameterOrder": ["accountId"], "description": "List all saved reports in the specified AdSense account.", "id": "adsense.accounts.reports.saved.list", "path": "accounts/{accountId}/reports/saved", "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"], "response": {"$ref": "SavedReports"}}}}}, "methods": {"generate": {"path": "accounts/{accountId}/reports", "id": "adsense.accounts.reports.generate", "response": {"$ref": "AdsenseReportsGenerateResponse"}, "description": "Generate an AdSense report based on the report request sent in the query parameters. Returns the result as JSON; to retrieve output in CSV format specify \"alt=csv\" as a query parameter.", "supportsMediaDownload": true, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"], "parameters": {"dimension": {"type": "string", "pattern": "[a-zA-Z_]+", "repeated": true, "description": "Dimensions to base the report on.", "location": "query"}, "accountId": {"type": "string", "location": "path", "description": "Account upon which to report.", "required": true}, "filter": {"repeated": true, "pattern": "[a-zA-Z_]+(==|=@).+", "description": "Filters to be run on the report.", "type": "string", "location": "query"}, "currency": {"pattern": "[a-zA-Z]+", "type": "string", "location": "query", "description": "Optional currency to use when reporting on monetary metrics. Defaults to the account's currency if not set."}, "locale": {"description": "Optional locale to use for translating report output to a local language. Defaults to \"en_US\" if not specified.", "location": "query", "pattern": "[a-zA-Z_]+", "type": "string"}, "maxResults": {"format": "int32", "type": "integer", "description": "The maximum number of rows of report data to return.", "location": "query", "maximum": "50000", "minimum": "0"}, "useTimezoneReporting": {"description": "Whether the report should be generated in the AdSense account's local timezone. If false default PST/PDT timezone will be used.", "type": "boolean", "location": "query"}, "startDate": {"type": "string", "required": true, "description": "Start of the date range to report on in \"YYYY-MM-DD\" format, inclusive.", "location": "query", "pattern": "\\d{4}-\\d{2}-\\d{2}|(today|startOfMonth|startOfYear)(([\\-\\+]\\d+[dwmy]){0,3}?)|(latest-(\\d{2})-(\\d{2})(-\\d+y)?)|(latest-latest-(\\d{2})(-\\d+m)?)"}, "sort": {"location": "query", "repeated": true, "description": "The name of a dimension or metric to sort the resulting report on, optionally prefixed with \"+\" to sort ascending or \"-\" to sort descending. If no prefix is specified, the column is sorted ascending.", "pattern": "(\\+|-)?[a-zA-Z_]+", "type": "string"}, "metric": {"repeated": true, "location": "query", "type": "string", "pattern": "[a-zA-Z_]+", "description": "Numeric columns to include in the report."}, "endDate": {"pattern": "\\d{4}-\\d{2}-\\d{2}|(today|startOfMonth|startOfYear)(([\\-\\+]\\d+[dwmy]){0,3}?)|(latest-(\\d{2})-(\\d{2})(-\\d+y)?)|(latest-latest-(\\d{2})(-\\d+m)?)", "description": "End of the date range to report on in \"YYYY-MM-DD\" format, inclusive.", "type": "string", "location": "query", "required": true}, "startIndex": {"maximum": "5000", "minimum": "0", "location": "query", "format": "int32", "type": "integer", "description": "Index of the first row of report data to return."}}, "parameterOrder": ["accountId", "startDate", "endDate"], "httpMethod": "GET"}}}, "payments": {"methods": {"list": {"description": "List the payments for the specified AdSense account.", "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"], "parameters": {"accountId": {"required": true, "description": "Account for which to retrieve the payments.", "location": "path", "type": "string"}}, "httpMethod": "GET", "response": {"$ref": "Payments"}, "parameterOrder": ["accountId"], "path": "accounts/{accountId}/payments", "id": "adsense.accounts.payments.list"}}}, "adclients": {"methods": {"list": {"parameterOrder": ["accountId"], "httpMethod": "GET", "id": "adsense.accounts.adclients.list", "path": "accounts/{accountId}/adclients", "response": {"$ref": "AdClients"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"], "parameters": {"accountId": {"location": "path", "type": "string", "required": true, "description": "Account for which to list ad clients."}, "pageToken": {"description": "A continuation token, used to page through ad clients. To retrieve the next page, set this parameter to the value of \"nextPageToken\" from the previous response.", "location": "query", "type": "string"}, "maxResults": {"description": "The maximum number of ad clients to include in the response, used for paging.", "type": "integer", "maximum": "10000", "format": "int32", "minimum": "0", "location": "query"}}, "description": "List all ad clients in the specified account."}, "getAdCode": {"description": "Get Auto ad code for a given ad client.", "response": {"$ref": "AdCode"}, "id": "adsense.accounts.adclients.getAdCode", "path": "accounts/{accountId}/adclients/{adClientId}/adcode", "parameterOrder": ["accountId", "adClientId"], "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"], "parameters": {"tagPartner": {"location": "query", "description": "Tag partner to include in the ad code snippet.", "type": "string"}, "accountId": {"description": "Account which contains the ad client.", "type": "string", "required": true, "location": "path"}, "adClientId": {"required": true, "type": "string", "location": "path", "description": "Ad client to get the code for."}}, "httpMethod": "GET"}}}, "urlchannels": {"methods": {"list": {"httpMethod": "GET", "parameters": {"pageToken": {"location": "query", "type": "string", "description": "A continuation token, used to page through URL channels. To retrieve the next page, set this parameter to the value of \"nextPageToken\" from the previous response."}, "accountId": {"location": "path", "type": "string", "description": "Account to which the ad client belongs.", "required": true}, "adClientId": {"type": "string", "location": "path", "required": true, "description": "Ad client for which to list URL channels."}, "maxResults": {"minimum": "0", "location": "query", "format": "int32", "type": "integer", "description": "The maximum number of URL channels to include in the response, used for paging.", "maximum": "10000"}}, "description": "List all URL channels in the specified ad client for the specified account.", "response": {"$ref": "UrlChannels"}, "id": "adsense.accounts.urlchannels.list", "path": "accounts/{accountId}/adclients/{adClientId}/urlchannels", "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"], "parameterOrder": ["accountId", "adClientId"]}}}}}, "urlchannels": {"methods": {"list": {"scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"], "id": "adsense.urlchannels.list", "response": {"$ref": "UrlChannels"}, "description": "List all URL channels in the specified ad client for this AdSense account.", "parameterOrder": ["adClientId"], "path": "adclients/{adClientId}/urlchannels", "parameters": {"maxResults": {"description": "The maximum number of URL channels to include in the response, used for paging.", "format": "int32", "maximum": "10000", "type": "integer", "minimum": "0", "location": "query"}, "pageToken": {"type": "string", "description": "A continuation token, used to page through URL channels. To retrieve the next page, set this parameter to the value of \"nextPageToken\" from the previous response.", "location": "query"}, "adClientId": {"location": "path", "description": "Ad client for which to list URL channels.", "type": "string", "required": true}}, "httpMethod": "GET"}}}, "metadata": {"resources": {"metrics": {"methods": {"list": {"scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"], "id": "adsense.metadata.metrics.list", "httpMethod": "GET", "path": "metadata/metrics", "description": "List the metadata for the metrics available to this AdSense account.", "response": {"$ref": "<PERSON><PERSON><PERSON>"}}}}, "dimensions": {"methods": {"list": {"response": {"$ref": "<PERSON><PERSON><PERSON>"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"], "id": "adsense.metadata.dimensions.list", "path": "metadata/dimensions", "description": "List the metadata for the dimensions available to this AdSense account.", "httpMethod": "GET"}}}}}, "adunits": {"methods": {"get": {"response": {"$ref": "AdUnit"}, "parameters": {"adClientId": {"location": "path", "type": "string", "required": true, "description": "Ad client for which to get the ad unit."}, "adUnitId": {"description": "Ad unit to retrieve.", "type": "string", "required": true, "location": "path"}}, "httpMethod": "GET", "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"], "description": "Gets the specified ad unit in the specified ad client.", "id": "adsense.adunits.get", "parameterOrder": ["adClientId", "adUnitId"], "path": "adclients/{adClientId}/adunits/{adUnitId}"}, "getAdCode": {"path": "adclients/{adClientId}/adunits/{adUnitId}/adcode", "description": "Get ad code for the specified ad unit.", "parameters": {"adUnitId": {"required": true, "description": "Ad unit to get the code for.", "location": "path", "type": "string"}, "adClientId": {"location": "path", "required": true, "type": "string", "description": "Ad client with contains the ad unit."}}, "parameterOrder": ["adClientId", "adUnitId"], "httpMethod": "GET", "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"], "id": "adsense.adunits.getAdCode", "response": {"$ref": "AdCode"}}, "list": {"id": "adsense.adunits.list", "parameterOrder": ["adClientId"], "description": "List all ad units in the specified ad client for this AdSense account.", "parameters": {"pageToken": {"description": "A continuation token, used to page through ad units. To retrieve the next page, set this parameter to the value of \"nextPageToken\" from the previous response.", "location": "query", "type": "string"}, "includeInactive": {"location": "query", "type": "boolean", "description": "Whether to include inactive ad units. Default: true."}, "adClientId": {"location": "path", "type": "string", "required": true, "description": "Ad client for which to list ad units."}, "maxResults": {"format": "int32", "description": "The maximum number of ad units to include in the response, used for paging.", "location": "query", "maximum": "10000", "type": "integer", "minimum": "0"}}, "httpMethod": "GET", "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"], "path": "adclients/{adClientId}/adunits", "response": {"$ref": "AdUnits"}}}, "resources": {"customchannels": {"methods": {"list": {"id": "adsense.adunits.customchannels.list", "httpMethod": "GET", "description": "List all custom channels which the specified ad unit belongs to.", "parameterOrder": ["adClientId", "adUnitId"], "response": {"$ref": "CustomChannels"}, "path": "adclients/{adClientId}/adunits/{adUnitId}/customchannels", "parameters": {"adUnitId": {"type": "string", "location": "path", "description": "Ad unit for which to list custom channels.", "required": true}, "pageToken": {"description": "A continuation token, used to page through custom channels. To retrieve the next page, set this parameter to the value of \"nextPageToken\" from the previous response.", "type": "string", "location": "query"}, "adClientId": {"location": "path", "required": true, "type": "string", "description": "Ad client which contains the ad unit."}, "maxResults": {"description": "The maximum number of custom channels to include in the response, used for paging.", "location": "query", "maximum": "10000", "minimum": "0", "type": "integer", "format": "int32"}}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"]}}}}}}, "servicePath": "adsense/v1.4/", "discoveryVersion": "v1", "baseUrl": "https://www.googleapis.com/adsense/v1.4/", "auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/adsense": {"description": "View and manage your AdSense data"}, "https://www.googleapis.com/auth/adsense.readonly": {"description": "View your AdSense data"}}}}, "parameters": {"fields": {"description": "Selector specifying which fields to include in a partial response.", "type": "string", "location": "query"}, "alt": {"description": "Data format for the response.", "default": "json", "location": "query", "enum": ["csv", "json"], "type": "string", "enumDescriptions": ["Responses with Content-Type of text/csv", "Responses with Content-Type of application/json"]}, "prettyPrint": {"location": "query", "description": "Returns response with indentations and line breaks.", "default": "true", "type": "boolean"}, "userIp": {"description": "Deprecated. Please use quotaUser instead.", "type": "string", "location": "query"}, "key": {"type": "string", "description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query"}, "oauth_token": {"type": "string", "location": "query", "description": "OAuth 2.0 token for the current user."}, "quotaUser": {"description": "An opaque string that represents a user for quota purposes. Must not exceed 40 characters.", "type": "string", "location": "query"}}, "rootUrl": "https://www.googleapis.com/", "description": "Accesses AdSense publishers' inventory and generates performance reports.", "icons": {"x32": "https://www.google.com/images/icons/product/adsense-32.png", "x16": "https://www.google.com/images/icons/product/adsense-16.png"}, "name": "adsense", "schemas": {"CustomChannel": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier of this custom channel. This should be considered an opaque identifier; it is not safe to rely on it being in any particular format."}, "code": {"type": "string", "description": "Code of this custom channel, not necessarily unique across ad clients."}, "kind": {"description": "Kind of resource this is, in this case adsense#customChannel.", "default": "adsense#customChannel", "type": "string"}, "targetingInfo": {"type": "object", "description": "The targeting information of this custom channel, if activated.", "properties": {"adsAppearOn": {"type": "string", "description": "The name used to describe this channel externally."}, "location": {"description": "The locations in which ads appear. (Only valid for content and mobile content ads (deprecated)). Acceptable values for content ads are: TOP_LEFT, TOP_CENTER, TOP_RIGHT, MIDDLE_LEFT, MIDDLE_CENTER, MID<PERSON><PERSON>_RIGHT, BOTTOM_LEFT, BOT<PERSON>M_CENTER, BOT<PERSON>M_RIGHT, M<PERSON><PERSON><PERSON>LE_LOCATIONS. Acceptable values for mobile content ads (deprecated) are: TOP, MIDDLE, BOTTOM, MULTIPLE_LOCATIONS.", "type": "string"}, "description": {"type": "string", "description": "The external description of the channel."}, "siteLanguage": {"description": "The language of the sites ads will be displayed on.", "type": "string"}}}, "name": {"type": "string", "description": "Name of this custom channel."}}, "id": "CustomChannel"}, "AdsenseReportsGenerateResponse": {"properties": {"rows": {"description": "The output rows of the report. Each row is a list of cells; one for each dimension in the request, followed by one for each metric in the request. The dimension cells contain strings, and the metric cells contain numbers.", "type": "array", "items": {"items": {"type": "string"}, "type": "array"}}, "endDate": {"type": "string", "description": "The requested end date in yyyy-mm-dd format."}, "startDate": {"type": "string", "description": "The requested start date in yyyy-mm-dd format."}, "kind": {"default": "adsense#report", "type": "string", "description": "Kind this is, in this case adsense#report."}, "averages": {"type": "array", "items": {"type": "string"}, "description": "The averages of the report. This is the same length as any other row in the report; cells corresponding to dimension columns are empty."}, "totals": {"items": {"type": "string"}, "type": "array", "description": "The totals of the report. This is the same length as any other row in the report; cells corresponding to dimension columns are empty."}, "totalMatchedRows": {"type": "string", "description": "The total number of rows matched by the report request. Fewer rows may be returned in the response due to being limited by the row count requested or the report row limit.", "format": "int64"}, "headers": {"items": {"properties": {"name": {"description": "The name of the header.", "type": "string"}, "type": {"description": "The type of the header; one of DIM<PERSON><PERSON><PERSON>, METRIC_TALLY, <PERSON>TRIC_RATIO, or METRIC_CURRENCY.", "type": "string"}, "currency": {"type": "string", "description": "The currency of this column. Only present if the header type is METRIC_CURRENCY."}}, "type": "object"}, "description": "The header information of the columns requested in the report. This is a list of headers; one for each dimension in the request, followed by one for each metric in the request.", "type": "array"}, "warnings": {"description": "Any warnings associated with generation of the report.", "type": "array", "items": {"type": "string"}}}, "id": "AdsenseReportsGenerateResponse", "type": "object"}, "Payment": {"properties": {"paymentAmountCurrencyCode": {"type": "string", "description": "The currency code for the amount to be paid."}, "paymentAmount": {"type": "string", "description": "The amount to be paid."}, "paymentDate": {"type": "string", "description": "The date this payment was/will be credited to the user, or none if the payment threshold has not been met."}, "id": {"description": "Unique identifier of this Payment.", "type": "string"}, "kind": {"description": "Kind of resource this is, in this case adsense#payment.", "type": "string", "default": "adsense#payment"}}, "type": "object", "id": "Payment"}, "AdCode": {"id": "AdCode", "type": "object", "properties": {"ampHead": {"type": "string", "description": "The AMP Auto ad code snippet that goes in the head of an AMP page."}, "kind": {"type": "string", "default": "adsense#adCode", "description": "Kind this is, in this case adsense#adCode."}, "adCode": {"type": "string", "description": "The Auto ad code snippet. The ad code snippet."}, "ampBody": {"type": "string", "description": "The AMP Auto ad code snippet that goes in the body of an AMP page."}}}, "Accounts": {"id": "Accounts", "type": "object", "properties": {"etag": {"description": "ETag of this response for caching purposes.", "type": "string"}, "kind": {"description": "Kind of list this is, in this case adsense#accounts.", "type": "string", "default": "adsense#accounts"}, "items": {"type": "array", "items": {"$ref": "Account"}, "description": "The accounts returned in this list response."}, "nextPageToken": {"type": "string", "description": "Continuation token used to page through accounts. To retrieve the next page of results, set the next request's \"pageToken\" value to this."}}}, "SavedReport": {"id": "SavedReport", "type": "object", "properties": {"kind": {"type": "string", "description": "Kind of resource this is, in this case adsense#savedReport.", "default": "adsense#savedReport"}, "id": {"type": "string", "description": "Unique identifier of this saved report."}, "name": {"type": "string", "description": "This saved report's name."}}}, "Metadata": {"type": "object", "properties": {"kind": {"type": "string", "default": "adsense#metadata", "description": "Kind of list this is, in this case adsense#metadata."}, "items": {"items": {"$ref": "ReportingMetadataEntry"}, "type": "array"}}, "id": "<PERSON><PERSON><PERSON>"}, "Payments": {"type": "object", "properties": {"kind": {"type": "string", "description": "Kind of list this is, in this case adsense#payments.", "default": "adsense#payments"}, "items": {"description": "The list of Payments for the account. One or both of a) the account's most recent payment; and b) the account's upcoming payment.", "type": "array", "items": {"$ref": "Payment"}}}, "id": "Payments"}, "AdClients": {"properties": {"kind": {"description": "Kind of list this is, in this case adsense#adClients.", "default": "adsense#adClients", "type": "string"}, "items": {"description": "The ad clients returned in this list response.", "type": "array", "items": {"$ref": "AdClient"}}, "nextPageToken": {"type": "string", "description": "Continuation token used to page through ad clients. To retrieve the next page of results, set the next request's \"pageToken\" value to this."}, "etag": {"type": "string", "description": "ETag of this response for caching purposes."}}, "type": "object", "id": "AdClients"}, "UrlChannels": {"id": "UrlChannels", "type": "object", "properties": {"kind": {"type": "string", "default": "adsense#urlChannels", "description": "Kind of list this is, in this case adsense#urlChannels."}, "items": {"type": "array", "description": "The URL channels returned in this list response.", "items": {"$ref": "UrlChannel"}}, "nextPageToken": {"type": "string", "description": "Continuation token used to page through URL channels. To retrieve the next page of results, set the next request's \"pageToken\" value to this."}, "etag": {"type": "string", "description": "ETag of this response for caching purposes."}}}, "Alerts": {"id": "<PERSON><PERSON><PERSON>", "type": "object", "properties": {"kind": {"description": "Kind of list this is, in this case adsense#alerts.", "type": "string", "default": "adsense#alerts"}, "items": {"type": "array", "items": {"$ref": "<PERSON><PERSON>"}, "description": "The alerts returned in this list response."}}}, "SavedAdStyles": {"properties": {"items": {"items": {"$ref": "SavedAdStyle"}, "description": "The saved ad styles returned in this list response.", "type": "array"}, "nextPageToken": {"description": "Continuation token used to page through ad units. To retrieve the next page of results, set the next request's \"pageToken\" value to this.", "type": "string"}, "etag": {"description": "ETag of this response for caching purposes.", "type": "string"}, "kind": {"description": "Kind of list this is, in this case adsense#savedAdStyles.", "default": "adsense#savedAdStyles", "type": "string"}}, "id": "SavedAdStyles", "type": "object"}, "AdUnit": {"id": "AdUnit", "type": "object", "properties": {"customStyle": {"$ref": "AdStyle", "description": "Custom style information specific to this ad unit."}, "mobileContentAdsSettings": {"properties": {"type": {"description": "Type of this ad unit.", "type": "string"}, "scriptingLanguage": {"type": "string", "description": "The scripting language to use for this ad unit."}, "size": {"description": "Size of this ad unit.", "type": "string"}, "markupLanguage": {"type": "string", "description": "The markup language to use for this ad unit."}}, "description": "Settings specific to WAP mobile content ads (AFMC) - deprecated.", "type": "object"}, "savedStyleId": {"type": "string", "description": "ID of the saved ad style which holds this ad unit's style information."}, "contentAdsSettings": {"properties": {"backupOption": {"description": "The backup option to be used in instances where no ad is available.", "properties": {"url": {"description": "URL to use when type is set to URL.", "type": "string"}, "color": {"description": "Color to use when type is set to COLOR.", "type": "string"}, "type": {"type": "string", "description": "Type of the backup option. Possible values are BLANK, COLOR and URL."}}, "type": "object"}, "type": {"type": "string", "description": "Type of this ad unit."}, "size": {"description": "Size of this ad unit.", "type": "string"}}, "description": "Settings specific to content ads (AFC) and highend mobile content ads (AFMC - deprecated).", "type": "object"}, "name": {"type": "string", "description": "Name of this ad unit."}, "kind": {"default": "adsense#adUnit", "type": "string", "description": "Kind of resource this is, in this case adsense#adUnit."}, "code": {"type": "string", "description": "Identity code of this ad unit, not necessarily unique across ad clients."}, "status": {"type": "string", "description": "Status of this ad unit. Possible values are:\nNEW: Indicates that the ad unit was created within the last seven days and does not yet have any activity associated with it.\n\nACTIVE: Indicates that there has been activity on this ad unit in the last seven days.\n\nINACTIVE: Indicates that there has been no activity on this ad unit in the last seven days."}, "feedAdsSettings": {"description": "Settings specific to feed ads (AFF) - deprecated.", "type": "object", "properties": {"minimumWordCount": {"description": "The minimum length an entry should be in order to have attached ads.", "type": "integer", "format": "int32"}, "frequency": {"format": "int32", "type": "integer", "description": "The frequency at which ads should appear in the feed (i.e. every N entries)."}, "adPosition": {"description": "The position of the ads relative to the feed entries.", "type": "string"}, "type": {"description": "The type of ads which should appear.", "type": "string"}}}, "id": {"type": "string", "description": "Unique identifier of this ad unit. This should be considered an opaque identifier; it is not safe to rely on it being in any particular format."}}}, "UrlChannel": {"type": "object", "properties": {"kind": {"default": "adsense#urlChannel", "description": "Kind of resource this is, in this case adsense#urlChannel.", "type": "string"}, "id": {"type": "string", "description": "Unique identifier of this URL channel. This should be considered an opaque identifier; it is not safe to rely on it being in any particular format."}, "urlPattern": {"description": "URL Pattern of this URL channel. Does not include \"http://\" or \"https://\". Example: www.example.com/home", "type": "string"}}, "id": "UrlChannel"}, "Alert": {"type": "object", "properties": {"severity": {"description": "Severity of this alert. Possible values: INFO, WARNING, SEVERE.", "type": "string"}, "message": {"description": "The localized alert message.", "type": "string"}, "isDismissible": {"description": "Whether this alert can be dismissed.", "type": "boolean"}, "type": {"description": "Type of this alert. Possible values: SELF_HOLD, MIGRATED_TO_BILLING3, ADDRESS_PIN_VERIFICATION, PHONE_PIN_VERIFICATION, CORPORATE_ENTITY, GRAYLISTED_PUBLISHER, API_HOLD.", "type": "string"}, "kind": {"default": "adsense#alert", "description": "Kind of resource this is, in this case adsense#alert.", "type": "string"}, "id": {"description": "Unique identifier of this alert. This should be considered an opaque identifier; it is not safe to rely on it being in any particular format.", "type": "string"}}, "id": "<PERSON><PERSON>"}, "AdUnits": {"properties": {"kind": {"type": "string", "default": "adsense#adUnits", "description": "Kind of list this is, in this case adsense#adUnits."}, "etag": {"description": "ETag of this response for caching purposes.", "type": "string"}, "items": {"description": "The ad units returned in this list response.", "items": {"$ref": "AdUnit"}, "type": "array"}, "nextPageToken": {"type": "string", "description": "Continuation token used to page through ad units. To retrieve the next page of results, set the next request's \"pageToken\" value to this."}}, "type": "object", "id": "AdUnits"}, "CustomChannels": {"type": "object", "properties": {"nextPageToken": {"description": "Continuation token used to page through custom channels. To retrieve the next page of results, set the next request's \"pageToken\" value to this.", "type": "string"}, "kind": {"description": "Kind of list this is, in this case adsense#customChannels.", "default": "adsense#customChannels", "type": "string"}, "etag": {"type": "string", "description": "ETag of this response for caching purposes."}, "items": {"description": "The custom channels returned in this list response.", "type": "array", "items": {"$ref": "CustomChannel"}}}, "id": "CustomChannels"}, "SavedReports": {"id": "SavedReports", "properties": {"kind": {"type": "string", "description": "Kind of list this is, in this case adsense#savedReports.", "default": "adsense#savedReports"}, "items": {"type": "array", "description": "The saved reports returned in this list response.", "items": {"$ref": "SavedReport"}}, "etag": {"description": "ETag of this response for caching purposes.", "type": "string"}, "nextPageToken": {"type": "string", "description": "Continuation token used to page through saved reports. To retrieve the next page of results, set the next request's \"pageToken\" value to this."}}, "type": "object"}, "Account": {"properties": {"id": {"description": "Unique identifier of this account.", "type": "string"}, "premium": {"description": "Whether this account is premium.", "type": "boolean"}, "kind": {"type": "string", "description": "Kind of resource this is, in this case adsense#account.", "default": "adsense#account"}, "name": {"type": "string", "description": "Name of this account."}, "subAccounts": {"items": {"$ref": "Account"}, "description": "Sub accounts of the this account.", "type": "array"}, "creation_time": {"type": "string", "format": "int64"}, "timezone": {"type": "string", "description": "AdSense timezone of this account."}}, "id": "Account", "type": "object"}, "AdStyle": {"id": "AdStyle", "properties": {"kind": {"type": "string", "description": "Kind this is, in this case adsense#adStyle.", "default": "adsense#adStyle"}, "font": {"type": "object", "description": "The font which is included in the style.", "properties": {"family": {"description": "The family of the font.", "type": "string"}, "size": {"description": "The size of the font.", "type": "string"}}}, "colors": {"properties": {"url": {"description": "The color of the ad url.", "type": "string"}, "background": {"description": "The color of the ad background.", "type": "string"}, "text": {"type": "string", "description": "The color of the ad text."}, "title": {"type": "string", "description": "The color of the ad title."}, "border": {"type": "string", "description": "The color of the ad border."}}, "type": "object", "description": "The colors which are included in the style. These are represented as six hexadecimal characters, similar to HTML color codes, but without the leading hash."}, "corners": {"description": "The style of the corners in the ad (deprecated: never populated, ignored).", "type": "string"}}, "type": "object"}, "SavedAdStyle": {"type": "object", "id": "SavedAdStyle", "properties": {"id": {"type": "string", "description": "Unique identifier of this saved ad style. This should be considered an opaque identifier; it is not safe to rely on it being in any particular format."}, "name": {"description": "The user selected name of this SavedAdStyle.", "type": "string"}, "adStyle": {"$ref": "AdStyle", "description": "The AdStyle itself."}, "kind": {"default": "adsense#savedAdStyle", "description": "Kind of resource this is, in this case adsense#savedAdStyle.", "type": "string"}}}, "AdClient": {"id": "AdClient", "type": "object", "properties": {"arcOptIn": {"description": "Whether this ad client is opted in to ARC.", "type": "boolean"}, "id": {"description": "Unique identifier of this ad client.", "type": "string"}, "kind": {"description": "Kind of resource this is, in this case adsense#adClient.", "type": "string", "default": "adsense#adClient"}, "supportsReporting": {"description": "Whether this ad client supports being reported on.", "type": "boolean"}, "productCode": {"description": "This ad client's product code, which corresponds to the PRODUCT_CODE report dimension.", "type": "string"}}}, "ReportingMetadataEntry": {"properties": {"kind": {"default": "adsense#reportingMetadataEntry", "description": "Kind of resource this is, in this case adsense#reportingMetadataEntry.", "type": "string"}, "id": {"type": "string", "description": "Unique identifier of this reporting metadata entry, corresponding to the name of the appropriate dimension or metric."}, "compatibleDimensions": {"items": {"type": "string"}, "description": "For metrics this is a list of dimension IDs which the metric is compatible with, for dimensions it is a list of compatibility groups the dimension belongs to.", "type": "array"}, "compatibleMetrics": {"description": "The names of the metrics the dimension or metric this reporting metadata entry describes is compatible with.", "type": "array", "items": {"type": "string"}}, "requiredDimensions": {"items": {"type": "string"}, "description": "The names of the dimensions which the dimension or metric this reporting metadata entry describes requires to also be present in order for the report to be valid. Omitting these will not cause an error or warning, but may result in data which cannot be correctly interpreted.", "type": "array"}, "supportedProducts": {"description": "The codes of the projects supported by the dimension or metric this reporting metadata entry describes.", "items": {"type": "string"}, "type": "array"}, "requiredMetrics": {"type": "array", "items": {"type": "string"}, "description": "The names of the metrics which the dimension or metric this reporting metadata entry describes requires to also be present in order for the report to be valid. Omitting these will not cause an error or warning, but may result in data which cannot be correctly interpreted."}}, "type": "object", "id": "ReportingMetadataEntry"}}, "basePath": "/adsense/v1.4/", "version": "v1.4", "ownerName": "Google", "documentationLink": "https://developers.google.com/adsense/management/", "batchPath": "batch/adsense/v1.4", "ownerDomain": "google.com", "title": "AdSense Management API"}