{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/adsense": {"description": "View and manage your AdSense data"}, "https://www.googleapis.com/auth/adsense.readonly": {"description": "View your AdSense data"}}}}, "basePath": "/adsense/v1.4/", "baseUrl": "https://www.googleapis.com/adsense/v1.4/", "batchPath": "batch/adsense/v1.4", "canonicalName": "AdSense", "description": "Accesses AdSense publishers' inventory and generates performance reports.", "discoveryVersion": "v1", "documentationLink": "https://developers.google.com/adsense/management/", "icons": {"x16": "https://www.google.com/images/icons/product/adsense-16.png", "x32": "https://www.google.com/images/icons/product/adsense-32.png"}, "id": "adsense:v1.4", "kind": "discovery#restDescription", "name": "adsense", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"alt": {"default": "json", "description": "Data format for the response.", "enum": ["csv", "json"], "enumDescriptions": ["Responses with Content-Type of text/csv", "Responses with Content-Type of application/json"], "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "An opaque string that represents a user for quota purposes. Must not exceed 40 characters.", "location": "query", "type": "string"}, "userIp": {"description": "Deprecated. Please use quotaUser instead.", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"accounts": {"methods": {"get": {"description": "Get information about the selected AdSense account.", "httpMethod": "GET", "id": "adsense.accounts.get", "parameterOrder": ["accountId"], "parameters": {"accountId": {"description": "Account to get information about.", "location": "path", "required": true, "type": "string"}, "tree": {"description": "Whether the tree of sub accounts should be returned.", "location": "query", "type": "boolean"}}, "path": "accounts/{accountId}", "response": {"$ref": "Account"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"]}, "list": {"description": "List all accounts available to this AdSense account.", "httpMethod": "GET", "id": "adsense.accounts.list", "parameters": {"maxResults": {"description": "The maximum number of accounts to include in the response, used for paging.", "format": "int32", "location": "query", "maximum": "10000", "minimum": "0", "type": "integer"}, "pageToken": {"description": "A continuation token, used to page through accounts. To retrieve the next page, set this parameter to the value of \"nextPageToken\" from the previous response.", "location": "query", "type": "string"}}, "path": "accounts", "response": {"$ref": "Accounts"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"]}}, "resources": {"adclients": {"methods": {"getAdCode": {"description": "Get Auto ad code for a given ad client.", "httpMethod": "GET", "id": "adsense.accounts.adclients.getAdCode", "parameterOrder": ["accountId", "adClientId"], "parameters": {"accountId": {"description": "Account which contains the ad client.", "location": "path", "required": true, "type": "string"}, "adClientId": {"description": "Ad client to get the code for.", "location": "path", "required": true, "type": "string"}, "tagPartner": {"description": "Tag partner to include in the ad code snippet.", "location": "query", "type": "string"}}, "path": "accounts/{accountId}/adclients/{adClientId}/adcode", "response": {"$ref": "AdCode"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"]}, "list": {"description": "List all ad clients in the specified account.", "httpMethod": "GET", "id": "adsense.accounts.adclients.list", "parameterOrder": ["accountId"], "parameters": {"accountId": {"description": "Account for which to list ad clients.", "location": "path", "required": true, "type": "string"}, "maxResults": {"description": "The maximum number of ad clients to include in the response, used for paging.", "format": "int32", "location": "query", "maximum": "10000", "minimum": "0", "type": "integer"}, "pageToken": {"description": "A continuation token, used to page through ad clients. To retrieve the next page, set this parameter to the value of \"nextPageToken\" from the previous response.", "location": "query", "type": "string"}}, "path": "accounts/{accountId}/adclients", "response": {"$ref": "AdClients"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"]}}}, "adunits": {"methods": {"get": {"description": "Gets the specified ad unit in the specified ad client for the specified account.", "httpMethod": "GET", "id": "adsense.accounts.adunits.get", "parameterOrder": ["accountId", "adClientId", "adUnitId"], "parameters": {"accountId": {"description": "Account to which the ad client belongs.", "location": "path", "required": true, "type": "string"}, "adClientId": {"description": "Ad client for which to get the ad unit.", "location": "path", "required": true, "type": "string"}, "adUnitId": {"description": "Ad unit to retrieve.", "location": "path", "required": true, "type": "string"}}, "path": "accounts/{accountId}/adclients/{adClientId}/adunits/{adUnitId}", "response": {"$ref": "AdUnit"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"]}, "getAdCode": {"description": "Get ad code for the specified ad unit.", "httpMethod": "GET", "id": "adsense.accounts.adunits.getAdCode", "parameterOrder": ["accountId", "adClientId", "adUnitId"], "parameters": {"accountId": {"description": "Account which contains the ad client.", "location": "path", "required": true, "type": "string"}, "adClientId": {"description": "Ad client with contains the ad unit.", "location": "path", "required": true, "type": "string"}, "adUnitId": {"description": "Ad unit to get the code for.", "location": "path", "required": true, "type": "string"}}, "path": "accounts/{accountId}/adclients/{adClientId}/adunits/{adUnitId}/adcode", "response": {"$ref": "AdCode"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"]}, "list": {"description": "List all ad units in the specified ad client for the specified account.", "httpMethod": "GET", "id": "adsense.accounts.adunits.list", "parameterOrder": ["accountId", "adClientId"], "parameters": {"accountId": {"description": "Account to which the ad client belongs.", "location": "path", "required": true, "type": "string"}, "adClientId": {"description": "Ad client for which to list ad units.", "location": "path", "required": true, "type": "string"}, "includeInactive": {"description": "Whether to include inactive ad units. Default: true.", "location": "query", "type": "boolean"}, "maxResults": {"description": "The maximum number of ad units to include in the response, used for paging.", "format": "int32", "location": "query", "maximum": "10000", "minimum": "0", "type": "integer"}, "pageToken": {"description": "A continuation token, used to page through ad units. To retrieve the next page, set this parameter to the value of \"nextPageToken\" from the previous response.", "location": "query", "type": "string"}}, "path": "accounts/{accountId}/adclients/{adClientId}/adunits", "response": {"$ref": "AdUnits"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"]}}, "resources": {"customchannels": {"methods": {"list": {"description": "List all custom channels which the specified ad unit belongs to.", "httpMethod": "GET", "id": "adsense.accounts.adunits.customchannels.list", "parameterOrder": ["accountId", "adClientId", "adUnitId"], "parameters": {"accountId": {"description": "Account to which the ad client belongs.", "location": "path", "required": true, "type": "string"}, "adClientId": {"description": "Ad client which contains the ad unit.", "location": "path", "required": true, "type": "string"}, "adUnitId": {"description": "Ad unit for which to list custom channels.", "location": "path", "required": true, "type": "string"}, "maxResults": {"description": "The maximum number of custom channels to include in the response, used for paging.", "format": "int32", "location": "query", "maximum": "10000", "minimum": "0", "type": "integer"}, "pageToken": {"description": "A continuation token, used to page through custom channels. To retrieve the next page, set this parameter to the value of \"nextPageToken\" from the previous response.", "location": "query", "type": "string"}}, "path": "accounts/{accountId}/adclients/{adClientId}/adunits/{adUnitId}/customchannels", "response": {"$ref": "CustomChannels"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"]}}}}}, "alerts": {"methods": {"delete": {"description": "Dismiss (delete) the specified alert from the specified publisher AdSense account.", "httpMethod": "DELETE", "id": "adsense.accounts.alerts.delete", "parameterOrder": ["accountId", "alertId"], "parameters": {"accountId": {"description": "Account which contains the ad unit.", "location": "path", "required": true, "type": "string"}, "alertId": {"description": "<PERSON><PERSON> to delete.", "location": "path", "required": true, "type": "string"}}, "path": "accounts/{accountId}/alerts/{alertId}", "scopes": ["https://www.googleapis.com/auth/adsense"]}, "list": {"description": "List the alerts for the specified AdSense account.", "httpMethod": "GET", "id": "adsense.accounts.alerts.list", "parameterOrder": ["accountId"], "parameters": {"accountId": {"description": "Account for which to retrieve the alerts.", "location": "path", "required": true, "type": "string"}, "locale": {"description": "The locale to use for translating alert messages. The account locale will be used if this is not supplied. The AdSense default (English) will be used if the supplied locale is invalid or unsupported.", "location": "query", "type": "string"}}, "path": "accounts/{accountId}/alerts", "response": {"$ref": "<PERSON><PERSON><PERSON>"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"]}}}, "customchannels": {"methods": {"get": {"description": "Get the specified custom channel from the specified ad client for the specified account.", "httpMethod": "GET", "id": "adsense.accounts.customchannels.get", "parameterOrder": ["accountId", "adClientId", "customChannelId"], "parameters": {"accountId": {"description": "Account to which the ad client belongs.", "location": "path", "required": true, "type": "string"}, "adClientId": {"description": "Ad client which contains the custom channel.", "location": "path", "required": true, "type": "string"}, "customChannelId": {"description": "Custom channel to retrieve.", "location": "path", "required": true, "type": "string"}}, "path": "accounts/{accountId}/adclients/{adClientId}/customchannels/{customChannelId}", "response": {"$ref": "CustomChannel"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"]}, "list": {"description": "List all custom channels in the specified ad client for the specified account.", "httpMethod": "GET", "id": "adsense.accounts.customchannels.list", "parameterOrder": ["accountId", "adClientId"], "parameters": {"accountId": {"description": "Account to which the ad client belongs.", "location": "path", "required": true, "type": "string"}, "adClientId": {"description": "Ad client for which to list custom channels.", "location": "path", "required": true, "type": "string"}, "maxResults": {"description": "The maximum number of custom channels to include in the response, used for paging.", "format": "int32", "location": "query", "maximum": "10000", "minimum": "0", "type": "integer"}, "pageToken": {"description": "A continuation token, used to page through custom channels. To retrieve the next page, set this parameter to the value of \"nextPageToken\" from the previous response.", "location": "query", "type": "string"}}, "path": "accounts/{accountId}/adclients/{adClientId}/customchannels", "response": {"$ref": "CustomChannels"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"]}}, "resources": {"adunits": {"methods": {"list": {"description": "List all ad units in the specified custom channel.", "httpMethod": "GET", "id": "adsense.accounts.customchannels.adunits.list", "parameterOrder": ["accountId", "adClientId", "customChannelId"], "parameters": {"accountId": {"description": "Account to which the ad client belongs.", "location": "path", "required": true, "type": "string"}, "adClientId": {"description": "Ad client which contains the custom channel.", "location": "path", "required": true, "type": "string"}, "customChannelId": {"description": "Custom channel for which to list ad units.", "location": "path", "required": true, "type": "string"}, "includeInactive": {"description": "Whether to include inactive ad units. Default: true.", "location": "query", "type": "boolean"}, "maxResults": {"description": "The maximum number of ad units to include in the response, used for paging.", "format": "int32", "location": "query", "maximum": "10000", "minimum": "0", "type": "integer"}, "pageToken": {"description": "A continuation token, used to page through ad units. To retrieve the next page, set this parameter to the value of \"nextPageToken\" from the previous response.", "location": "query", "type": "string"}}, "path": "accounts/{accountId}/adclients/{adClientId}/customchannels/{customChannelId}/adunits", "response": {"$ref": "AdUnits"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"]}}}}}, "payments": {"methods": {"list": {"description": "List the payments for the specified AdSense account.", "httpMethod": "GET", "id": "adsense.accounts.payments.list", "parameterOrder": ["accountId"], "parameters": {"accountId": {"description": "Account for which to retrieve the payments.", "location": "path", "required": true, "type": "string"}}, "path": "accounts/{accountId}/payments", "response": {"$ref": "Payments"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"]}}}, "reports": {"methods": {"generate": {"description": "Generate an AdSense report based on the report request sent in the query parameters. Returns the result as JSON; to retrieve output in CSV format specify \"alt=csv\" as a query parameter.", "httpMethod": "GET", "id": "adsense.accounts.reports.generate", "parameterOrder": ["accountId", "startDate", "endDate"], "parameters": {"accountId": {"description": "Account upon which to report.", "location": "path", "required": true, "type": "string"}, "currency": {"description": "Optional currency to use when reporting on monetary metrics. Defaults to the account's currency if not set.", "location": "query", "pattern": "[a-zA-Z]+", "type": "string"}, "dimension": {"description": "Dimensions to base the report on.", "location": "query", "pattern": "[a-zA-Z_]+", "repeated": true, "type": "string"}, "endDate": {"description": "End of the date range to report on in \"YYYY-MM-DD\" format, inclusive.", "location": "query", "pattern": "\\d{4}-\\d{2}-\\d{2}|(today|startOfMonth|startOfYear)(([\\-\\+]\\d+[dwmy]){0,3}?)|(latest-(\\d{2})-(\\d{2})(-\\d+y)?)|(latest-latest-(\\d{2})(-\\d+m)?)", "required": true, "type": "string"}, "filter": {"description": "Filters to be run on the report.", "location": "query", "pattern": "[a-zA-Z_]+(==|=@).+", "repeated": true, "type": "string"}, "locale": {"description": "Optional locale to use for translating report output to a local language. Defaults to \"en_US\" if not specified.", "location": "query", "pattern": "[a-zA-Z_]+", "type": "string"}, "maxResults": {"description": "The maximum number of rows of report data to return.", "format": "int32", "location": "query", "maximum": "50000", "minimum": "0", "type": "integer"}, "metric": {"description": "Numeric columns to include in the report.", "location": "query", "pattern": "[a-zA-Z_]+", "repeated": true, "type": "string"}, "sort": {"description": "The name of a dimension or metric to sort the resulting report on, optionally prefixed with \"+\" to sort ascending or \"-\" to sort descending. If no prefix is specified, the column is sorted ascending.", "location": "query", "pattern": "(\\+|-)?[a-zA-Z_]+", "repeated": true, "type": "string"}, "startDate": {"description": "Start of the date range to report on in \"YYYY-MM-DD\" format, inclusive.", "location": "query", "pattern": "\\d{4}-\\d{2}-\\d{2}|(today|startOfMonth|startOfYear)(([\\-\\+]\\d+[dwmy]){0,3}?)|(latest-(\\d{2})-(\\d{2})(-\\d+y)?)|(latest-latest-(\\d{2})(-\\d+m)?)", "required": true, "type": "string"}, "startIndex": {"description": "Index of the first row of report data to return.", "format": "int32", "location": "query", "maximum": "5000", "minimum": "0", "type": "integer"}, "useTimezoneReporting": {"description": "Whether the report should be generated in the AdSense account's local timezone. If false default PST/PDT timezone will be used.", "location": "query", "type": "boolean"}}, "path": "accounts/{accountId}/reports", "response": {"$ref": "AdsenseReportsGenerateResponse"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"], "supportsMediaDownload": true}}, "resources": {"saved": {"methods": {"generate": {"description": "Generate an AdSense report based on the saved report ID sent in the query parameters.", "httpMethod": "GET", "id": "adsense.accounts.reports.saved.generate", "parameterOrder": ["accountId", "savedReportId"], "parameters": {"accountId": {"description": "Account to which the saved reports belong.", "location": "path", "required": true, "type": "string"}, "locale": {"description": "Optional locale to use for translating report output to a local language. Defaults to \"en_US\" if not specified.", "location": "query", "pattern": "[a-zA-Z_]+", "type": "string"}, "maxResults": {"description": "The maximum number of rows of report data to return.", "format": "int32", "location": "query", "maximum": "50000", "minimum": "0", "type": "integer"}, "savedReportId": {"description": "The saved report to retrieve.", "location": "path", "required": true, "type": "string"}, "startIndex": {"description": "Index of the first row of report data to return.", "format": "int32", "location": "query", "maximum": "5000", "minimum": "0", "type": "integer"}}, "path": "accounts/{accountId}/reports/{savedReportId}", "response": {"$ref": "AdsenseReportsGenerateResponse"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"]}, "list": {"description": "List all saved reports in the specified AdSense account.", "httpMethod": "GET", "id": "adsense.accounts.reports.saved.list", "parameterOrder": ["accountId"], "parameters": {"accountId": {"description": "Account to which the saved reports belong.", "location": "path", "required": true, "type": "string"}, "maxResults": {"description": "The maximum number of saved reports to include in the response, used for paging.", "format": "int32", "location": "query", "maximum": "100", "minimum": "0", "type": "integer"}, "pageToken": {"description": "A continuation token, used to page through saved reports. To retrieve the next page, set this parameter to the value of \"nextPageToken\" from the previous response.", "location": "query", "type": "string"}}, "path": "accounts/{accountId}/reports/saved", "response": {"$ref": "SavedReports"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"]}}}}}, "savedadstyles": {"methods": {"get": {"description": "List a specific saved ad style for the specified account.", "httpMethod": "GET", "id": "adsense.accounts.savedadstyles.get", "parameterOrder": ["accountId", "savedAdStyleId"], "parameters": {"accountId": {"description": "Account for which to get the saved ad style.", "location": "path", "required": true, "type": "string"}, "savedAdStyleId": {"description": "Saved ad style to retrieve.", "location": "path", "required": true, "type": "string"}}, "path": "accounts/{accountId}/savedadstyles/{savedAdStyleId}", "response": {"$ref": "SavedAdStyle"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"]}, "list": {"description": "List all saved ad styles in the specified account.", "httpMethod": "GET", "id": "adsense.accounts.savedadstyles.list", "parameterOrder": ["accountId"], "parameters": {"accountId": {"description": "Account for which to list saved ad styles.", "location": "path", "required": true, "type": "string"}, "maxResults": {"description": "The maximum number of saved ad styles to include in the response, used for paging.", "format": "int32", "location": "query", "maximum": "10000", "minimum": "0", "type": "integer"}, "pageToken": {"description": "A continuation token, used to page through saved ad styles. To retrieve the next page, set this parameter to the value of \"nextPageToken\" from the previous response.", "location": "query", "type": "string"}}, "path": "accounts/{accountId}/savedadstyles", "response": {"$ref": "SavedAdStyles"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"]}}}, "urlchannels": {"methods": {"list": {"description": "List all URL channels in the specified ad client for the specified account.", "httpMethod": "GET", "id": "adsense.accounts.urlchannels.list", "parameterOrder": ["accountId", "adClientId"], "parameters": {"accountId": {"description": "Account to which the ad client belongs.", "location": "path", "required": true, "type": "string"}, "adClientId": {"description": "Ad client for which to list URL channels.", "location": "path", "required": true, "type": "string"}, "maxResults": {"description": "The maximum number of URL channels to include in the response, used for paging.", "format": "int32", "location": "query", "maximum": "10000", "minimum": "0", "type": "integer"}, "pageToken": {"description": "A continuation token, used to page through URL channels. To retrieve the next page, set this parameter to the value of \"nextPageToken\" from the previous response.", "location": "query", "type": "string"}}, "path": "accounts/{accountId}/adclients/{adClientId}/urlchannels", "response": {"$ref": "UrlChannels"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"]}}}}}, "adclients": {"methods": {"list": {"description": "List all ad clients in this AdSense account.", "httpMethod": "GET", "id": "adsense.adclients.list", "parameters": {"maxResults": {"description": "The maximum number of ad clients to include in the response, used for paging.", "format": "int32", "location": "query", "maximum": "10000", "minimum": "0", "type": "integer"}, "pageToken": {"description": "A continuation token, used to page through ad clients. To retrieve the next page, set this parameter to the value of \"nextPageToken\" from the previous response.", "location": "query", "type": "string"}}, "path": "adclients", "response": {"$ref": "AdClients"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"]}}}, "adunits": {"methods": {"get": {"description": "Gets the specified ad unit in the specified ad client.", "httpMethod": "GET", "id": "adsense.adunits.get", "parameterOrder": ["adClientId", "adUnitId"], "parameters": {"adClientId": {"description": "Ad client for which to get the ad unit.", "location": "path", "required": true, "type": "string"}, "adUnitId": {"description": "Ad unit to retrieve.", "location": "path", "required": true, "type": "string"}}, "path": "adclients/{adClientId}/adunits/{adUnitId}", "response": {"$ref": "AdUnit"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"]}, "getAdCode": {"description": "Get ad code for the specified ad unit.", "httpMethod": "GET", "id": "adsense.adunits.getAdCode", "parameterOrder": ["adClientId", "adUnitId"], "parameters": {"adClientId": {"description": "Ad client with contains the ad unit.", "location": "path", "required": true, "type": "string"}, "adUnitId": {"description": "Ad unit to get the code for.", "location": "path", "required": true, "type": "string"}}, "path": "adclients/{adClientId}/adunits/{adUnitId}/adcode", "response": {"$ref": "AdCode"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"]}, "list": {"description": "List all ad units in the specified ad client for this AdSense account.", "httpMethod": "GET", "id": "adsense.adunits.list", "parameterOrder": ["adClientId"], "parameters": {"adClientId": {"description": "Ad client for which to list ad units.", "location": "path", "required": true, "type": "string"}, "includeInactive": {"description": "Whether to include inactive ad units. Default: true.", "location": "query", "type": "boolean"}, "maxResults": {"description": "The maximum number of ad units to include in the response, used for paging.", "format": "int32", "location": "query", "maximum": "10000", "minimum": "0", "type": "integer"}, "pageToken": {"description": "A continuation token, used to page through ad units. To retrieve the next page, set this parameter to the value of \"nextPageToken\" from the previous response.", "location": "query", "type": "string"}}, "path": "adclients/{adClientId}/adunits", "response": {"$ref": "AdUnits"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"]}}, "resources": {"customchannels": {"methods": {"list": {"description": "List all custom channels which the specified ad unit belongs to.", "httpMethod": "GET", "id": "adsense.adunits.customchannels.list", "parameterOrder": ["adClientId", "adUnitId"], "parameters": {"adClientId": {"description": "Ad client which contains the ad unit.", "location": "path", "required": true, "type": "string"}, "adUnitId": {"description": "Ad unit for which to list custom channels.", "location": "path", "required": true, "type": "string"}, "maxResults": {"description": "The maximum number of custom channels to include in the response, used for paging.", "format": "int32", "location": "query", "maximum": "10000", "minimum": "0", "type": "integer"}, "pageToken": {"description": "A continuation token, used to page through custom channels. To retrieve the next page, set this parameter to the value of \"nextPageToken\" from the previous response.", "location": "query", "type": "string"}}, "path": "adclients/{adClientId}/adunits/{adUnitId}/customchannels", "response": {"$ref": "CustomChannels"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"]}}}}}, "alerts": {"methods": {"delete": {"description": "Dismiss (delete) the specified alert from the publisher's AdSense account.", "httpMethod": "DELETE", "id": "adsense.alerts.delete", "parameterOrder": ["alertId"], "parameters": {"alertId": {"description": "<PERSON><PERSON> to delete.", "location": "path", "required": true, "type": "string"}}, "path": "alerts/{alertId}", "scopes": ["https://www.googleapis.com/auth/adsense"]}, "list": {"description": "List the alerts for this AdSense account.", "httpMethod": "GET", "id": "adsense.alerts.list", "parameters": {"locale": {"description": "The locale to use for translating alert messages. The account locale will be used if this is not supplied. The AdSense default (English) will be used if the supplied locale is invalid or unsupported.", "location": "query", "type": "string"}}, "path": "alerts", "response": {"$ref": "<PERSON><PERSON><PERSON>"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"]}}}, "customchannels": {"methods": {"get": {"description": "Get the specified custom channel from the specified ad client.", "httpMethod": "GET", "id": "adsense.customchannels.get", "parameterOrder": ["adClientId", "customChannelId"], "parameters": {"adClientId": {"description": "Ad client which contains the custom channel.", "location": "path", "required": true, "type": "string"}, "customChannelId": {"description": "Custom channel to retrieve.", "location": "path", "required": true, "type": "string"}}, "path": "adclients/{adClientId}/customchannels/{customChannelId}", "response": {"$ref": "CustomChannel"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"]}, "list": {"description": "List all custom channels in the specified ad client for this AdSense account.", "httpMethod": "GET", "id": "adsense.customchannels.list", "parameterOrder": ["adClientId"], "parameters": {"adClientId": {"description": "Ad client for which to list custom channels.", "location": "path", "required": true, "type": "string"}, "maxResults": {"description": "The maximum number of custom channels to include in the response, used for paging.", "format": "int32", "location": "query", "maximum": "10000", "minimum": "0", "type": "integer"}, "pageToken": {"description": "A continuation token, used to page through custom channels. To retrieve the next page, set this parameter to the value of \"nextPageToken\" from the previous response.", "location": "query", "type": "string"}}, "path": "adclients/{adClientId}/customchannels", "response": {"$ref": "CustomChannels"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"]}}, "resources": {"adunits": {"methods": {"list": {"description": "List all ad units in the specified custom channel.", "httpMethod": "GET", "id": "adsense.customchannels.adunits.list", "parameterOrder": ["adClientId", "customChannelId"], "parameters": {"adClientId": {"description": "Ad client which contains the custom channel.", "location": "path", "required": true, "type": "string"}, "customChannelId": {"description": "Custom channel for which to list ad units.", "location": "path", "required": true, "type": "string"}, "includeInactive": {"description": "Whether to include inactive ad units. Default: true.", "location": "query", "type": "boolean"}, "maxResults": {"description": "The maximum number of ad units to include in the response, used for paging.", "format": "int32", "location": "query", "maximum": "10000", "minimum": "0", "type": "integer"}, "pageToken": {"description": "A continuation token, used to page through ad units. To retrieve the next page, set this parameter to the value of \"nextPageToken\" from the previous response.", "location": "query", "type": "string"}}, "path": "adclients/{adClientId}/customchannels/{customChannelId}/adunits", "response": {"$ref": "AdUnits"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"]}}}}}, "metadata": {"resources": {"dimensions": {"methods": {"list": {"description": "List the metadata for the dimensions available to this AdSense account.", "httpMethod": "GET", "id": "adsense.metadata.dimensions.list", "path": "metadata/dimensions", "response": {"$ref": "<PERSON><PERSON><PERSON>"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"]}}}, "metrics": {"methods": {"list": {"description": "List the metadata for the metrics available to this AdSense account.", "httpMethod": "GET", "id": "adsense.metadata.metrics.list", "path": "metadata/metrics", "response": {"$ref": "<PERSON><PERSON><PERSON>"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"]}}}}}, "payments": {"methods": {"list": {"description": "List the payments for this AdSense account.", "httpMethod": "GET", "id": "adsense.payments.list", "path": "payments", "response": {"$ref": "Payments"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"]}}}, "reports": {"methods": {"generate": {"description": "Generate an AdSense report based on the report request sent in the query parameters. Returns the result as JSON; to retrieve output in CSV format specify \"alt=csv\" as a query parameter.", "httpMethod": "GET", "id": "adsense.reports.generate", "parameterOrder": ["startDate", "endDate"], "parameters": {"accountId": {"description": "Accounts upon which to report.", "location": "query", "repeated": true, "type": "string"}, "currency": {"description": "Optional currency to use when reporting on monetary metrics. Defaults to the account's currency if not set.", "location": "query", "pattern": "[a-zA-Z]+", "type": "string"}, "dimension": {"description": "Dimensions to base the report on.", "location": "query", "pattern": "[a-zA-Z_]+", "repeated": true, "type": "string"}, "endDate": {"description": "End of the date range to report on in \"YYYY-MM-DD\" format, inclusive.", "location": "query", "pattern": "\\d{4}-\\d{2}-\\d{2}|(today|startOfMonth|startOfYear)(([\\-\\+]\\d+[dwmy]){0,3}?)|(latest-(\\d{2})-(\\d{2})(-\\d+y)?)|(latest-latest-(\\d{2})(-\\d+m)?)", "required": true, "type": "string"}, "filter": {"description": "Filters to be run on the report.", "location": "query", "pattern": "[a-zA-Z_]+(==|=@).+", "repeated": true, "type": "string"}, "locale": {"description": "Optional locale to use for translating report output to a local language. Defaults to \"en_US\" if not specified.", "location": "query", "pattern": "[a-zA-Z_]+", "type": "string"}, "maxResults": {"description": "The maximum number of rows of report data to return.", "format": "int32", "location": "query", "maximum": "50000", "minimum": "0", "type": "integer"}, "metric": {"description": "Numeric columns to include in the report.", "location": "query", "pattern": "[a-zA-Z_]+", "repeated": true, "type": "string"}, "sort": {"description": "The name of a dimension or metric to sort the resulting report on, optionally prefixed with \"+\" to sort ascending or \"-\" to sort descending. If no prefix is specified, the column is sorted ascending.", "location": "query", "pattern": "(\\+|-)?[a-zA-Z_]+", "repeated": true, "type": "string"}, "startDate": {"description": "Start of the date range to report on in \"YYYY-MM-DD\" format, inclusive.", "location": "query", "pattern": "\\d{4}-\\d{2}-\\d{2}|(today|startOfMonth|startOfYear)(([\\-\\+]\\d+[dwmy]){0,3}?)|(latest-(\\d{2})-(\\d{2})(-\\d+y)?)|(latest-latest-(\\d{2})(-\\d+m)?)", "required": true, "type": "string"}, "startIndex": {"description": "Index of the first row of report data to return.", "format": "int32", "location": "query", "maximum": "5000", "minimum": "0", "type": "integer"}, "useTimezoneReporting": {"description": "Whether the report should be generated in the AdSense account's local timezone. If false default PST/PDT timezone will be used.", "location": "query", "type": "boolean"}}, "path": "reports", "response": {"$ref": "AdsenseReportsGenerateResponse"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"], "supportsMediaDownload": true}}, "resources": {"saved": {"methods": {"generate": {"description": "Generate an AdSense report based on the saved report ID sent in the query parameters.", "httpMethod": "GET", "id": "adsense.reports.saved.generate", "parameterOrder": ["savedReportId"], "parameters": {"locale": {"description": "Optional locale to use for translating report output to a local language. Defaults to \"en_US\" if not specified.", "location": "query", "pattern": "[a-zA-Z_]+", "type": "string"}, "maxResults": {"description": "The maximum number of rows of report data to return.", "format": "int32", "location": "query", "maximum": "50000", "minimum": "0", "type": "integer"}, "savedReportId": {"description": "The saved report to retrieve.", "location": "path", "required": true, "type": "string"}, "startIndex": {"description": "Index of the first row of report data to return.", "format": "int32", "location": "query", "maximum": "5000", "minimum": "0", "type": "integer"}}, "path": "reports/{savedReportId}", "response": {"$ref": "AdsenseReportsGenerateResponse"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"]}, "list": {"description": "List all saved reports in this AdSense account.", "httpMethod": "GET", "id": "adsense.reports.saved.list", "parameters": {"maxResults": {"description": "The maximum number of saved reports to include in the response, used for paging.", "format": "int32", "location": "query", "maximum": "100", "minimum": "0", "type": "integer"}, "pageToken": {"description": "A continuation token, used to page through saved reports. To retrieve the next page, set this parameter to the value of \"nextPageToken\" from the previous response.", "location": "query", "type": "string"}}, "path": "reports/saved", "response": {"$ref": "SavedReports"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"]}}}}}, "savedadstyles": {"methods": {"get": {"description": "Get a specific saved ad style from the user's account.", "httpMethod": "GET", "id": "adsense.savedadstyles.get", "parameterOrder": ["savedAdStyleId"], "parameters": {"savedAdStyleId": {"description": "Saved ad style to retrieve.", "location": "path", "required": true, "type": "string"}}, "path": "savedadstyles/{savedAdStyleId}", "response": {"$ref": "SavedAdStyle"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"]}, "list": {"description": "List all saved ad styles in the user's account.", "httpMethod": "GET", "id": "adsense.savedadstyles.list", "parameters": {"maxResults": {"description": "The maximum number of saved ad styles to include in the response, used for paging.", "format": "int32", "location": "query", "maximum": "10000", "minimum": "0", "type": "integer"}, "pageToken": {"description": "A continuation token, used to page through saved ad styles. To retrieve the next page, set this parameter to the value of \"nextPageToken\" from the previous response.", "location": "query", "type": "string"}}, "path": "savedadstyles", "response": {"$ref": "SavedAdStyles"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"]}}}, "urlchannels": {"methods": {"list": {"description": "List all URL channels in the specified ad client for this AdSense account.", "httpMethod": "GET", "id": "adsense.urlchannels.list", "parameterOrder": ["adClientId"], "parameters": {"adClientId": {"description": "Ad client for which to list URL channels.", "location": "path", "required": true, "type": "string"}, "maxResults": {"description": "The maximum number of URL channels to include in the response, used for paging.", "format": "int32", "location": "query", "maximum": "10000", "minimum": "0", "type": "integer"}, "pageToken": {"description": "A continuation token, used to page through URL channels. To retrieve the next page, set this parameter to the value of \"nextPageToken\" from the previous response.", "location": "query", "type": "string"}}, "path": "adclients/{adClientId}/urlchannels", "response": {"$ref": "UrlChannels"}, "scopes": ["https://www.googleapis.com/auth/adsense", "https://www.googleapis.com/auth/adsense.readonly"]}}}}, "revision": "********", "rootUrl": "https://www.googleapis.com/", "schemas": {"Account": {"id": "Account", "properties": {"creation_time": {"format": "int64", "type": "string"}, "id": {"description": "Unique identifier of this account.", "type": "string"}, "kind": {"default": "adsense#account", "description": "Kind of resource this is, in this case adsense#account.", "type": "string"}, "name": {"description": "Name of this account.", "type": "string"}, "premium": {"description": "Whether this account is premium.", "type": "boolean"}, "subAccounts": {"description": "Sub accounts of the this account.", "items": {"$ref": "Account"}, "type": "array"}, "timezone": {"description": "AdSense timezone of this account.", "type": "string"}}, "type": "object"}, "Accounts": {"id": "Accounts", "properties": {"etag": {"description": "ETag of this response for caching purposes.", "type": "string"}, "items": {"description": "The accounts returned in this list response.", "items": {"$ref": "Account"}, "type": "array"}, "kind": {"default": "adsense#accounts", "description": "Kind of list this is, in this case adsense#accounts.", "type": "string"}, "nextPageToken": {"description": "Continuation token used to page through accounts. To retrieve the next page of results, set the next request's \"pageToken\" value to this.", "type": "string"}}, "type": "object"}, "AdClient": {"id": "AdClient", "properties": {"arcOptIn": {"description": "Whether this ad client is opted in to ARC.", "type": "boolean"}, "id": {"description": "Unique identifier of this ad client.", "type": "string"}, "kind": {"default": "adsense#adClient", "description": "Kind of resource this is, in this case adsense#adClient.", "type": "string"}, "productCode": {"description": "This ad client's product code, which corresponds to the PRODUCT_CODE report dimension.", "type": "string"}, "supportsReporting": {"description": "Whether this ad client supports being reported on.", "type": "boolean"}}, "type": "object"}, "AdClients": {"id": "AdClients", "properties": {"etag": {"description": "ETag of this response for caching purposes.", "type": "string"}, "items": {"description": "The ad clients returned in this list response.", "items": {"$ref": "AdClient"}, "type": "array"}, "kind": {"default": "adsense#adClients", "description": "Kind of list this is, in this case adsense#adClients.", "type": "string"}, "nextPageToken": {"description": "Continuation token used to page through ad clients. To retrieve the next page of results, set the next request's \"pageToken\" value to this.", "type": "string"}}, "type": "object"}, "AdCode": {"id": "AdCode", "properties": {"adCode": {"description": "The Auto ad code snippet. The ad code snippet.", "type": "string"}, "ampBody": {"description": "The AMP Auto ad code snippet that goes in the body of an AMP page.", "type": "string"}, "ampHead": {"description": "The AMP Auto ad code snippet that goes in the head of an AMP page.", "type": "string"}, "kind": {"default": "adsense#adCode", "description": "Kind this is, in this case adsense#adCode.", "type": "string"}}, "type": "object"}, "AdStyle": {"id": "AdStyle", "properties": {"colors": {"description": "The colors which are included in the style. These are represented as six hexadecimal characters, similar to HTML color codes, but without the leading hash.", "properties": {"background": {"description": "The color of the ad background.", "type": "string"}, "border": {"description": "The color of the ad border.", "type": "string"}, "text": {"description": "The color of the ad text.", "type": "string"}, "title": {"description": "The color of the ad title.", "type": "string"}, "url": {"description": "The color of the ad url.", "type": "string"}}, "type": "object"}, "corners": {"description": "The style of the corners in the ad (deprecated: never populated, ignored).", "type": "string"}, "font": {"description": "The font which is included in the style.", "properties": {"family": {"description": "The family of the font.", "type": "string"}, "size": {"description": "The size of the font.", "type": "string"}}, "type": "object"}, "kind": {"default": "adsense#adStyle", "description": "Kind this is, in this case adsense#adStyle.", "type": "string"}}, "type": "object"}, "AdUnit": {"id": "AdUnit", "properties": {"code": {"description": "Identity code of this ad unit, not necessarily unique across ad clients.", "type": "string"}, "contentAdsSettings": {"description": "Settings specific to content ads (AFC) and highend mobile content ads (AFMC - deprecated).", "properties": {"backupOption": {"description": "The backup option to be used in instances where no ad is available.", "properties": {"color": {"description": "Color to use when type is set to COLOR.", "type": "string"}, "type": {"description": "Type of the backup option. Possible values are BLANK, COLOR and URL.", "type": "string"}, "url": {"description": "URL to use when type is set to URL.", "type": "string"}}, "type": "object"}, "size": {"description": "Size of this ad unit.", "type": "string"}, "type": {"description": "Type of this ad unit.", "type": "string"}}, "type": "object"}, "customStyle": {"$ref": "AdStyle", "description": "Custom style information specific to this ad unit."}, "feedAdsSettings": {"description": "Settings specific to feed ads (AFF) - deprecated.", "properties": {"adPosition": {"description": "The position of the ads relative to the feed entries.", "type": "string"}, "frequency": {"description": "The frequency at which ads should appear in the feed (i.e. every N entries).", "format": "int32", "type": "integer"}, "minimumWordCount": {"description": "The minimum length an entry should be in order to have attached ads.", "format": "int32", "type": "integer"}, "type": {"description": "The type of ads which should appear.", "type": "string"}}, "type": "object"}, "id": {"description": "Unique identifier of this ad unit. This should be considered an opaque identifier; it is not safe to rely on it being in any particular format.", "type": "string"}, "kind": {"default": "adsense#adUnit", "description": "Kind of resource this is, in this case adsense#adUnit.", "type": "string"}, "mobileContentAdsSettings": {"description": "Settings specific to WAP mobile content ads (AFMC) - deprecated.", "properties": {"markupLanguage": {"description": "The markup language to use for this ad unit.", "type": "string"}, "scriptingLanguage": {"description": "The scripting language to use for this ad unit.", "type": "string"}, "size": {"description": "Size of this ad unit.", "type": "string"}, "type": {"description": "Type of this ad unit.", "type": "string"}}, "type": "object"}, "name": {"description": "Name of this ad unit.", "type": "string"}, "savedStyleId": {"description": "ID of the saved ad style which holds this ad unit's style information.", "type": "string"}, "status": {"description": "Status of this ad unit. Possible values are:\nNEW: Indicates that the ad unit was created within the last seven days and does not yet have any activity associated with it.\n\nACTIVE: Indicates that there has been activity on this ad unit in the last seven days.\n\nINACTIVE: Indicates that there has been no activity on this ad unit in the last seven days.", "type": "string"}}, "type": "object"}, "AdUnits": {"id": "AdUnits", "properties": {"etag": {"description": "ETag of this response for caching purposes.", "type": "string"}, "items": {"description": "The ad units returned in this list response.", "items": {"$ref": "AdUnit"}, "type": "array"}, "kind": {"default": "adsense#adUnits", "description": "Kind of list this is, in this case adsense#adUnits.", "type": "string"}, "nextPageToken": {"description": "Continuation token used to page through ad units. To retrieve the next page of results, set the next request's \"pageToken\" value to this.", "type": "string"}}, "type": "object"}, "AdsenseReportsGenerateResponse": {"id": "AdsenseReportsGenerateResponse", "properties": {"averages": {"description": "The averages of the report. This is the same length as any other row in the report; cells corresponding to dimension columns are empty.", "items": {"type": "string"}, "type": "array"}, "endDate": {"description": "The requested end date in yyyy-mm-dd format.", "type": "string"}, "headers": {"description": "The header information of the columns requested in the report. This is a list of headers; one for each dimension in the request, followed by one for each metric in the request.", "items": {"properties": {"currency": {"description": "The currency of this column. Only present if the header type is METRIC_CURRENCY.", "type": "string"}, "name": {"description": "The name of the header.", "type": "string"}, "type": {"description": "The type of the header; one of DIM<PERSON><PERSON><PERSON>, METRIC_TALLY, <PERSON>TRIC_RATIO, or METRIC_CURRENCY.", "type": "string"}}, "type": "object"}, "type": "array"}, "kind": {"default": "adsense#report", "description": "Kind this is, in this case adsense#report.", "type": "string"}, "rows": {"description": "The output rows of the report. Each row is a list of cells; one for each dimension in the request, followed by one for each metric in the request. The dimension cells contain strings, and the metric cells contain numbers.", "items": {"items": {"type": "string"}, "type": "array"}, "type": "array"}, "startDate": {"description": "The requested start date in yyyy-mm-dd format.", "type": "string"}, "totalMatchedRows": {"description": "The total number of rows matched by the report request. Fewer rows may be returned in the response due to being limited by the row count requested or the report row limit.", "format": "int64", "type": "string"}, "totals": {"description": "The totals of the report. This is the same length as any other row in the report; cells corresponding to dimension columns are empty.", "items": {"type": "string"}, "type": "array"}, "warnings": {"description": "Any warnings associated with generation of the report.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Alert": {"id": "<PERSON><PERSON>", "properties": {"id": {"description": "Unique identifier of this alert. This should be considered an opaque identifier; it is not safe to rely on it being in any particular format.", "type": "string"}, "isDismissible": {"description": "Whether this alert can be dismissed.", "type": "boolean"}, "kind": {"default": "adsense#alert", "description": "Kind of resource this is, in this case adsense#alert.", "type": "string"}, "message": {"description": "The localized alert message.", "type": "string"}, "severity": {"description": "Severity of this alert. Possible values: INFO, WARNING, SEVERE.", "type": "string"}, "type": {"description": "Type of this alert. Possible values: SELF_HOLD, MIGRATED_TO_BILLING3, ADDRESS_PIN_VERIFICATION, PHONE_PIN_VERIFICATION, CORPORATE_ENTITY, GRAYLISTED_PUBLISHER, API_HOLD.", "type": "string"}}, "type": "object"}, "Alerts": {"id": "<PERSON><PERSON><PERSON>", "properties": {"items": {"description": "The alerts returned in this list response.", "items": {"$ref": "<PERSON><PERSON>"}, "type": "array"}, "kind": {"default": "adsense#alerts", "description": "Kind of list this is, in this case adsense#alerts.", "type": "string"}}, "type": "object"}, "CustomChannel": {"id": "CustomChannel", "properties": {"code": {"description": "Code of this custom channel, not necessarily unique across ad clients.", "type": "string"}, "id": {"description": "Unique identifier of this custom channel. This should be considered an opaque identifier; it is not safe to rely on it being in any particular format.", "type": "string"}, "kind": {"default": "adsense#customChannel", "description": "Kind of resource this is, in this case adsense#customChannel.", "type": "string"}, "name": {"description": "Name of this custom channel.", "type": "string"}, "targetingInfo": {"description": "The targeting information of this custom channel, if activated.", "properties": {"adsAppearOn": {"description": "The name used to describe this channel externally.", "type": "string"}, "description": {"description": "The external description of the channel.", "type": "string"}, "location": {"description": "The locations in which ads appear. (Only valid for content and mobile content ads (deprecated)). Acceptable values for content ads are: TOP_LEFT, TOP_CENTER, TOP_RIGHT, MIDDLE_LEFT, MIDDLE_CENTER, MID<PERSON><PERSON>_RIGHT, BOTTOM_LEFT, BOT<PERSON>M_CENTER, BOT<PERSON>M_RIGHT, M<PERSON><PERSON><PERSON>LE_LOCATIONS. Acceptable values for mobile content ads (deprecated) are: TOP, MIDDLE, BOTTOM, MULTIPLE_LOCATIONS.", "type": "string"}, "siteLanguage": {"description": "The language of the sites ads will be displayed on.", "type": "string"}}, "type": "object"}}, "type": "object"}, "CustomChannels": {"id": "CustomChannels", "properties": {"etag": {"description": "ETag of this response for caching purposes.", "type": "string"}, "items": {"description": "The custom channels returned in this list response.", "items": {"$ref": "CustomChannel"}, "type": "array"}, "kind": {"default": "adsense#customChannels", "description": "Kind of list this is, in this case adsense#customChannels.", "type": "string"}, "nextPageToken": {"description": "Continuation token used to page through custom channels. To retrieve the next page of results, set the next request's \"pageToken\" value to this.", "type": "string"}}, "type": "object"}, "Metadata": {"id": "<PERSON><PERSON><PERSON>", "properties": {"items": {"items": {"$ref": "ReportingMetadataEntry"}, "type": "array"}, "kind": {"default": "adsense#metadata", "description": "Kind of list this is, in this case adsense#metadata.", "type": "string"}}, "type": "object"}, "Payment": {"id": "Payment", "properties": {"id": {"description": "Unique identifier of this Payment.", "type": "string"}, "kind": {"default": "adsense#payment", "description": "Kind of resource this is, in this case adsense#payment.", "type": "string"}, "paymentAmount": {"description": "The amount to be paid.", "type": "string"}, "paymentAmountCurrencyCode": {"description": "The currency code for the amount to be paid.", "type": "string"}, "paymentDate": {"description": "The date this payment was/will be credited to the user, or none if the payment threshold has not been met.", "type": "string"}}, "type": "object"}, "Payments": {"id": "Payments", "properties": {"items": {"description": "The list of Payments for the account. One or both of a) the account's most recent payment; and b) the account's upcoming payment.", "items": {"$ref": "Payment"}, "type": "array"}, "kind": {"default": "adsense#payments", "description": "Kind of list this is, in this case adsense#payments.", "type": "string"}}, "type": "object"}, "ReportingMetadataEntry": {"id": "ReportingMetadataEntry", "properties": {"compatibleDimensions": {"description": "For metrics this is a list of dimension IDs which the metric is compatible with, for dimensions it is a list of compatibility groups the dimension belongs to.", "items": {"type": "string"}, "type": "array"}, "compatibleMetrics": {"description": "The names of the metrics the dimension or metric this reporting metadata entry describes is compatible with.", "items": {"type": "string"}, "type": "array"}, "id": {"description": "Unique identifier of this reporting metadata entry, corresponding to the name of the appropriate dimension or metric.", "type": "string"}, "kind": {"default": "adsense#reportingMetadataEntry", "description": "Kind of resource this is, in this case adsense#reportingMetadataEntry.", "type": "string"}, "requiredDimensions": {"description": "The names of the dimensions which the dimension or metric this reporting metadata entry describes requires to also be present in order for the report to be valid. Omitting these will not cause an error or warning, but may result in data which cannot be correctly interpreted.", "items": {"type": "string"}, "type": "array"}, "requiredMetrics": {"description": "The names of the metrics which the dimension or metric this reporting metadata entry describes requires to also be present in order for the report to be valid. Omitting these will not cause an error or warning, but may result in data which cannot be correctly interpreted.", "items": {"type": "string"}, "type": "array"}, "supportedProducts": {"description": "The codes of the projects supported by the dimension or metric this reporting metadata entry describes.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "SavedAdStyle": {"id": "SavedAdStyle", "properties": {"adStyle": {"$ref": "AdStyle", "description": "The AdStyle itself."}, "id": {"description": "Unique identifier of this saved ad style. This should be considered an opaque identifier; it is not safe to rely on it being in any particular format.", "type": "string"}, "kind": {"default": "adsense#savedAdStyle", "description": "Kind of resource this is, in this case adsense#savedAdStyle.", "type": "string"}, "name": {"description": "The user selected name of this SavedAdStyle.", "type": "string"}}, "type": "object"}, "SavedAdStyles": {"id": "SavedAdStyles", "properties": {"etag": {"description": "ETag of this response for caching purposes.", "type": "string"}, "items": {"description": "The saved ad styles returned in this list response.", "items": {"$ref": "SavedAdStyle"}, "type": "array"}, "kind": {"default": "adsense#savedAdStyles", "description": "Kind of list this is, in this case adsense#savedAdStyles.", "type": "string"}, "nextPageToken": {"description": "Continuation token used to page through ad units. To retrieve the next page of results, set the next request's \"pageToken\" value to this.", "type": "string"}}, "type": "object"}, "SavedReport": {"id": "SavedReport", "properties": {"id": {"description": "Unique identifier of this saved report.", "type": "string"}, "kind": {"default": "adsense#savedReport", "description": "Kind of resource this is, in this case adsense#savedReport.", "type": "string"}, "name": {"description": "This saved report's name.", "type": "string"}}, "type": "object"}, "SavedReports": {"id": "SavedReports", "properties": {"etag": {"description": "ETag of this response for caching purposes.", "type": "string"}, "items": {"description": "The saved reports returned in this list response.", "items": {"$ref": "SavedReport"}, "type": "array"}, "kind": {"default": "adsense#savedReports", "description": "Kind of list this is, in this case adsense#savedReports.", "type": "string"}, "nextPageToken": {"description": "Continuation token used to page through saved reports. To retrieve the next page of results, set the next request's \"pageToken\" value to this.", "type": "string"}}, "type": "object"}, "UrlChannel": {"id": "UrlChannel", "properties": {"id": {"description": "Unique identifier of this URL channel. This should be considered an opaque identifier; it is not safe to rely on it being in any particular format.", "type": "string"}, "kind": {"default": "adsense#urlChannel", "description": "Kind of resource this is, in this case adsense#urlChannel.", "type": "string"}, "urlPattern": {"description": "URL Pattern of this URL channel. Does not include \"http://\" or \"https://\". Example: www.example.com/home", "type": "string"}}, "type": "object"}, "UrlChannels": {"id": "UrlChannels", "properties": {"etag": {"description": "ETag of this response for caching purposes.", "type": "string"}, "items": {"description": "The URL channels returned in this list response.", "items": {"$ref": "UrlChannel"}, "type": "array"}, "kind": {"default": "adsense#urlChannels", "description": "Kind of list this is, in this case adsense#urlChannels.", "type": "string"}, "nextPageToken": {"description": "Continuation token used to page through URL channels. To retrieve the next page of results, set the next request's \"pageToken\" value to this.", "type": "string"}}, "type": "object"}}, "servicePath": "adsense/v1.4/", "title": "AdSense Management API", "version": "v1.4"}