{"canonicalName": "AdSense Host", "auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/adsensehost": {"description": "View and manage your AdSense host data and associated accounts"}}}}, "rootUrl": "https://www.googleapis.com/", "ownerDomain": "google.com", "name": "adsensehost", "batchPath": "batch/adsensehost/v4.1", "title": "AdSense Host API", "ownerName": "Google", "labels": ["limited_availability"], "resources": {"adclients": {"methods": {"get": {"id": "adsensehost.adclients.get", "path": "adclients/{adClientId}", "description": "Get information about one of the ad clients in the Host AdSense account.", "httpMethod": "GET", "response": {"$ref": "AdClient"}, "parameterOrder": ["adClientId"], "parameters": {"adClientId": {"location": "path", "description": "Ad client to get.", "type": "string", "required": true}}, "scopes": ["https://www.googleapis.com/auth/adsensehost"]}, "list": {"response": {"$ref": "AdClients"}, "httpMethod": "GET", "scopes": ["https://www.googleapis.com/auth/adsensehost"], "parameters": {"maxResults": {"description": "The maximum number of ad clients to include in the response, used for paging.", "format": "uint32", "maximum": "10000", "type": "integer", "minimum": "0", "location": "query"}, "pageToken": {"description": "A continuation token, used to page through ad clients. To retrieve the next page, set this parameter to the value of \"nextPageToken\" from the previous response.", "type": "string", "location": "query"}}, "path": "adclients", "id": "adsensehost.adclients.list", "description": "List all host ad clients in this AdSense account."}}}, "reports": {"methods": {"generate": {"response": {"$ref": "Report"}, "parameterOrder": ["startDate", "endDate"], "httpMethod": "GET", "scopes": ["https://www.googleapis.com/auth/adsensehost"], "parameters": {"locale": {"description": "Optional locale to use for translating report output to a local language. Defaults to \"en_US\" if not specified.", "type": "string", "pattern": "[a-zA-Z_]+", "location": "query"}, "endDate": {"description": "End of the date range to report on in \"YYYY-MM-DD\" format, inclusive.", "type": "string", "required": true, "pattern": "\\d{4}-\\d{2}-\\d{2}|(today|startOfMonth|startOfYear)(([\\-\\+]\\d+[dwmy]){0,3}?)", "location": "query"}, "metric": {"pattern": "[a-zA-Z_]+", "repeated": true, "location": "query", "description": "Numeric columns to include in the report.", "type": "string"}, "maxResults": {"description": "The maximum number of rows of report data to return.", "format": "uint32", "maximum": "50000", "type": "integer", "minimum": "0", "location": "query"}, "sort": {"description": "The name of a dimension or metric to sort the resulting report on, optionally prefixed with \"+\" to sort ascending or \"-\" to sort descending. If no prefix is specified, the column is sorted ascending.", "type": "string", "pattern": "(\\+|-)?[a-zA-Z_]+", "repeated": true, "location": "query"}, "startDate": {"location": "query", "description": "Start of the date range to report on in \"YYYY-MM-DD\" format, inclusive.", "type": "string", "required": true, "pattern": "\\d{4}-\\d{2}-\\d{2}|(today|startOfMonth|startOfYear)(([\\-\\+]\\d+[dwmy]){0,3}?)"}, "dimension": {"pattern": "[a-zA-Z_]+", "repeated": true, "location": "query", "description": "Dimensions to base the report on.", "type": "string"}, "filter": {"description": "Filters to be run on the report.", "type": "string", "pattern": "[a-zA-Z_]+(==|=@).+", "repeated": true, "location": "query"}, "startIndex": {"description": "Index of the first row of report data to return.", "format": "uint32", "maximum": "5000", "type": "integer", "minimum": "0", "location": "query"}}, "path": "reports", "id": "adsensehost.reports.generate", "description": "Generate an AdSense report based on the report request sent in the query parameters. Returns the result as JSON; to retrieve output in CSV format specify \"alt=csv\" as a query parameter."}}}, "urlchannels": {"methods": {"delete": {"path": "adclients/{adClientId}/urlchannels/{urlChannelId}", "id": "adsensehost.urlchannels.delete", "description": "Delete a URL channel from the host AdSense account.", "response": {"$ref": "UrlChannel"}, "parameterOrder": ["adClientId", "urlChannelId"], "httpMethod": "DELETE", "parameters": {"adClientId": {"type": "string", "required": true, "location": "path", "description": "Ad client from which to delete the URL channel."}, "urlChannelId": {"description": "URL channel to delete.", "type": "string", "required": true, "location": "path"}}, "scopes": ["https://www.googleapis.com/auth/adsensehost"]}, "insert": {"path": "adclients/{adClientId}/urlchannels", "id": "adsensehost.urlchannels.insert", "description": "Add a new URL channel to the host AdSense account.", "request": {"$ref": "UrlChannel"}, "response": {"$ref": "UrlChannel"}, "parameterOrder": ["adClientId"], "httpMethod": "POST", "scopes": ["https://www.googleapis.com/auth/adsensehost"], "parameters": {"adClientId": {"location": "path", "description": "Ad client to which the new URL channel will be added.", "type": "string", "required": true}}}, "list": {"httpMethod": "GET", "response": {"$ref": "UrlChannels"}, "parameterOrder": ["adClientId"], "parameters": {"pageToken": {"description": "A continuation token, used to page through URL channels. To retrieve the next page, set this parameter to the value of \"nextPageToken\" from the previous response.", "type": "string", "location": "query"}, "maxResults": {"format": "uint32", "maximum": "10000", "type": "integer", "minimum": "0", "location": "query", "description": "The maximum number of URL channels to include in the response, used for paging."}, "adClientId": {"description": "Ad client for which to list URL channels.", "type": "string", "required": true, "location": "path"}}, "scopes": ["https://www.googleapis.com/auth/adsensehost"], "id": "adsensehost.urlchannels.list", "path": "adclients/{adClientId}/urlchannels", "description": "List all host URL channels in the host AdSense account."}}}, "associationsessions": {"methods": {"start": {"description": "Create an association session for initiating an association with an AdSense user.", "response": {"$ref": "AssociationSession"}, "parameterOrder": ["productCode", "websiteUrl"], "httpMethod": "GET", "scopes": ["https://www.googleapis.com/auth/adsensehost"], "parameters": {"userLocale": {"type": "string", "location": "query", "description": "The preferred locale of the user."}, "websiteUrl": {"type": "string", "required": true, "location": "query", "description": "The URL of the user's hosted website."}, "callbackUrl": {"description": "The URL to redirect the user to once association is completed. It receives a token parameter that can then be used to retrieve the associated account.", "type": "string", "location": "query"}, "websiteLocale": {"location": "query", "description": "The locale of the user's hosted website.", "type": "string"}, "productCode": {"type": "string", "required": true, "repeated": true, "enumDescriptions": ["AdSense For Content", "AdSense For Games", "AdSense For Mobile Content - deprecated", "AdSense For Search - deprecated", "AdSense For Video"], "location": "query", "description": "Products to associate with the user.", "enum": ["AFC", "AFG", "AFMC", "AFS", "AFV"]}}, "path": "associationsessions/start", "id": "adsensehost.associationsessions.start"}, "verify": {"path": "associationsessions/verify", "id": "adsensehost.associationsessions.verify", "description": "Verify an association session after the association callback returns from AdSense signup.", "response": {"$ref": "AssociationSession"}, "parameterOrder": ["token"], "httpMethod": "GET", "scopes": ["https://www.googleapis.com/auth/adsensehost"], "parameters": {"token": {"location": "query", "description": "The token returned to the association callback URL.", "type": "string", "required": true}}}}}, "accounts": {"methods": {"get": {"description": "Get information about the selected associated AdSense account.", "response": {"$ref": "Account"}, "parameterOrder": ["accountId"], "httpMethod": "GET", "scopes": ["https://www.googleapis.com/auth/adsensehost"], "parameters": {"accountId": {"description": "Account to get information about.", "type": "string", "required": true, "location": "path"}}, "path": "accounts/{accountId}", "id": "adsensehost.accounts.get"}, "list": {"httpMethod": "GET", "response": {"$ref": "Accounts"}, "parameterOrder": ["filterAdClientId"], "parameters": {"filterAdClientId": {"type": "string", "required": true, "repeated": true, "location": "query", "description": "Ad clients to list accounts for."}}, "scopes": ["https://www.googleapis.com/auth/adsensehost"], "id": "adsensehost.accounts.list", "path": "accounts", "description": "List hosted accounts associated with this AdSense account by ad client id."}}, "resources": {"adclients": {"methods": {"get": {"response": {"$ref": "AdClient"}, "parameterOrder": ["accountId", "adClientId"], "httpMethod": "GET", "parameters": {"accountId": {"location": "path", "description": "Account which contains the ad client.", "type": "string", "required": true}, "adClientId": {"location": "path", "description": "Ad client to get.", "type": "string", "required": true}}, "scopes": ["https://www.googleapis.com/auth/adsensehost"], "path": "accounts/{accountId}/adclients/{adClientId}", "id": "adsensehost.accounts.adclients.get", "description": "Get information about one of the ad clients in the specified publisher's AdSense account."}, "list": {"id": "adsensehost.accounts.adclients.list", "path": "accounts/{accountId}/adclients", "description": "List all hosted ad clients in the specified hosted account.", "httpMethod": "GET", "response": {"$ref": "AdClients"}, "parameterOrder": ["accountId"], "parameters": {"pageToken": {"location": "query", "description": "A continuation token, used to page through ad clients. To retrieve the next page, set this parameter to the value of \"nextPageToken\" from the previous response.", "type": "string"}, "accountId": {"location": "path", "description": "Account for which to list ad clients.", "type": "string", "required": true}, "maxResults": {"format": "uint32", "maximum": "10000", "type": "integer", "minimum": "0", "location": "query", "description": "The maximum number of ad clients to include in the response, used for paging."}}, "scopes": ["https://www.googleapis.com/auth/adsensehost"]}}}, "adunits": {"methods": {"insert": {"path": "accounts/{accountId}/adclients/{adClientId}/adunits", "id": "adsensehost.accounts.adunits.insert", "description": "Insert the supplied ad unit into the specified publisher AdSense account.", "request": {"$ref": "AdUnit"}, "response": {"$ref": "AdUnit"}, "parameterOrder": ["accountId", "adClientId"], "httpMethod": "POST", "scopes": ["https://www.googleapis.com/auth/adsensehost"], "parameters": {"adClientId": {"type": "string", "required": true, "location": "path", "description": "Ad client into which to insert the ad unit."}, "accountId": {"location": "path", "description": "Account which will contain the ad unit.", "type": "string", "required": true}}}, "patch": {"response": {"$ref": "AdUnit"}, "parameterOrder": ["accountId", "adClientId", "adUnitId"], "httpMethod": "PATCH", "scopes": ["https://www.googleapis.com/auth/adsensehost"], "parameters": {"accountId": {"description": "Account which contains the ad client.", "type": "string", "required": true, "location": "path"}, "adClientId": {"description": "Ad client which contains the ad unit.", "type": "string", "required": true, "location": "path"}, "adUnitId": {"location": "query", "description": "Ad unit to get.", "type": "string", "required": true}}, "path": "accounts/{accountId}/adclients/{adClientId}/adunits", "id": "adsensehost.accounts.adunits.patch", "description": "Update the supplied ad unit in the specified publisher AdSense account. This method supports patch semantics.", "request": {"$ref": "AdUnit"}}, "get": {"id": "adsensehost.accounts.adunits.get", "path": "accounts/{accountId}/adclients/{adClientId}/adunits/{adUnitId}", "description": "Get the specified host ad unit in this AdSense account.", "httpMethod": "GET", "response": {"$ref": "AdUnit"}, "parameterOrder": ["accountId", "adClientId", "adUnitId"], "parameters": {"accountId": {"location": "path", "description": "Account which contains the ad unit.", "type": "string", "required": true}, "adClientId": {"type": "string", "required": true, "location": "path", "description": "Ad client for which to get ad unit."}, "adUnitId": {"location": "path", "description": "Ad unit to get.", "type": "string", "required": true}}, "scopes": ["https://www.googleapis.com/auth/adsensehost"]}, "getAdCode": {"description": "Get ad code for the specified ad unit, attaching the specified host custom channels.", "response": {"$ref": "AdCode"}, "parameterOrder": ["accountId", "adClientId", "adUnitId"], "httpMethod": "GET", "parameters": {"adClientId": {"location": "path", "description": "Ad client with contains the ad unit.", "type": "string", "required": true}, "adUnitId": {"location": "path", "description": "Ad unit to get the code for.", "type": "string", "required": true}, "hostCustomChannelId": {"location": "query", "description": "Host custom channel to attach to the ad code.", "type": "string", "repeated": true}, "accountId": {"description": "Account which contains the ad client.", "type": "string", "required": true, "location": "path"}}, "scopes": ["https://www.googleapis.com/auth/adsensehost"], "path": "accounts/{accountId}/adclients/{adClientId}/adunits/{adUnitId}/adcode", "id": "adsensehost.accounts.adunits.getAdCode"}, "update": {"request": {"$ref": "AdUnit"}, "description": "Update the supplied ad unit in the specified publisher AdSense account.", "response": {"$ref": "AdUnit"}, "parameterOrder": ["accountId", "adClientId"], "httpMethod": "PUT", "parameters": {"accountId": {"description": "Account which contains the ad client.", "type": "string", "required": true, "location": "path"}, "adClientId": {"location": "path", "description": "Ad client which contains the ad unit.", "type": "string", "required": true}}, "scopes": ["https://www.googleapis.com/auth/adsensehost"], "path": "accounts/{accountId}/adclients/{adClientId}/adunits", "id": "adsensehost.accounts.adunits.update"}, "delete": {"httpMethod": "DELETE", "response": {"$ref": "AdUnit"}, "parameterOrder": ["accountId", "adClientId", "adUnitId"], "parameters": {"adClientId": {"type": "string", "required": true, "location": "path", "description": "Ad client for which to get ad unit."}, "adUnitId": {"description": "Ad unit to delete.", "type": "string", "required": true, "location": "path"}, "accountId": {"location": "path", "description": "Account which contains the ad unit.", "type": "string", "required": true}}, "scopes": ["https://www.googleapis.com/auth/adsensehost"], "id": "adsensehost.accounts.adunits.delete", "path": "accounts/{accountId}/adclients/{adClientId}/adunits/{adUnitId}", "description": "Delete the specified ad unit from the specified publisher AdSense account."}, "list": {"httpMethod": "GET", "response": {"$ref": "AdUnits"}, "parameterOrder": ["accountId", "adClientId"], "parameters": {"includeInactive": {"location": "query", "description": "Whether to include inactive ad units. Default: true.", "type": "boolean"}, "pageToken": {"location": "query", "description": "A continuation token, used to page through ad units. To retrieve the next page, set this parameter to the value of \"nextPageToken\" from the previous response.", "type": "string"}, "accountId": {"description": "Account which contains the ad client.", "type": "string", "required": true, "location": "path"}, "maxResults": {"minimum": "0", "location": "query", "description": "The maximum number of ad units to include in the response, used for paging.", "format": "uint32", "maximum": "10000", "type": "integer"}, "adClientId": {"type": "string", "required": true, "location": "path", "description": "Ad client for which to list ad units."}}, "scopes": ["https://www.googleapis.com/auth/adsensehost"], "id": "adsensehost.accounts.adunits.list", "path": "accounts/{accountId}/adclients/{adClientId}/adunits", "description": "List all ad units in the specified publisher's AdSense account."}}}, "reports": {"methods": {"generate": {"description": "Generate an AdSense report based on the report request sent in the query parameters. Returns the result as JSON; to retrieve output in CSV format specify \"alt=csv\" as a query parameter.", "response": {"$ref": "Report"}, "parameterOrder": ["accountId", "startDate", "endDate"], "httpMethod": "GET", "scopes": ["https://www.googleapis.com/auth/adsensehost"], "parameters": {"maxResults": {"maximum": "50000", "format": "uint32", "type": "integer", "minimum": "0", "location": "query", "description": "The maximum number of rows of report data to return."}, "sort": {"description": "The name of a dimension or metric to sort the resulting report on, optionally prefixed with \"+\" to sort ascending or \"-\" to sort descending. If no prefix is specified, the column is sorted ascending.", "type": "string", "pattern": "(\\+|-)?[a-zA-Z_]+", "repeated": true, "location": "query"}, "startDate": {"type": "string", "required": true, "pattern": "\\d{4}-\\d{2}-\\d{2}|(today|startOfMonth|startOfYear)(([\\-\\+]\\d+[dwmy]){0,3}?)", "location": "query", "description": "Start of the date range to report on in \"YYYY-MM-DD\" format, inclusive."}, "accountId": {"description": "Hosted account upon which to report.", "type": "string", "required": true, "location": "path"}, "dimension": {"description": "Dimensions to base the report on.", "type": "string", "pattern": "[a-zA-Z_]+", "repeated": true, "location": "query"}, "filter": {"description": "Filters to be run on the report.", "type": "string", "pattern": "[a-zA-Z_]+(==|=@).+", "repeated": true, "location": "query"}, "startIndex": {"format": "uint32", "maximum": "5000", "type": "integer", "minimum": "0", "location": "query", "description": "Index of the first row of report data to return."}, "locale": {"description": "Optional locale to use for translating report output to a local language. Defaults to \"en_US\" if not specified.", "type": "string", "pattern": "[a-zA-Z_]+", "location": "query"}, "endDate": {"location": "query", "description": "End of the date range to report on in \"YYYY-MM-DD\" format, inclusive.", "type": "string", "required": true, "pattern": "\\d{4}-\\d{2}-\\d{2}|(today|startOfMonth|startOfYear)(([\\-\\+]\\d+[dwmy]){0,3}?)"}, "metric": {"location": "query", "description": "Numeric columns to include in the report.", "type": "string", "pattern": "[a-zA-Z_]+", "repeated": true}}, "path": "accounts/{accountId}/reports", "id": "adsensehost.accounts.reports.generate"}}}}}, "customchannels": {"methods": {"insert": {"response": {"$ref": "CustomChannel"}, "parameterOrder": ["adClientId"], "httpMethod": "POST", "parameters": {"adClientId": {"location": "path", "description": "Ad client to which the new custom channel will be added.", "type": "string", "required": true}}, "scopes": ["https://www.googleapis.com/auth/adsensehost"], "path": "adclients/{adClientId}/customchannels", "id": "adsensehost.customchannels.insert", "request": {"$ref": "CustomChannel"}, "description": "Add a new custom channel to the host AdSense account."}, "patch": {"id": "adsensehost.customchannels.patch", "path": "adclients/{adClientId}/customchannels", "request": {"$ref": "CustomChannel"}, "description": "Update a custom channel in the host AdSense account. This method supports patch semantics.", "httpMethod": "PATCH", "parameterOrder": ["adClientId", "customChannelId"], "response": {"$ref": "CustomChannel"}, "parameters": {"customChannelId": {"description": "Custom channel to get.", "type": "string", "required": true, "location": "query"}, "adClientId": {"description": "Ad client in which the custom channel will be updated.", "type": "string", "required": true, "location": "path"}}, "scopes": ["https://www.googleapis.com/auth/adsensehost"]}, "get": {"id": "adsensehost.customchannels.get", "path": "adclients/{adClientId}/customchannels/{customChannelId}", "description": "Get a specific custom channel from the host AdSense account.", "httpMethod": "GET", "response": {"$ref": "CustomChannel"}, "parameterOrder": ["adClientId", "customChannelId"], "parameters": {"adClientId": {"location": "path", "description": "Ad client from which to get the custom channel.", "type": "string", "required": true}, "customChannelId": {"description": "Custom channel to get.", "type": "string", "required": true, "location": "path"}}, "scopes": ["https://www.googleapis.com/auth/adsensehost"]}, "update": {"response": {"$ref": "CustomChannel"}, "parameterOrder": ["adClientId"], "httpMethod": "PUT", "scopes": ["https://www.googleapis.com/auth/adsensehost"], "parameters": {"adClientId": {"description": "Ad client in which the custom channel will be updated.", "type": "string", "required": true, "location": "path"}}, "path": "adclients/{adClientId}/customchannels", "id": "adsensehost.customchannels.update", "description": "Update a custom channel in the host AdSense account.", "request": {"$ref": "CustomChannel"}}, "delete": {"description": "Delete a specific custom channel from the host AdSense account.", "response": {"$ref": "CustomChannel"}, "parameterOrder": ["adClientId", "customChannelId"], "httpMethod": "DELETE", "parameters": {"adClientId": {"description": "Ad client from which to delete the custom channel.", "type": "string", "required": true, "location": "path"}, "customChannelId": {"location": "path", "description": "Custom channel to delete.", "type": "string", "required": true}}, "scopes": ["https://www.googleapis.com/auth/adsensehost"], "path": "adclients/{adClientId}/customchannels/{customChannelId}", "id": "adsensehost.customchannels.delete"}, "list": {"description": "List all host custom channels in this AdSense account.", "response": {"$ref": "CustomChannels"}, "parameterOrder": ["adClientId"], "httpMethod": "GET", "parameters": {"pageToken": {"description": "A continuation token, used to page through custom channels. To retrieve the next page, set this parameter to the value of \"nextPageToken\" from the previous response.", "type": "string", "location": "query"}, "maxResults": {"type": "integer", "minimum": "0", "location": "query", "description": "The maximum number of custom channels to include in the response, used for paging.", "format": "uint32", "maximum": "10000"}, "adClientId": {"description": "Ad client for which to list custom channels.", "type": "string", "required": true, "location": "path"}}, "scopes": ["https://www.googleapis.com/auth/adsensehost"], "path": "adclients/{adClientId}/customchannels", "id": "adsensehost.customchannels.list"}}}}, "parameters": {"prettyPrint": {"location": "query", "description": "Returns response with indentations and line breaks.", "type": "boolean", "default": "true"}, "quotaUser": {"location": "query", "description": "An opaque string that represents a user for quota purposes. Must not exceed 40 characters.", "type": "string"}, "fields": {"location": "query", "description": "Selector specifying which fields to include in a partial response.", "type": "string"}, "oauth_token": {"type": "string", "location": "query", "description": "OAuth 2.0 token for the current user."}, "alt": {"enumDescriptions": ["Responses with Content-Type of text/csv", "Responses with Content-Type of application/json"], "location": "query", "description": "Data format for the response.", "default": "json", "enum": ["csv", "json"], "type": "string"}, "userIp": {"description": "Deprecated. Please use quotaUser instead.", "type": "string", "location": "query"}, "key": {"location": "query", "description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "type": "string"}}, "version": "v4.1", "baseUrl": "https://www.googleapis.com/adsensehost/v4.1/", "kind": "discovery#restDescription", "description": "Generates performance reports, generates ad codes, and provides publisher management capabilities for AdSense Hosts.", "servicePath": "adsensehost/v4.1/", "basePath": "/adsensehost/v4.1/", "id": "adsensehost:v4.1", "documentationLink": "https://developers.google.com/adsense/host/", "revision": "********", "discoveryVersion": "v1", "schemas": {"Accounts": {"type": "object", "properties": {"items": {"description": "The accounts returned in this list response.", "type": "array", "items": {"$ref": "Account"}}, "etag": {"description": "ETag of this response for caching purposes.", "type": "string"}, "kind": {"description": "Kind of list this is, in this case adsensehost#accounts.", "type": "string", "default": "adsensehost#accounts"}}, "id": "Accounts"}, "AdClient": {"id": "AdClient", "type": "object", "properties": {"kind": {"description": "Kind of resource this is, in this case adsensehost#adClient.", "type": "string", "default": "adsensehost#adClient"}, "arcOptIn": {"description": "Whether this ad client is opted in to ARC.", "type": "boolean"}, "productCode": {"description": "This ad client's product code, which corresponds to the PRODUCT_CODE report dimension.", "type": "string"}, "id": {"description": "Unique identifier of this ad client.", "type": "string"}, "supportsReporting": {"description": "Whether this ad client supports being reported on.", "type": "boolean"}}}, "Report": {"type": "object", "properties": {"headers": {"description": "The header information of the columns requested in the report. This is a list of headers; one for each dimension in the request, followed by one for each metric in the request.", "type": "array", "items": {"type": "object", "properties": {"name": {"description": "The name of the header.", "type": "string"}, "type": {"description": "The type of the header; one of DIM<PERSON><PERSON><PERSON>, METRIC_TALLY, <PERSON>TRIC_RATIO, or METRIC_CURRENCY.", "type": "string"}, "currency": {"description": "The currency of this column. Only present if the header type is METRIC_CURRENCY.", "type": "string"}}}}, "rows": {"description": "The output rows of the report. Each row is a list of cells; one for each dimension in the request, followed by one for each metric in the request. The dimension cells contain strings, and the metric cells contain numbers.", "type": "array", "items": {"type": "array", "items": {"type": "string"}}}, "kind": {"description": "Kind this is, in this case adsensehost#report.", "type": "string", "default": "adsensehost#report"}, "averages": {"description": "The averages of the report. This is the same length as any other row in the report; cells corresponding to dimension columns are empty.", "type": "array", "items": {"type": "string"}}, "totalMatchedRows": {"description": "The total number of rows matched by the report request. Fewer rows may be returned in the response due to being limited by the row count requested or the report row limit.", "format": "int64", "type": "string"}, "warnings": {"description": "Any warnings associated with generation of the report.", "type": "array", "items": {"type": "string"}}, "totals": {"description": "The totals of the report. This is the same length as any other row in the report; cells corresponding to dimension columns are empty.", "type": "array", "items": {"type": "string"}}}, "id": "Report"}, "CustomChannels": {"type": "object", "properties": {"etag": {"description": "ETag of this response for caching purposes.", "type": "string"}, "nextPageToken": {"description": "Continuation token used to page through custom channels. To retrieve the next page of results, set the next request's \"pageToken\" value to this.", "type": "string"}, "kind": {"description": "Kind of list this is, in this case adsensehost#customChannels.", "type": "string", "default": "adsensehost#customChannels"}, "items": {"description": "The custom channels returned in this list response.", "type": "array", "items": {"$ref": "CustomChannel"}}}, "id": "CustomChannels"}, "CustomChannel": {"type": "object", "properties": {"name": {"description": "Name of this custom channel.", "type": "string"}, "kind": {"description": "Kind of resource this is, in this case adsensehost#customChannel.", "type": "string", "default": "adsensehost#customChannel"}, "code": {"description": "Code of this custom channel, not necessarily unique across ad clients.", "type": "string"}, "id": {"type": "string", "description": "Unique identifier of this custom channel. This should be considered an opaque identifier; it is not safe to rely on it being in any particular format."}}, "id": "CustomChannel"}, "AdUnits": {"type": "object", "properties": {"nextPageToken": {"description": "Continuation token used to page through ad units. To retrieve the next page of results, set the next request's \"pageToken\" value to this.", "type": "string"}, "kind": {"description": "Kind of list this is, in this case adsensehost#adUnits.", "type": "string", "default": "adsensehost#adUnits"}, "items": {"description": "The ad units returned in this list response.", "type": "array", "items": {"$ref": "AdUnit"}}, "etag": {"description": "ETag of this response for caching purposes.", "type": "string"}}, "id": "AdUnits"}, "UrlChannels": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "UrlChannel"}, "description": "The URL channels returned in this list response."}, "etag": {"description": "ETag of this response for caching purposes.", "type": "string"}, "nextPageToken": {"description": "Continuation token used to page through URL channels. To retrieve the next page of results, set the next request's \"pageToken\" value to this.", "type": "string"}, "kind": {"description": "Kind of list this is, in this case adsensehost#urlChannels.", "type": "string", "default": "adsensehost#urlChannels"}}, "id": "UrlChannels"}, "Account": {"type": "object", "properties": {"id": {"description": "Unique identifier of this account.", "type": "string"}, "status": {"description": "Approval status of this account. One of: PENDING, APPROVED, DISABLED.", "type": "string"}, "name": {"description": "Name of this account.", "type": "string"}, "kind": {"type": "string", "default": "adsensehost#account", "description": "Kind of resource this is, in this case adsensehost#account."}}, "id": "Account"}, "AdUnit": {"type": "object", "properties": {"contentAdsSettings": {"description": "Settings specific to content ads (AFC) and highend mobile content ads (AFMC - deprecated).", "type": "object", "properties": {"size": {"description": "Size of this ad unit. Size values are in the form SIZE_{width}_{height}.", "type": "string"}, "type": {"type": "string", "description": "Type of this ad unit. Possible values are TEXT, TEXT_IMAGE, IMAGE and LINK."}, "backupOption": {"description": "The backup option to be used in instances where no ad is available.", "type": "object", "properties": {"color": {"description": "Color to use when type is set to COLOR. These are represented as six hexadecimal characters, similar to HTML color codes, but without the leading hash.", "type": "string"}, "type": {"description": "Type of the backup option. Possible values are BLANK, COLOR and URL.", "type": "string"}, "url": {"description": "URL to use when type is set to URL.", "type": "string"}}}}}, "status": {"description": "Status of this ad unit. Possible values are:\nNEW: Indicates that the ad unit was created within the last seven days and does not yet have any activity associated with it.\n\nACTIVE: Indicates that there has been activity on this ad unit in the last seven days.\n\nINACTIVE: Indicates that there has been no activity on this ad unit in the last seven days.", "type": "string"}, "name": {"description": "Name of this ad unit.", "type": "string"}, "mobileContentAdsSettings": {"description": "Settings specific to WAP mobile content ads (AFMC - deprecated).", "type": "object", "properties": {"markupLanguage": {"description": "The markup language to use for this ad unit.", "type": "string"}, "size": {"description": "Size of this ad unit.", "type": "string"}, "scriptingLanguage": {"description": "The scripting language to use for this ad unit.", "type": "string"}, "type": {"description": "Type of this ad unit.", "type": "string"}}}, "id": {"description": "Unique identifier of this ad unit. This should be considered an opaque identifier; it is not safe to rely on it being in any particular format.", "type": "string"}, "customStyle": {"$ref": "AdStyle", "description": "Custom style information specific to this ad unit."}, "kind": {"description": "Kind of resource this is, in this case adsensehost#adUnit.", "type": "string", "default": "adsensehost#adUnit"}, "code": {"description": "Identity code of this ad unit, not necessarily unique across ad clients.", "type": "string"}}, "id": "AdUnit"}, "AdStyle": {"type": "object", "properties": {"kind": {"description": "Kind this is, in this case adsensehost#adStyle.", "type": "string", "default": "adsensehost#adStyle"}, "font": {"type": "object", "properties": {"family": {"type": "string", "description": "The family of the font. Possible values are: ACCOUNT_DEFAULT_FAMILY, ADSENSE_DEFAULT_FAMILY, ARIAL, TIMES and VERDANA."}, "size": {"description": "The size of the font. Possible values are: ACCOUNT_DEFAULT_SIZE, ADSENSE_DEFAULT_SIZE, SMALL, MEDIUM and LARGE.", "type": "string"}}, "description": "The font which is included in the style."}, "colors": {"description": "The colors included in the style. These are represented as six hexadecimal characters, similar to HTML color codes, but without the leading hash.", "type": "object", "properties": {"border": {"description": "The color of the ad border.", "type": "string"}, "text": {"type": "string", "description": "The color of the ad text."}, "background": {"description": "The color of the ad background.", "type": "string"}, "url": {"description": "The color of the ad url.", "type": "string"}, "title": {"description": "The color of the ad title.", "type": "string"}}}, "corners": {"description": "The style of the corners in the ad (deprecated: never populated, ignored).", "type": "string"}}, "id": "AdStyle"}, "AssociationSession": {"type": "object", "properties": {"websiteLocale": {"description": "The locale of the user's hosted website.", "type": "string"}, "accountId": {"description": "Hosted account id of the associated publisher after association. Present if status is ACCEPTED.", "type": "string"}, "id": {"description": "Unique identifier of this association session.", "type": "string"}, "redirectUrl": {"description": "Redirect URL of this association session. Used to redirect users into the AdSense association flow.", "type": "string"}, "kind": {"description": "Kind of resource this is, in this case adsensehost#associationSession.", "type": "string", "default": "adsensehost#associationSession"}, "productCodes": {"description": "The products to associate with the user. Options: AFC, AFG, AFV, AFS (deprecated), AFMC (deprecated)", "type": "array", "items": {"type": "string"}}, "userLocale": {"description": "The preferred locale of the user themselves when going through the AdSense association flow.", "type": "string"}, "websiteUrl": {"description": "The URL of the user's hosted website.", "type": "string"}, "status": {"description": "Status of the completed association, available once the association callback token has been verified. One of ACCEPTED, REJECTED, or ERROR.", "type": "string"}}, "id": "AssociationSession"}, "AdCode": {"type": "object", "properties": {"kind": {"description": "Kind this is, in this case adsensehost#adCode.", "type": "string", "default": "adsensehost#adCode"}, "adCode": {"type": "string", "description": "The ad code snippet."}}, "id": "AdCode"}, "UrlChannel": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier of this URL channel. This should be considered an opaque identifier; it is not safe to rely on it being in any particular format."}, "urlPattern": {"description": "URL Pattern of this URL channel. Does not include \"http://\" or \"https://\". Example: www.example.com/home", "type": "string"}, "kind": {"description": "Kind of resource this is, in this case adsensehost#urlChannel.", "type": "string", "default": "adsensehost#urlChannel"}}, "id": "UrlChannel"}, "AdClients": {"id": "AdClients", "type": "object", "properties": {"kind": {"description": "Kind of list this is, in this case adsensehost#adClients.", "type": "string", "default": "adsensehost#adClients"}, "items": {"description": "The ad clients returned in this list response.", "type": "array", "items": {"$ref": "AdClient"}}, "etag": {"description": "ETag of this response for caching purposes.", "type": "string"}, "nextPageToken": {"description": "Continuation token used to page through ad clients. To retrieve the next page of results, set the next request's \"pageToken\" value to this.", "type": "string"}}}}, "icons": {"x32": "https://www.google.com/images/icons/product/adsense-32.png", "x16": "https://www.google.com/images/icons/product/adsense-16.png"}, "protocol": "rest"}