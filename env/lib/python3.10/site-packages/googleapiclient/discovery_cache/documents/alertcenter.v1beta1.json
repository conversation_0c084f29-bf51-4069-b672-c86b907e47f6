{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/apps.alerts": {"description": "See and delete your domain's G Suite alerts, and send alert feedback"}}}}, "basePath": "", "baseUrl": "https://alertcenter.googleapis.com/", "batchPath": "batch", "canonicalName": "AlertCenter", "description": "Manages alerts on issues affecting your domain.", "discoveryVersion": "v1", "documentationLink": "https://developers.google.com/admin-sdk/alertcenter/", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "alertcenter:v1beta1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://alertcenter.mtls.googleapis.com/", "name": "alertcenter", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"alerts": {"methods": {"batchDelete": {"description": "Performs batch delete operation on alerts.", "flatPath": "v1beta1/alerts:batchDelete", "httpMethod": "POST", "id": "alertcenter.alerts.batchDelete", "parameterOrder": [], "parameters": {}, "path": "v1beta1/alerts:batchDelete", "request": {"$ref": "GoogleAppsAlertcenterV1beta1BatchDeleteAlertsRequest"}, "response": {"$ref": "GoogleAppsAlertcenterV1beta1BatchDeleteAlertsResponse"}, "scopes": ["https://www.googleapis.com/auth/apps.alerts"]}, "batchUndelete": {"description": "Performs batch undelete operation on alerts.", "flatPath": "v1beta1/alerts:batchUndelete", "httpMethod": "POST", "id": "alertcenter.alerts.batchUndelete", "parameterOrder": [], "parameters": {}, "path": "v1beta1/alerts:batchUndelete", "request": {"$ref": "GoogleAppsAlertcenterV1beta1BatchUndeleteAlertsRequest"}, "response": {"$ref": "GoogleAppsAlertcenterV1beta1BatchUndeleteAlertsResponse"}, "scopes": ["https://www.googleapis.com/auth/apps.alerts"]}, "delete": {"description": "Marks the specified alert for deletion. An alert that has been marked for deletion is removed from Alert Center after 30 days. Marking an alert for deletion has no effect on an alert which has already been marked for deletion. Attempting to mark a nonexistent alert for deletion results in a `NOT_FOUND` error.", "flatPath": "v1beta1/alerts/{alertId}", "httpMethod": "DELETE", "id": "alertcenter.alerts.delete", "parameterOrder": ["alertId"], "parameters": {"alertId": {"description": "Required. The identifier of the alert to delete.", "location": "path", "required": true, "type": "string"}, "customerId": {"description": "Optional. The unique identifier of the Google Workspace organization account of the customer the alert is associated with. Inferred from the caller identity if not provided.", "location": "query", "type": "string"}}, "path": "v1beta1/alerts/{alertId}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/apps.alerts"]}, "get": {"description": "Gets the specified alert. Attempting to get a nonexistent alert returns `NOT_FOUND` error.", "flatPath": "v1beta1/alerts/{alertId}", "httpMethod": "GET", "id": "alertcenter.alerts.get", "parameterOrder": ["alertId"], "parameters": {"alertId": {"description": "Required. The identifier of the alert to retrieve.", "location": "path", "required": true, "type": "string"}, "customerId": {"description": "Optional. The unique identifier of the Google Workspace organization account of the customer the alert is associated with. Inferred from the caller identity if not provided.", "location": "query", "type": "string"}}, "path": "v1beta1/alerts/{alertId}", "response": {"$ref": "GoogleAppsAlertcenterV1beta1Alert"}, "scopes": ["https://www.googleapis.com/auth/apps.alerts"]}, "getMetadata": {"description": "Returns the metadata of an alert. Attempting to get metadata for a non-existent alert returns `NOT_FOUND` error.", "flatPath": "v1beta1/alerts/{alertId}/metadata", "httpMethod": "GET", "id": "alertcenter.alerts.getMetadata", "parameterOrder": ["alertId"], "parameters": {"alertId": {"description": "Required. The identifier of the alert this metadata belongs to.", "location": "path", "required": true, "type": "string"}, "customerId": {"description": "Optional. The unique identifier of the Google Workspace organization account of the customer the alert metadata is associated with. Inferred from the caller identity if not provided.", "location": "query", "type": "string"}}, "path": "v1beta1/alerts/{alertId}/metadata", "response": {"$ref": "GoogleAppsAlertcenterV1beta1AlertMetadata"}, "scopes": ["https://www.googleapis.com/auth/apps.alerts"]}, "list": {"description": "Lists the alerts.", "flatPath": "v1beta1/alerts", "httpMethod": "GET", "id": "alertcenter.alerts.list", "parameterOrder": [], "parameters": {"customerId": {"description": "Optional. The unique identifier of the Google Workspace organization account of the customer the alerts are associated with. Inferred from the caller identity if not provided.", "location": "query", "type": "string"}, "filter": {"description": "Optional. A query string for filtering alert results. For more details, see [Query filters](/admin-sdk/alertcenter/guides/query-filters) and [Supported query filter fields](/admin-sdk/alertcenter/reference/filter-fields#alerts.list).", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. The sort order of the list results. If not specified results may be returned in arbitrary order. You can sort the results in descending order based on the creation timestamp using `order_by=\"create_time desc\"`. Currently, supported sorting are `create_time asc`, `create_time desc`, `update_time desc`", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The requested page size. Server may return fewer items than requested. If unspecified, server picks an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A token identifying a page of results the server should return. If empty, a new iteration is started. To continue an iteration, pass in the value from the previous ListAlertsResponse's next_page_token field.", "location": "query", "type": "string"}}, "path": "v1beta1/alerts", "response": {"$ref": "GoogleAppsAlertcenterV1beta1ListAlertsResponse"}, "scopes": ["https://www.googleapis.com/auth/apps.alerts"]}, "undelete": {"description": "Restores, or \"undeletes\", an alert that was marked for deletion within the past 30 days. Attempting to undelete an alert which was marked for deletion over 30 days ago (which has been removed from the Alert Center database) or a nonexistent alert returns a `NOT_FOUND` error. Attempting to undelete an alert which has not been marked for deletion has no effect.", "flatPath": "v1beta1/alerts/{alertId}:undelete", "httpMethod": "POST", "id": "alertcenter.alerts.undelete", "parameterOrder": ["alertId"], "parameters": {"alertId": {"description": "Required. The identifier of the alert to undelete.", "location": "path", "required": true, "type": "string"}}, "path": "v1beta1/alerts/{alertId}:undelete", "request": {"$ref": "GoogleAppsAlertcenterV1beta1UndeleteAlertRequest"}, "response": {"$ref": "GoogleAppsAlertcenterV1beta1Alert"}, "scopes": ["https://www.googleapis.com/auth/apps.alerts"]}}, "resources": {"feedback": {"methods": {"create": {"description": "Creates new feedback for an alert. Attempting to create a feedback for a non-existent alert returns `NOT_FOUND` error. Attempting to create a feedback for an alert that is marked for deletion returns `FAILED_PRECONDITION' error.", "flatPath": "v1beta1/alerts/{alertId}/feedback", "httpMethod": "POST", "id": "alertcenter.alerts.feedback.create", "parameterOrder": ["alertId"], "parameters": {"alertId": {"description": "Required. The identifier of the alert this feedback belongs to.", "location": "path", "required": true, "type": "string"}, "customerId": {"description": "Optional. The unique identifier of the Google Workspace organization account of the customer the alert is associated with. Inferred from the caller identity if not provided.", "location": "query", "type": "string"}}, "path": "v1beta1/alerts/{alertId}/feedback", "request": {"$ref": "GoogleAppsAlertcenterV1beta1AlertFeedback"}, "response": {"$ref": "GoogleAppsAlertcenterV1beta1AlertFeedback"}, "scopes": ["https://www.googleapis.com/auth/apps.alerts"]}, "list": {"description": "Lists all the feedback for an alert. Attempting to list feedbacks for a non-existent alert returns `NOT_FOUND` error.", "flatPath": "v1beta1/alerts/{alertId}/feedback", "httpMethod": "GET", "id": "alertcenter.alerts.feedback.list", "parameterOrder": ["alertId"], "parameters": {"alertId": {"description": "Required. The alert identifier. The \"-\" wildcard could be used to represent all alerts.", "location": "path", "required": true, "type": "string"}, "customerId": {"description": "Optional. The unique identifier of the Google Workspace organization account of the customer the alert feedback are associated with. Inferred from the caller identity if not provided.", "location": "query", "type": "string"}, "filter": {"description": "Optional. A query string for filtering alert feedback results. For more details, see [Query filters](/admin-sdk/alertcenter/guides/query-filters) and [Supported query filter fields](/admin-sdk/alertcenter/reference/filter-fields#alerts.feedback.list).", "location": "query", "type": "string"}}, "path": "v1beta1/alerts/{alertId}/feedback", "response": {"$ref": "GoogleAppsAlertcenterV1beta1ListAlertFeedbackResponse"}, "scopes": ["https://www.googleapis.com/auth/apps.alerts"]}}}}}, "v1beta1": {"methods": {"getSettings": {"description": "Returns customer-level settings.", "flatPath": "v1beta1/settings", "httpMethod": "GET", "id": "alertcenter.getSettings", "parameterOrder": [], "parameters": {"customerId": {"description": "Optional. The unique identifier of the Google Workspace organization account of the customer the alert settings are associated with. Inferred from the caller identity if not provided.", "location": "query", "type": "string"}}, "path": "v1beta1/settings", "response": {"$ref": "GoogleAppsAlertcenterV1beta1Settings"}, "scopes": ["https://www.googleapis.com/auth/apps.alerts"]}, "updateSettings": {"description": "Updates the customer-level settings.", "flatPath": "v1beta1/settings", "httpMethod": "PATCH", "id": "alertcenter.updateSettings", "parameterOrder": [], "parameters": {"customerId": {"description": "Optional. The unique identifier of the Google Workspace organization account of the customer the alert settings are associated with. Inferred from the caller identity if not provided.", "location": "query", "type": "string"}}, "path": "v1beta1/settings", "request": {"$ref": "GoogleAppsAlertcenterV1beta1Settings"}, "response": {"$ref": "GoogleAppsAlertcenterV1beta1Settings"}, "scopes": ["https://www.googleapis.com/auth/apps.alerts"]}}}}, "revision": "********", "rootUrl": "https://alertcenter.googleapis.com/", "schemas": {"GoogleAppsAlertcenterTypeAccountWarning": {"description": "Alerts for user account warning events.", "id": "GoogleAppsAlertcenterTypeAccountWarning", "properties": {"email": {"description": "Required. The email of the user that this event belongs to.", "type": "string"}, "loginDetails": {"$ref": "GoogleAppsAlertcenterTypeAccountWarningLoginDetails", "description": "Optional. Details of the login action associated with the warning event. This is only available for: * Suspicious login * Suspicious login (less secure app) * Suspicious programmatic login * User suspended (suspicious activity)"}}, "type": "object"}, "GoogleAppsAlertcenterTypeAccountWarningLoginDetails": {"description": "The details of the login action.", "id": "GoogleAppsAlertcenterTypeAccountWarningLoginDetails", "properties": {"ipAddress": {"description": "Optional. The human-readable IP address (for example, `***********`) that is associated with the warning event.", "type": "string"}, "loginTime": {"description": "Optional. The successful login time that is associated with the warning event. This isn't present for blocked login attempts.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleAppsAlertcenterTypeActivityRule": {"description": "Alerts from Google Workspace Security Center rules service configured by an admin.", "id": "GoogleAppsAlertcenterTypeActivityRule", "properties": {"actionNames": {"description": "List of action names associated with the rule threshold.", "items": {"type": "string"}, "type": "array"}, "createTime": {"description": "Rule create timestamp.", "format": "google-datetime", "type": "string"}, "description": {"description": "Description of the rule.", "type": "string"}, "displayName": {"description": "Alert display name.", "type": "string"}, "name": {"description": "Rule name.", "type": "string"}, "query": {"description": "Query that is used to get the data from the associated source.", "type": "string"}, "supersededAlerts": {"description": "List of alert IDs superseded by this alert. It is used to indicate that this alert is essentially extension of superseded alerts and we found the relationship after creating these alerts.", "items": {"type": "string"}, "type": "array"}, "supersedingAlert": {"description": "Alert ID superseding this alert. It is used to indicate that superseding alert is essentially extension of this alert and we found the relationship after creating both alerts.", "type": "string"}, "threshold": {"description": "Alert threshold is for example “COUNT > 5”.", "type": "string"}, "triggerSource": {"description": "The trigger sources for this rule. * GMAIL_EVENTS * DEVICE_EVENTS * USER_EVENTS", "type": "string"}, "updateTime": {"description": "The timestamp of the last update to the rule.", "format": "google-datetime", "type": "string"}, "windowSize": {"description": "Rule window size. Possible values are 1 hour or 24 hours.", "format": "google-duration", "type": "string"}}, "type": "object"}, "GoogleAppsAlertcenterTypeAppMakerSqlSetupNotification": {"description": "Alerts from App Maker to notify admins to set up default SQL instance.", "id": "GoogleAppsAlertcenterTypeAppMakerSqlSetupNotification", "properties": {"requestInfo": {"description": "List of applications with requests for default SQL set up.", "items": {"$ref": "GoogleAppsAlertcenterTypeAppMakerSqlSetupNotificationRequestInfo"}, "type": "array"}}, "type": "object"}, "GoogleAppsAlertcenterTypeAppMakerSqlSetupNotificationRequestInfo": {"description": "Requests for one application that needs default SQL setup.", "id": "GoogleAppsAlertcenterTypeAppMakerSqlSetupNotificationRequestInfo", "properties": {"appDeveloperEmail": {"description": "List of app developers who triggered notifications for above application.", "items": {"type": "string"}, "type": "array"}, "appKey": {"description": "Required. The application that requires the SQL setup.", "type": "string"}, "numberOfRequests": {"description": "Required. Number of requests sent for this application to set up default SQL instance.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleAppsAlertcenterTypeAttachment": {"description": "Attachment with application-specific information about an alert.", "id": "GoogleAppsAlertcenterTypeAttachment", "properties": {"csv": {"$ref": "GoogleAppsAlertcenterTypeAttachmentCsv", "description": "A CSV file attachment."}}, "type": "object"}, "GoogleAppsAlertcenterTypeAttachmentCsv": {"description": "A representation of a CSV file attachment, as a list of column headers and a list of data rows.", "id": "GoogleAppsAlertcenterTypeAttachmentCsv", "properties": {"dataRows": {"description": "The list of data rows in a CSV file, as string arrays rather than as a single comma-separated string.", "items": {"$ref": "GoogleAppsAlertcenterTypeAttachmentCsvCsvRow"}, "type": "array"}, "headers": {"description": "The list of headers for data columns in a CSV file.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleAppsAlertcenterTypeAttachmentCsvCsvRow": {"description": "A representation of a single data row in a CSV file.", "id": "GoogleAppsAlertcenterTypeAttachmentCsvCsvRow", "properties": {"entries": {"description": "The data entries in a CSV file row, as a string array rather than a single comma-separated string.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleAppsAlertcenterTypeBadWhitelist": {"description": "<PERSON><PERSON> for setting the domain or IP that malicious email comes from as whitelisted domain or IP in Gmail advanced settings.", "id": "GoogleAppsAlertcenterTypeBadWhitelist", "properties": {"domainId": {"$ref": "GoogleAppsAlertcenterTypeDomainId", "description": "The domain ID."}, "maliciousEntity": {"$ref": "GoogleAppsAlertcenterTypeMaliciousEntity", "description": "The entity whose actions triggered a Gmail phishing alert."}, "messages": {"description": "The list of messages contained by this alert.", "items": {"$ref": "GoogleAppsAlertcenterTypeGmailMessageInfo"}, "type": "array"}, "sourceIp": {"description": "The source IP address of the malicious email, for example, `127.0.0.1`.", "type": "string"}}, "type": "object"}, "GoogleAppsAlertcenterTypeDeviceCompromised": {"description": "A mobile device compromised alert. Derived from audit logs.", "id": "GoogleAppsAlertcenterTypeDeviceCompromised", "properties": {"email": {"description": "The email of the user this alert was created for.", "type": "string"}, "events": {"description": "Required. The list of security events.", "items": {"$ref": "GoogleAppsAlertcenterTypeDeviceCompromisedDeviceCompromisedSecurityDetail"}, "type": "array"}}, "type": "object"}, "GoogleAppsAlertcenterTypeDeviceCompromisedDeviceCompromisedSecurityDetail": {"description": "Detailed information of a single MDM device compromised event.", "id": "GoogleAppsAlertcenterTypeDeviceCompromisedDeviceCompromisedSecurityDetail", "properties": {"deviceCompromisedState": {"description": "The device compromised state. Possible values are \"`Compromised`\" or \"`Not Compromised`\".", "type": "string"}, "deviceId": {"description": "Required. The device ID.", "type": "string"}, "deviceModel": {"description": "The model of the device.", "type": "string"}, "deviceType": {"description": "The type of the device.", "type": "string"}, "iosVendorId": {"description": "Required for iOS, empty for others.", "type": "string"}, "resourceId": {"description": "The device resource ID.", "type": "string"}, "serialNumber": {"description": "The serial number of the device.", "type": "string"}}, "type": "object"}, "GoogleAppsAlertcenterTypeDlpRuleViolation": {"description": "Alerts that get triggered on violations of Data Loss Prevention (DLP) rules.", "id": "GoogleAppsAlertcenterTypeDlpRuleViolation", "properties": {"ruleViolationInfo": {"$ref": "GoogleAppsAlertcenterTypeRuleViolationInfo", "description": "Details about the violated DLP rule. <PERSON><PERSON> can use the predefined detectors provided by Google Cloud DLP https://cloud.google.com/dlp/ when setting up a DLP rule. Matched Cloud DLP detectors in this violation if any will be captured in the MatchInfo.predefined_detector."}}, "type": "object"}, "GoogleAppsAlertcenterTypeDomainId": {"description": "Domain ID of Gmail phishing alerts.", "id": "GoogleAppsAlertcenterTypeDomainId", "properties": {"customerPrimaryDomain": {"description": "The primary domain for the customer.", "type": "string"}}, "type": "object"}, "GoogleAppsAlertcenterTypeDomainWideTakeoutInitiated": {"description": "A takeout operation for the entire domain was initiated by an admin. Derived from audit logs.", "id": "GoogleAppsAlertcenterTypeDomainWideTakeoutInitiated", "properties": {"email": {"description": "The email of the admin who initiated the takeout.", "type": "string"}, "takeoutRequestId": {"description": "The takeout request ID.", "type": "string"}}, "type": "object"}, "GoogleAppsAlertcenterTypeGmailMessageInfo": {"description": "Details of a message in phishing spike alert.", "id": "GoogleAppsAlertcenterTypeGmailMessageInfo", "properties": {"attachmentsSha256Hash": {"description": "The `SHA256` hash of email's attachment and all MIME parts.", "items": {"type": "string"}, "type": "array"}, "date": {"description": "The date the malicious email was sent.", "format": "google-datetime", "type": "string"}, "md5HashMessageBody": {"description": "The hash of the message body text.", "type": "string"}, "md5HashSubject": {"description": "The MD5 Hash of email's subject (only available for reported emails).", "type": "string"}, "messageBodySnippet": {"description": "The snippet of the message body text (only available for reported emails).", "type": "string"}, "messageId": {"description": "The message ID.", "type": "string"}, "recipient": {"description": "The recipient of this email.", "type": "string"}, "subjectText": {"description": "The email subject text (only available for reported emails).", "type": "string"}}, "type": "object"}, "GoogleAppsAlertcenterTypeGoogleOperations": {"description": "An incident reported by Google Operations for a Google Workspace application.", "id": "GoogleAppsAlertcenterTypeGoogleOperations", "properties": {"affectedUserEmails": {"description": "The list of emails which correspond to the users directly affected by the incident.", "items": {"type": "string"}, "type": "array"}, "attachmentData": {"$ref": "GoogleAppsAlertcenterTypeAttachment", "description": "Optional. Application-specific data for an incident, provided when the Google Workspace application which reported the incident cannot be completely restored to a valid state."}, "description": {"description": "A detailed, freeform incident description.", "type": "string"}, "header": {"description": "A header to display above the incident message. Typically used to attach a localized notice on the timeline for followup comms translations.", "type": "string"}, "title": {"description": "A one-line incident description.", "type": "string"}}, "type": "object"}, "GoogleAppsAlertcenterTypeMailPhishing": {"description": "Proto for all phishing alerts with common payload. Supported types are any of the following: * User reported phishing * User reported spam spike * Suspicious message reported * Phishing reclassification * Malware reclassification * Gmail potential employee spoofing", "id": "GoogleAppsAlertcenterTypeMailPhishing", "properties": {"domainId": {"$ref": "GoogleAppsAlertcenterTypeDomainId", "description": "The domain ID."}, "isInternal": {"description": "If `true`, the email originated from within the organization.", "type": "boolean"}, "maliciousEntity": {"$ref": "GoogleAppsAlertcenterTypeMaliciousEntity", "description": "The entity whose actions triggered a Gmail phishing alert."}, "messages": {"description": "The list of messages contained by this alert.", "items": {"$ref": "GoogleAppsAlertcenterTypeGmailMessageInfo"}, "type": "array"}, "systemActionType": {"description": "System actions on the messages.", "enum": ["SYSTEM_ACTION_TYPE_UNSPECIFIED", "NO_OPERATION", "REMOVED_FROM_INBOX"], "enumDescriptions": ["System action is unspecified.", "No operation.", "Messages were removed from the inbox."], "type": "string"}}, "type": "object"}, "GoogleAppsAlertcenterTypeMaliciousEntity": {"description": "<PERSON><PERSON><PERSON> whose actions triggered a Gmail phishing alert.", "id": "GoogleAppsAlertcenterTypeMaliciousEntity", "properties": {"displayName": {"description": "The header from display name.", "type": "string"}, "entity": {"$ref": "GoogleAppsAlertcenterTypeUser", "description": "The actor who triggered a gmail phishing alert."}, "fromHeader": {"description": "The sender email address.", "type": "string"}}, "type": "object"}, "GoogleAppsAlertcenterTypePhishingSpike": {"description": "Alert for a spike in user reported phishing. *Warning*: This type has been deprecated. Use [MailPhishing](/admin-sdk/alertcenter/reference/rest/v1beta1/MailPhishing) instead.", "id": "GoogleAppsAlertcenterTypePhishingSpike", "properties": {"domainId": {"$ref": "GoogleAppsAlertcenterTypeDomainId", "description": "The domain ID."}, "isInternal": {"description": "If `true`, the email originated from within the organization.", "type": "boolean"}, "maliciousEntity": {"$ref": "GoogleAppsAlertcenterTypeMaliciousEntity", "description": "The entity whose actions triggered a Gmail phishing alert."}, "messages": {"description": "The list of messages contained by this alert.", "items": {"$ref": "GoogleAppsAlertcenterTypeGmailMessageInfo"}, "type": "array"}}, "type": "object"}, "GoogleAppsAlertcenterTypeRuleViolationInfo": {"description": "Common alert information about violated rules that are configured by Google Workspace administrators.", "id": "GoogleAppsAlertcenterTypeRuleViolationInfo", "properties": {"dataSource": {"description": "Source of the data.", "enum": ["DATA_SOURCE_UNSPECIFIED", "DRIVE"], "enumDescriptions": ["Data source is unspecified.", "Drive data source."], "type": "string"}, "matchInfo": {"description": "List of matches that were found in the resource content.", "items": {"$ref": "GoogleAppsAlertcenterTypeRuleViolationInfoMatchInfo"}, "type": "array"}, "recipients": {"description": "Resource recipients. For Drive, they are grantees that the Drive file was shared with at the time of rule triggering. Valid values include user emails, group emails, domains, or 'anyone' if the file was publicly accessible. If the file was private the recipients list will be empty. For Gmail, they are emails of the users or groups that the Gmail message was sent to.", "items": {"type": "string"}, "type": "array"}, "resourceInfo": {"$ref": "GoogleAppsAlertcenterTypeRuleViolationInfoResourceInfo", "description": "Details of the resource which violated the rule."}, "ruleInfo": {"$ref": "GoogleAppsAlertcenterTypeRuleViolationInfoRuleInfo", "description": "Details of the violated rule."}, "suppressedActionTypes": {"description": "Actions suppressed due to other actions with higher priority.", "items": {"enum": ["ACTION_TYPE_UNSPECIFIED", "DRIVE_BLOCK_EXTERNAL_SHARING", "DRIVE_WARN_ON_EXTERNAL_SHARING", "ALERT", "RULE_ACTIVATE", "RULE_DEACTIVATE"], "enumDescriptions": ["Action type is unspecified.", "Block sharing a file externally.", "Show a warning message when sharing a file externally.", "Send alert.", "Activate Rule Action", "Deactivate Rule Action"], "type": "string"}, "type": "array"}, "trigger": {"description": "Trigger of the rule.", "enum": ["TRIGGER_UNSPECIFIED", "DRIVE_SHARE"], "enumDescriptions": ["<PERSON>gger is unspecified.", "A Drive file is shared."], "type": "string"}, "triggeredActionInfo": {"description": "Metadata related to the triggered actions.", "items": {"$ref": "GoogleAppsAlertcenterTypeRuleViolationInfoActionInfo"}, "type": "array"}, "triggeredActionTypes": {"description": "Actions applied as a consequence of the rule being triggered.", "items": {"enum": ["ACTION_TYPE_UNSPECIFIED", "DRIVE_BLOCK_EXTERNAL_SHARING", "DRIVE_WARN_ON_EXTERNAL_SHARING", "ALERT", "RULE_ACTIVATE", "RULE_DEACTIVATE"], "enumDescriptions": ["Action type is unspecified.", "Block sharing a file externally.", "Show a warning message when sharing a file externally.", "Send alert.", "Activate Rule Action", "Deactivate Rule Action"], "type": "string"}, "type": "array"}, "triggeringUserEmail": {"description": "Email of the user who caused the violation. Value could be empty if not applicable, for example, a violation found by drive continuous scan.", "type": "string"}}, "type": "object"}, "GoogleAppsAlertcenterTypeRuleViolationInfoActionInfo": {"description": "Metadata related to the action.", "id": "GoogleAppsAlertcenterTypeRuleViolationInfoActionInfo", "properties": {}, "type": "object"}, "GoogleAppsAlertcenterTypeRuleViolationInfoMatchInfo": {"description": "Proto that contains match information from the condition part of the rule.", "id": "GoogleAppsAlertcenterTypeRuleViolationInfoMatchInfo", "properties": {"predefinedDetector": {"$ref": "GoogleAppsAlertcenterTypeRuleViolationInfoMatchInfoPredefinedDetectorInfo", "description": "For matched detector predefined by Google."}, "userDefinedDetector": {"$ref": "GoogleAppsAlertcenterTypeRuleViolationInfoMatchInfoUserDefinedDetectorInfo", "description": "For matched detector defined by administrators."}}, "type": "object"}, "GoogleAppsAlertcenterTypeRuleViolationInfoMatchInfoPredefinedDetectorInfo": {"description": "Detector provided by Google.", "id": "GoogleAppsAlertcenterTypeRuleViolationInfoMatchInfoPredefinedDetectorInfo", "properties": {"detectorName": {"description": "Name that uniquely identifies the detector.", "type": "string"}}, "type": "object"}, "GoogleAppsAlertcenterTypeRuleViolationInfoMatchInfoUserDefinedDetectorInfo": {"description": "Detector defined by administrators.", "id": "GoogleAppsAlertcenterTypeRuleViolationInfoMatchInfoUserDefinedDetectorInfo", "properties": {"displayName": {"description": "Display name of the detector.", "type": "string"}, "resourceName": {"description": "Resource name that uniquely identifies the detector.", "type": "string"}}, "type": "object"}, "GoogleAppsAlertcenterTypeRuleViolationInfoResourceInfo": {"description": "Proto that contains resource information.", "id": "GoogleAppsAlertcenterTypeRuleViolationInfoResourceInfo", "properties": {"documentId": {"description": "Drive file ID.", "type": "string"}, "resourceTitle": {"description": "Title of the resource, for example email subject, or document title.", "type": "string"}}, "type": "object"}, "GoogleAppsAlertcenterTypeRuleViolationInfoRuleInfo": {"description": "Proto that contains rule information.", "id": "GoogleAppsAlertcenterTypeRuleViolationInfoRuleInfo", "properties": {"displayName": {"description": "User provided name of the rule.", "type": "string"}, "resourceName": {"description": "Resource name that uniquely identifies the rule.", "type": "string"}}, "type": "object"}, "GoogleAppsAlertcenterTypeStateSponsoredAttack": {"description": "A state-sponsored attack alert. Derived from audit logs.", "id": "GoogleAppsAlertcenterTypeStateSponsoredAttack", "properties": {"email": {"description": "The email of the user this incident was created for.", "type": "string"}}, "type": "object"}, "GoogleAppsAlertcenterTypeSuspiciousActivity": {"description": "A mobile suspicious activity alert. Derived from audit logs.", "id": "GoogleAppsAlertcenterTypeSuspiciousActivity", "properties": {"email": {"description": "The email of the user this alert was created for.", "type": "string"}, "events": {"description": "Required. The list of security events.", "items": {"$ref": "GoogleAppsAlertcenterTypeSuspiciousActivitySuspiciousActivitySecurityDetail"}, "type": "array"}}, "type": "object"}, "GoogleAppsAlertcenterTypeSuspiciousActivitySuspiciousActivitySecurityDetail": {"description": "Detailed information of a single MDM suspicious activity event.", "id": "GoogleAppsAlertcenterTypeSuspiciousActivitySuspiciousActivitySecurityDetail", "properties": {"deviceId": {"description": "Required. The device ID.", "type": "string"}, "deviceModel": {"description": "The model of the device.", "type": "string"}, "deviceProperty": {"description": "The device property which was changed.", "type": "string"}, "deviceType": {"description": "The type of the device.", "type": "string"}, "iosVendorId": {"description": "Required for iOS, empty for others.", "type": "string"}, "newValue": {"description": "The new value of the device property after the change.", "type": "string"}, "oldValue": {"description": "The old value of the device property before the change.", "type": "string"}, "resourceId": {"description": "The device resource ID.", "type": "string"}, "serialNumber": {"description": "The serial number of the device.", "type": "string"}}, "type": "object"}, "GoogleAppsAlertcenterTypeUser": {"description": "A user.", "id": "GoogleAppsAlertcenterTypeUser", "properties": {"displayName": {"description": "Display name of the user.", "type": "string"}, "emailAddress": {"description": "Email address of the user.", "type": "string"}}, "type": "object"}, "GoogleAppsAlertcenterV1beta1Alert": {"description": "An alert affecting a customer.", "id": "GoogleAppsAlertcenterV1beta1Alert", "properties": {"alertId": {"description": "Output only. The unique identifier for the alert.", "type": "string"}, "createTime": {"description": "Output only. The time this alert was created.", "format": "google-datetime", "type": "string"}, "customerId": {"description": "Output only. The unique identifier of the Google account of the customer.", "type": "string"}, "data": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Optional. The data associated with this alert, for example google.apps.alertcenter.type.DeviceCompromised.", "type": "object"}, "deleted": {"description": "Output only. `True` if this alert is marked for deletion.", "type": "boolean"}, "endTime": {"description": "Optional. The time the event that caused this alert ceased being active. If provided, the end time must not be earlier than the start time. If not provided, it indicates an ongoing alert.", "format": "google-datetime", "type": "string"}, "etag": {"description": "Optional. `etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of an alert from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform alert updates in order to avoid race conditions: An `etag` is returned in the response which contains alerts, and systems are expected to put that etag in the request to update alert to ensure that their change will be applied to the same version of the alert. If no `etag` is provided in the call to update alert, then the existing alert is overwritten blindly.", "type": "string"}, "metadata": {"$ref": "GoogleAppsAlertcenterV1beta1AlertMetadata", "description": "Output only. The metadata associated with this alert."}, "securityInvestigationToolLink": {"description": "Output only. An optional [Security Investigation Tool](https://support.google.com/a/answer/7575955) query for this alert.", "type": "string"}, "source": {"description": "Required. A unique identifier for the system that reported the alert. This is output only after alert is created. Supported sources are any of the following: * Google Operations * Mobile device management * Gmail phishing * Domain wide takeout * State sponsored attack * Google identity", "type": "string"}, "startTime": {"description": "Required. The time the event that caused this alert was started or detected.", "format": "google-datetime", "type": "string"}, "type": {"description": "Required. The type of the alert. This is output only after alert is created. For a list of available alert types see [Google Workspace Alert types](/admin-sdk/alertcenter/reference/alert-types).", "type": "string"}, "updateTime": {"description": "Output only. The time this alert was last updated.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleAppsAlertcenterV1beta1AlertFeedback": {"description": "A customer feedback about an alert.", "id": "GoogleAppsAlertcenterV1beta1AlertFeedback", "properties": {"alertId": {"description": "Output only. The alert identifier.", "type": "string"}, "createTime": {"description": "Output only. The time this feedback was created.", "format": "google-datetime", "type": "string"}, "customerId": {"description": "Output only. The unique identifier of the Google account of the customer.", "type": "string"}, "email": {"description": "Output only. The email of the user that provided the feedback.", "type": "string"}, "feedbackId": {"description": "Output only. The unique identifier for the feedback.", "type": "string"}, "type": {"description": "Required. The type of the feedback.", "enum": ["ALERT_FEEDBACK_TYPE_UNSPECIFIED", "NOT_USEFUL", "SOMEWHAT_USEFUL", "VERY_USEFUL"], "enumDescriptions": ["The feedback type is not specified.", "The alert report is not useful.", "The alert report is somewhat useful.", "The alert report is very useful."], "type": "string"}}, "type": "object"}, "GoogleAppsAlertcenterV1beta1AlertMetadata": {"description": "An alert metadata.", "id": "GoogleAppsAlertcenterV1beta1AlertMetadata", "properties": {"alertId": {"description": "Output only. The alert identifier.", "type": "string"}, "assignee": {"description": "The email address of the user assigned to the alert.", "type": "string"}, "customerId": {"description": "Output only. The unique identifier of the Google account of the customer.", "type": "string"}, "etag": {"description": "Optional. `etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of an alert metadata from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform metatdata updates in order to avoid race conditions: An `etag` is returned in the response which contains alert metadata, and systems are expected to put that etag in the request to update alert metadata to ensure that their change will be applied to the same version of the alert metadata. If no `etag` is provided in the call to update alert metadata, then the existing alert metadata is overwritten blindly.", "type": "string"}, "severity": {"description": "The severity value of the alert. Alert Center will set this field at alert creation time, default's to an empty string when it could not be determined. The supported values for update actions on this field are the following: * HIGH * MEDIUM * LOW", "type": "string"}, "status": {"description": "The current status of the alert. The supported values are the following: * NOT_STARTED * IN_PROGRESS * CLOSED", "type": "string"}, "updateTime": {"description": "Output only. The time this metadata was last updated.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleAppsAlertcenterV1beta1BatchDeleteAlertsRequest": {"description": "A request to perform batch delete on alerts.", "id": "GoogleAppsAlertcenterV1beta1BatchDeleteAlertsRequest", "properties": {"alertId": {"description": "Required. list of alert IDs.", "items": {"type": "string"}, "type": "array"}, "customerId": {"description": "Optional. The unique identifier of the Google Workspace organization account of the customer the alerts are associated with.", "type": "string"}}, "type": "object"}, "GoogleAppsAlertcenterV1beta1BatchDeleteAlertsResponse": {"description": "Response to batch delete operation on alerts.", "id": "GoogleAppsAlertcenterV1beta1BatchDeleteAlertsResponse", "properties": {"failedAlertStatus": {"additionalProperties": {"$ref": "GoogleRpcStatus"}, "description": "The status details for each failed alert_id.", "type": "object"}, "successAlertIds": {"description": "The successful list of alert IDs.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleAppsAlertcenterV1beta1BatchUndeleteAlertsRequest": {"description": "A request to perform batch undelete on alerts.", "id": "GoogleAppsAlertcenterV1beta1BatchUndeleteAlertsRequest", "properties": {"alertId": {"description": "Required. list of alert IDs.", "items": {"type": "string"}, "type": "array"}, "customerId": {"description": "Optional. The unique identifier of the Google Workspace organization account of the customer the alerts are associated with.", "type": "string"}}, "type": "object"}, "GoogleAppsAlertcenterV1beta1BatchUndeleteAlertsResponse": {"description": "Response to batch undelete operation on alerts.", "id": "GoogleAppsAlertcenterV1beta1BatchUndeleteAlertsResponse", "properties": {"failedAlertStatus": {"additionalProperties": {"$ref": "GoogleRpcStatus"}, "description": "The status details for each failed alert_id.", "type": "object"}, "successAlertIds": {"description": "The successful list of alert IDs.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleAppsAlertcenterV1beta1ListAlertFeedbackResponse": {"description": "Response message for an alert feedback listing request.", "id": "GoogleAppsAlertcenterV1beta1ListAlertFeedbackResponse", "properties": {"feedback": {"description": "The list of alert feedback. Feedback entries for each alert are ordered by creation time descending.", "items": {"$ref": "GoogleAppsAlertcenterV1beta1AlertFeedback"}, "type": "array"}}, "type": "object"}, "GoogleAppsAlertcenterV1beta1ListAlertsResponse": {"description": "Response message for an alert listing request.", "id": "GoogleAppsAlertcenterV1beta1ListAlertsResponse", "properties": {"alerts": {"description": "The list of alerts.", "items": {"$ref": "GoogleAppsAlertcenterV1beta1Alert"}, "type": "array"}, "nextPageToken": {"description": "The token for the next page. If not empty, indicates that there may be more alerts that match the listing request; this value can be used in a subsequent ListAlertsRequest to get alerts continuing from last result of the current list call.", "type": "string"}}, "type": "object"}, "GoogleAppsAlertcenterV1beta1Settings": {"description": "Customer-level settings.", "id": "GoogleAppsAlertcenterV1beta1Settings", "properties": {"notifications": {"description": "The list of notifications.", "items": {"$ref": "GoogleAppsAlertcenterV1beta1SettingsNotification"}, "type": "array"}}, "type": "object"}, "GoogleAppsAlertcenterV1beta1SettingsNotification": {"description": "Settings for callback notifications. For more details see [Google Workspace Alert Notification](/admin-sdk/alertcenter/guides/notifications).", "id": "GoogleAppsAlertcenterV1beta1SettingsNotification", "properties": {"cloudPubsubTopic": {"$ref": "GoogleAppsAlertcenterV1beta1SettingsNotificationCloudPubsubTopic", "description": "A Google Cloud Pub/sub topic destination."}}, "type": "object"}, "GoogleAppsAlertcenterV1beta1SettingsNotificationCloudPubsubTopic": {"description": "A reference to a Cloud Pubsub topic. To register for notifications, the owner of the topic must grant `<EMAIL>` the `projects.topics.publish` permission.", "id": "GoogleAppsAlertcenterV1beta1SettingsNotificationCloudPubsubTopic", "properties": {"payloadFormat": {"description": "Optional. The format of the payload that would be sent. If not specified the format will be JSON.", "enum": ["PAYLOAD_FORMAT_UNSPECIFIED", "JSON"], "enumDescriptions": ["Payload format is not specified (will use JSON as default).", "Use JSON."], "type": "string"}, "topicName": {"description": "The `name` field of a Cloud Pubsub [Topic] (https://cloud.google.com/pubsub/docs/reference/rest/v1/projects.topics#Topic).", "type": "string"}}, "type": "object"}, "GoogleAppsAlertcenterV1beta1UndeleteAlertRequest": {"description": "A request to undelete a specific alert that was marked for deletion.", "id": "GoogleAppsAlertcenterV1beta1UndeleteAlertRequest", "properties": {"customerId": {"description": "Optional. The unique identifier of the Google Workspace organization account of the customer the alert is associated with. Inferred from the caller identity if not provided.", "type": "string"}}, "type": "object"}, "GoogleProtobufEmpty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); } The JSON representation for `Empty` is empty JSON object `{}`.", "id": "GoogleProtobufEmpty", "properties": {}, "type": "object"}, "GoogleRpcStatus": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "GoogleRpcStatus", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Google Workspace Alert Center API", "version": "v1beta1", "version_module": true}