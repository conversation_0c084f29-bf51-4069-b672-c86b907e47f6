{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/chrome.management.policy": {"description": "See, edit, create or delete policies applied to Chrome OS and Chrome Browsers managed within your organization"}, "https://www.googleapis.com/auth/chrome.management.policy.readonly": {"description": "See policies applied to Chrome OS and Chrome Browsers managed within your organization"}}}}, "basePath": "", "baseUrl": "https://chromepolicy.googleapis.com/", "batchPath": "batch", "canonicalName": "Chrome Policy", "description": "The Chrome Policy API is a suite of services that allows Chrome administrators to control the policies applied to their managed Chrome OS devices and Chrome browsers.", "discoveryVersion": "v1", "documentationLink": "http://developers.google.com/chrome/policy", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "chromepolicy:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://chromepolicy.mtls.googleapis.com/", "name": "chromepolicy", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"customers": {"resources": {"policies": {"methods": {"resolve": {"description": "Gets the resolved policy values for a list of policies that match a search query.", "flatPath": "v1/customers/{customersId}/policies:resolve", "httpMethod": "POST", "id": "chromepolicy.customers.policies.resolve", "parameterOrder": ["customer"], "parameters": {"customer": {"description": "ID of the G Suite account or literal \"my_customer\" for the customer associated to the request.", "location": "path", "pattern": "^customers/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+customer}/policies:resolve", "request": {"$ref": "GoogleChromePolicyV1ResolveRequest"}, "response": {"$ref": "GoogleChromePolicyV1ResolveResponse"}, "scopes": ["https://www.googleapis.com/auth/chrome.management.policy", "https://www.googleapis.com/auth/chrome.management.policy.readonly"]}}, "resources": {"orgunits": {"methods": {"batchInherit": {"description": "Modify multiple policy values that are applied to a specific org unit so that they now inherit the value from a parent (if applicable). All targets must have the same target format. That is to say that they must point to the same target resource and must have the same keys specified in `additionalTargetKeyNames`. On failure the request will return the error details as part of the google.rpc.Status.", "flatPath": "v1/customers/{customersId}/policies/orgunits:batchInherit", "httpMethod": "POST", "id": "chromepolicy.customers.policies.orgunits.batchInherit", "parameterOrder": ["customer"], "parameters": {"customer": {"description": "ID of the G Suite account or literal \"my_customer\" for the customer associated to the request.", "location": "path", "pattern": "^customers/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+customer}/policies/orgunits:batchInherit", "request": {"$ref": "GoogleChromePolicyV1BatchInheritOrgUnitPoliciesRequest"}, "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/chrome.management.policy"]}, "batchModify": {"description": "Modify multiple policy values that are applied to a specific org unit. All targets must have the same target format. That is to say that they must point to the same target resource and must have the same keys specified in `additionalTargetKeyNames`. On failure the request will return the error details as part of the google.rpc.Status.", "flatPath": "v1/customers/{customersId}/policies/orgunits:batchModify", "httpMethod": "POST", "id": "chromepolicy.customers.policies.orgunits.batchModify", "parameterOrder": ["customer"], "parameters": {"customer": {"description": "ID of the G Suite account or literal \"my_customer\" for the customer associated to the request.", "location": "path", "pattern": "^customers/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+customer}/policies/orgunits:batchModify", "request": {"$ref": "GoogleChromePolicyV1BatchModifyOrgUnitPoliciesRequest"}, "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/chrome.management.policy"]}}}}}, "policySchemas": {"methods": {"get": {"description": "Get a specific policy schema for a customer by its resource name.", "flatPath": "v1/customers/{customersId}/policySchemas/{policySchemasId}", "httpMethod": "GET", "id": "chromepolicy.customers.policySchemas.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The policy schema resource name to query.", "location": "path", "pattern": "^customers/[^/]+/policySchemas/.*$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleChromePolicyV1PolicySchema"}, "scopes": ["https://www.googleapis.com/auth/chrome.management.policy", "https://www.googleapis.com/auth/chrome.management.policy.readonly"]}, "list": {"description": "Gets a list of policy schemas that match a specified filter value for a given customer.", "flatPath": "v1/customers/{customersId}/policySchemas", "httpMethod": "GET", "id": "chromepolicy.customers.policySchemas.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "The schema filter used to find a particular schema based on fields like its resource name, description and `additionalTargetKeyNames`.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of policy schemas to return.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The page token used to retrieve a specific page of the listing request.", "location": "query", "type": "string"}, "parent": {"description": "Required. The customer for which the listing request will apply.", "location": "path", "pattern": "^customers/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/policySchemas", "response": {"$ref": "GoogleChromePolicyV1ListPolicySchemasResponse"}, "scopes": ["https://www.googleapis.com/auth/chrome.management.policy", "https://www.googleapis.com/auth/chrome.management.policy.readonly"]}}}}}}, "revision": "20210410", "rootUrl": "https://chromepolicy.googleapis.com/", "schemas": {"GoogleChromePolicyV1AdditionalTargetKeyName": {"description": "Additional key names that will be used to identify the target of the policy value.", "id": "GoogleChromePolicyV1AdditionalTargetKeyName", "properties": {"key": {"description": "Key name.", "type": "string"}, "keyDescription": {"description": "Key description.", "type": "string"}}, "type": "object"}, "GoogleChromePolicyV1BatchInheritOrgUnitPoliciesRequest": {"description": "Request message for specifying that multiple policy values inherit their value from their parents.", "id": "GoogleChromePolicyV1BatchInheritOrgUnitPoliciesRequest", "properties": {"requests": {"description": "List of policies that have to inherit their values as defined by the `requests`. All requests in the list must follow these restrictions: 1. All schemas in the list must have the same root namespace. 2. All `policyTargetKey.targetResource` values must point to an org unit resource. 3. All `policyTargetKey` values must have the same key names in the ` additionalTargetKeys`. This also means if one of the targets has an empty `additionalTargetKeys` map, all of the targets must have an empty `additionalTargetKeys` map. 4. No two modification requests can reference the same `policySchema` + ` policyTargetKey` pair. ", "items": {"$ref": "GoogleChromePolicyV1InheritOrgUnitPolicyRequest"}, "type": "array"}}, "type": "object"}, "GoogleChromePolicyV1BatchModifyOrgUnitPoliciesRequest": {"description": "Request message for modifying multiple policy values for a specific target.", "id": "GoogleChromePolicyV1BatchModifyOrgUnitPoliciesRequest", "properties": {"requests": {"description": "List of policies to modify as defined by the `requests`. All requests in the list must follow these restrictions: 1. All schemas in the list must have the same root namespace. 2. All `policyTargetKey.targetResource` values must point to an org unit resource. 3. All `policyTargetKey` values must have the same key names in the ` additionalTargetKeys`. This also means if one of the targets has an empty `additionalTargetKeys` map, all of the targets must have an empty `additionalTargetKeys` map. 4. No two modification requests can reference the same `policySchema` + ` policyTargetKey` pair. ", "items": {"$ref": "GoogleChromePolicyV1ModifyOrgUnitPolicyRequest"}, "type": "array"}}, "type": "object"}, "GoogleChromePolicyV1InheritOrgUnitPolicyRequest": {"description": "Request parameters for inheriting policy value of a specific org unit target from the policy value of its parent org unit.", "id": "GoogleChromePolicyV1InheritOrgUnitPolicyRequest", "properties": {"policySchema": {"description": "The fully qualified name of the policy schema that is being inherited.", "type": "string"}, "policyTargetKey": {"$ref": "GoogleChromePolicyV1PolicyTargetKey", "description": "Required. The key of the target for which we want to modify a policy. The target resource must point to an Org Unit."}}, "type": "object"}, "GoogleChromePolicyV1ListPolicySchemasResponse": {"description": "Response message for listing policy schemas that match a filter.", "id": "GoogleChromePolicyV1ListPolicySchemasResponse", "properties": {"nextPageToken": {"description": "The page token used to get the next page of policy schemas.", "type": "string"}, "policySchemas": {"description": "The list of policy schemas that match the query.", "items": {"$ref": "GoogleChromePolicyV1PolicySchema"}, "type": "array"}}, "type": "object"}, "GoogleChromePolicyV1ModifyOrgUnitPolicyRequest": {"description": "Request parameters for modifying a policy value for a specific org unit target.", "id": "GoogleChromePolicyV1ModifyOrgUnitPolicyRequest", "properties": {"policyTargetKey": {"$ref": "GoogleChromePolicyV1PolicyTargetKey", "description": "Required. The key of the target for which we want to modify a policy. The target resource must point to an Org Unit."}, "policyValue": {"$ref": "GoogleChromePolicyV1PolicyValue", "description": "The new value for the policy."}, "updateMask": {"description": "Required. Policy fields to update. Only fields in this mask will be updated; other fields in `policy_value` will be ignored (even if they have values). If a field is in this list it must have a value in 'policy_value'.", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "GoogleChromePolicyV1PolicySchema": {"description": "Resource representing a policy schema. Next ID: 10", "id": "GoogleChromePolicyV1PolicySchema", "properties": {"accessRestrictions": {"description": "Output only. Specific access restrictions related to this policy.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "additionalTargetKeyNames": {"description": "Output only. Additional key names that will be used to identify the target of the policy value. When specifying a `policyTargetKey`, each of the additional keys specified here will have to be included in the `additionalTargetKeys` map.", "items": {"$ref": "GoogleChromePolicyV1AdditionalTargetKeyName"}, "readOnly": true, "type": "array"}, "definition": {"$ref": "Proto2FileDescriptorProto", "description": "Schema definition using proto descriptor."}, "fieldDescriptions": {"description": "Output only. Detailed description of each field that is part of the schema.", "items": {"$ref": "GoogleChromePolicyV1PolicySchemaFieldDescription"}, "readOnly": true, "type": "array"}, "name": {"description": "Format: name=customers/{customer}/policySchemas/{schema_namespace}", "type": "string"}, "notices": {"description": "Output only. Special notice messages related to setting certain values in certain fields in the schema.", "items": {"$ref": "GoogleChromePolicyV1PolicySchemaNoticeDescription"}, "readOnly": true, "type": "array"}, "policyDescription": {"description": "Output only. Description about the policy schema for user consumption.", "readOnly": true, "type": "string"}, "schemaName": {"description": "Output only. The full qualified name of the policy schema. This value is used to fill the field `policy_schema` in PolicyValue when calling BatchInheritOrgUnitPolicies or BatchModifyOrgUnitPolicies.", "readOnly": true, "type": "string"}, "supportUri": {"description": "Output only. URI to related support article for this schema.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromePolicyV1PolicySchemaFieldDescription": {"description": "Provides detailed information for a particular field that is part of a PolicySchema.", "id": "GoogleChromePolicyV1PolicySchemaFieldDescription", "properties": {"description": {"description": "Output only. The description for the field.", "readOnly": true, "type": "string"}, "field": {"description": "Output only. The name of the field for associated with this description.", "readOnly": true, "type": "string"}, "inputConstraint": {"description": "Output only. Any input constraints associated on the values for the field.", "readOnly": true, "type": "string"}, "knownValueDescriptions": {"description": "Output only. If the field has a set of know values, this field will provide a description for these values.", "items": {"$ref": "GoogleChromePolicyV1PolicySchemaFieldKnownValueDescription"}, "readOnly": true, "type": "array"}, "nestedFieldDescriptions": {"description": "Output only. Provides the description of the fields nested in this field, if the field is a message type that defines multiple fields.", "items": {"$ref": "GoogleChromePolicyV1PolicySchemaFieldDescription"}, "readOnly": true, "type": "array"}}, "type": "object"}, "GoogleChromePolicyV1PolicySchemaFieldKnownValueDescription": {"description": "Provides detailed information about a known value that is allowed for a particular field in a PolicySchema.", "id": "GoogleChromePolicyV1PolicySchemaFieldKnownValueDescription", "properties": {"description": {"description": "Output only. Additional description for this value.", "readOnly": true, "type": "string"}, "value": {"description": "Output only. The string represenstation of the value that can be set for the field.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromePolicyV1PolicySchemaNoticeDescription": {"description": "Provides special notice messages related to a particular value in a field that is part of a PolicySchema.", "id": "GoogleChromePolicyV1PolicySchemaNoticeDescription", "properties": {"acknowledgementRequired": {"description": "Output only. Whether the user needs to acknowledge the notice message before the value can be set.", "readOnly": true, "type": "boolean"}, "field": {"description": "Output only. The field name associated with the notice.", "readOnly": true, "type": "string"}, "noticeMessage": {"description": "Output only. The notice message associate with the value of the field.", "readOnly": true, "type": "string"}, "noticeValue": {"description": "Output only. The value of the field that has a notice. When setting the field to this value, the user may be required to acknowledge the notice message in order for the value to be set.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromePolicyV1PolicyTargetKey": {"description": "The key used to identify the target on which the policy will be applied.", "id": "GoogleChromePolicyV1PolicyTargetKey", "properties": {"additionalTargetKeys": {"additionalProperties": {"type": "string"}, "description": "Map containing the additional target key name and value pairs used to further identify the target of the policy.", "type": "object"}, "targetResource": {"description": "The target resource on which this policy is applied. The following resources are supported: * Organizational Unit (\"orgunits/{orgunit_id}\")", "type": "string"}}, "type": "object"}, "GoogleChromePolicyV1PolicyValue": {"description": "A particular value for a policy managed by the service.", "id": "GoogleChromePolicyV1PolicyValue", "properties": {"policySchema": {"description": "The fully qualified name of the policy schema associated with this policy.", "type": "string"}, "value": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "The value of the policy that is compatible with the schema that it is associated with.", "type": "object"}}, "type": "object"}, "GoogleChromePolicyV1ResolveRequest": {"description": "Request message for getting the resolved policy value for a specific target.", "id": "GoogleChromePolicyV1ResolveRequest", "properties": {"pageSize": {"description": "The maximum number of policies to return, defaults to 100 and has a maximum of 1000.", "format": "int32", "type": "integer"}, "pageToken": {"description": "The page token used to retrieve a specific page of the request.", "type": "string"}, "policySchemaFilter": {"description": "The schema filter to apply to the resolve request. Specify a schema name to view a particular schema, for example: chrome.users.ShowLogoutButton Wildcards are supported, but only in the leaf portion of the schema name. Wildcards cannot be used in namespace directly. Please read https://developers.google.com/chrome/chrome-management/guides/policyapi for details on schema namepsaces. For example: Valid: \"chrome.users.*\", \"chrome.users.apps.*\", \"chrome.printers.*\" Invalid: \"*\", \"*.users\", \"chrome.*\", \"chrome.*.apps.*\"", "type": "string"}, "policyTargetKey": {"$ref": "GoogleChromePolicyV1PolicyTargetKey", "description": "Required. The key of the target resource on which the policies should be resolved. The target resource must point to an Org Unit."}}, "type": "object"}, "GoogleChromePolicyV1ResolveResponse": {"description": "Response message for getting the resolved policy value for a specific target.", "id": "GoogleChromePolicyV1ResolveResponse", "properties": {"nextPageToken": {"description": "The page token used to get the next set of resolved policies found by the request.", "type": "string"}, "resolvedPolicies": {"description": "The list of resolved policies found by the resolve request.", "items": {"$ref": "GoogleChromePolicyV1ResolvedPolicy"}, "type": "array"}}, "type": "object"}, "GoogleChromePolicyV1ResolvedPolicy": {"description": "The resolved value of a policy for a given target.", "id": "GoogleChromePolicyV1ResolvedPolicy", "properties": {"sourceKey": {"$ref": "GoogleChromePolicyV1PolicyTargetKey", "description": "Output only. The source resource from which this policy value is obtained. May be the same as `targetKey` if the policy is directly modified on the target, otherwise it would be another resource from which the policy gets its value (if applicable). If not present, the source is the default value for the customer.", "readOnly": true}, "targetKey": {"$ref": "GoogleChromePolicyV1PolicyTargetKey", "description": "Output only. The target resource for which the resolved policy value applies.", "readOnly": true}, "value": {"$ref": "GoogleChromePolicyV1PolicyValue", "description": "Output only. The resolved value of the policy.", "readOnly": true}}, "type": "object"}, "GoogleProtobufEmpty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); } The JSON representation for `Empty` is empty JSON object `{}`.", "id": "GoogleProtobufEmpty", "properties": {}, "type": "object"}, "Proto2DescriptorProto": {"description": "Describes a message type.", "id": "Proto2DescriptorProto", "properties": {"enumType": {"items": {"$ref": "Proto2EnumDescriptorProto"}, "type": "array"}, "field": {"items": {"$ref": "Proto2FieldDescriptorProto"}, "type": "array"}, "name": {"type": "string"}, "nestedType": {"items": {"$ref": "Proto2DescriptorProto"}, "type": "array"}, "oneofDecl": {"items": {"$ref": "Proto2OneofDescriptorProto"}, "type": "array"}}, "type": "object"}, "Proto2EnumDescriptorProto": {"description": "Describes an enum type.", "id": "Proto2EnumDescriptorProto", "properties": {"name": {"type": "string"}, "value": {"items": {"$ref": "Proto2EnumValueDescriptorProto"}, "type": "array"}}, "type": "object"}, "Proto2EnumValueDescriptorProto": {"description": "Describes a value within an enum.", "id": "Proto2EnumValueDescriptorProto", "properties": {"name": {"type": "string"}, "number": {"format": "int32", "type": "integer"}}, "type": "object"}, "Proto2FieldDescriptorProto": {"description": "Describes a field within a message.", "id": "Proto2FieldDescriptorProto", "properties": {"defaultValue": {"description": "For numeric types, contains the original text representation of the value. For booleans, \"true\" or \"false\". For strings, contains the default text contents (not escaped in any way). For bytes, contains the C escaped value. All bytes >= 128 are escaped.", "type": "string"}, "jsonName": {"description": "JSON name of this field. The value is set by protocol compiler. If the user has set a \"json_name\" option on this field, that option's value will be used. Otherwise, it's deduced from the field's name by converting it to camelCase.", "type": "string"}, "label": {"enum": ["LABEL_OPTIONAL", "LABEL_REQUIRED", "LABEL_REPEATED"], "enumDescriptions": ["0 is reserved for errors", "", ""], "type": "string"}, "name": {"type": "string"}, "number": {"format": "int32", "type": "integer"}, "oneofIndex": {"description": "If set, gives the index of a oneof in the containing type's oneof_decl list. This field is a member of that oneof.", "format": "int32", "type": "integer"}, "proto3Optional": {"description": "If true, this is a proto3 \"optional\". When a proto3 field is optional, it tracks presence regardless of field type. When proto3_optional is true, this field must be belong to a oneof to signal to old proto3 clients that presence is tracked for this field. This oneof is known as a \"synthetic\" oneof, and this field must be its sole member (each proto3 optional field gets its own synthetic oneof). Synthetic oneofs exist in the descriptor only, and do not generate any API. Synthetic oneofs must be ordered after all \"real\" oneofs. For message fields, proto3_optional doesn't create any semantic change, since non-repeated message fields always track presence. However it still indicates the semantic detail of whether the user wrote \"optional\" or not. This can be useful for round-tripping the .proto file. For consistency we give message fields a synthetic oneof also, even though it is not required to track presence. This is especially important because the parser can't tell if a field is a message or an enum, so it must always create a synthetic oneof. Proto2 optional fields do not set this flag, because they already indicate optional with `LABEL_OPTIONAL`.", "type": "boolean"}, "type": {"description": "If type_name is set, this need not be set. If both this and type_name are set, this must be one of TYPE_ENUM, TYPE_MESSAGE or TYPE_GROUP.", "enum": ["TYPE_DOUBLE", "TYPE_FLOAT", "TYPE_INT64", "TYPE_UINT64", "TYPE_INT32", "TYPE_FIXED64", "TYPE_FIXED32", "TYPE_BOOL", "TYPE_STRING", "TYPE_GROUP", "TYPE_MESSAGE", "TYPE_BYTES", "TYPE_UINT32", "TYPE_ENUM", "TYPE_SFIXED32", "TYPE_SFIXED64", "TYPE_SINT32", "TYPE_SINT64"], "enumDescriptions": ["0 is reserved for errors. Order is weird for historical reasons.", "", "Not ZigZag encoded. Negative numbers take 10 bytes. Use TYPE_SINT64 if negative values are likely.", "", "Not ZigZag encoded. Negative numbers take 10 bytes. Use TYPE_SINT32 if negative values are likely.", "", "", "", "", "Tag-delimited aggregate. Group type is deprecated and not supported in proto3. However, Proto3 implementations should still be able to parse the group wire format and treat group fields as unknown fields.", "Length-delimited aggregate.", "New in version 2.", "", "", "", "", "Uses ZigZag encoding.", "Uses ZigZag encoding."], "type": "string"}, "typeName": {"description": "For message and enum types, this is the name of the type. If the name starts with a '.', it is fully-qualified. Otherwise, C++-like scoping rules are used to find the type (i.e. first the nested types within this message are searched, then within the parent, on up to the root namespace).", "type": "string"}}, "type": "object"}, "Proto2FileDescriptorProto": {"description": "Describes a complete .proto file.", "id": "Proto2FileDescriptorProto", "properties": {"enumType": {"items": {"$ref": "Proto2EnumDescriptorProto"}, "type": "array"}, "messageType": {"description": "All top-level definitions in this file.", "items": {"$ref": "Proto2DescriptorProto"}, "type": "array"}, "name": {"description": "file name, relative to root of source tree", "type": "string"}, "package": {"description": "e.g. \"foo\", \"foo.bar\", etc.", "type": "string"}, "syntax": {"description": "The syntax of the proto file. The supported values are \"proto2\" and \"proto3\".", "type": "string"}}, "type": "object"}, "Proto2OneofDescriptorProto": {"description": "Describes a oneof.", "id": "Proto2OneofDescriptorProto", "properties": {"name": {"type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Chrome Policy API", "version": "v1", "version_module": true}