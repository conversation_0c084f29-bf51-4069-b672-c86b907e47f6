# Djibouti holidays ar localization.
# Authors: <AUTHORS>
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.42\n"
"POT-Creation-Date: 2023-07-15 20:58+0300\n"
"PO-Revision-Date: 2024-01-22 13:45+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: ar\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=6; plural=(n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5);\n"
"Generated-By: Lingua 4.15.0\n"
"X-Generator: Poedit 3.4\n"

#. %s (estimated).
#, c-format
msgid "%s (estimé)"
msgstr "(تقدير) %s"

#. New Year's Day.
msgid "Nouvel an"
msgstr "يوم السنة الجديدة"

#. Labor Day.
msgid "Fête du travail"
msgstr "عيد العمال"

#. Independence Day.
msgid "Fête de l'indépendance"
msgstr "عيد الإستقلال"

#. Independence Day Holiday.
msgid "Fête de l'indépendance deuxième jour"
msgstr "عطلة عيد الاستقلال"

#. Christmas Day.
msgid "Noël"
msgstr "عيد الميلاد المجيد"

#. Isra and Miraj.
msgid "Al Isra et Al Mirague"
msgstr "الإسراء والمعراج"

#. Eid al-Fitr.
msgid "Eid al-Fitr"
msgstr "عيد الفطر"

#. Eid al-Fitr Holiday.
msgid "Eid al-Fitr deuxième jour"
msgstr "عطلة عيد الفطر"

#. Arafat Day.
msgid "Arafat"
msgstr "يوم عرفة"

#. Eid al-Adha.
msgid "Eid al-Adha"
msgstr "عيد الأضحى"

#. Eid al-Adha Holiday.
msgid "Eid al-Adha deuxième jour"
msgstr "عطلة عيد الأضحى"

#. Islamic New Year.
msgid "Nouvel an musulman"
msgstr "رأس السنة الهجرية"

#. Prophet Muhammad's Birthday.
msgid "Anniversaire du prophète Muhammad"
msgstr "عيد المولد النبوي"
