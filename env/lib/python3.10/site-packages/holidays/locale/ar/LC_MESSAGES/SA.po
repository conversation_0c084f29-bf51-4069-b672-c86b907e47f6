# Saudi Arabia holidays.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.41\n"
"POT-Creation-Date: 2023-08-04 20:02+0300\n"
"PO-Revision-Date: 2023-08-04 20:05+0300\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: ar\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=6; plural=(n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5);\n"
"Generated-By: Lingua 4.15.0\n"
"X-Generator: Poedit 3.2.2\n"

#. %s (estimated).
#, c-format
msgid "(تقدير) %s"
msgstr ""

#. %s (observed).
#, c-format
msgid "(ملاحظة) %s"
msgstr ""

#. Celebrate the country's win against Argentina in the World Cup
msgid "يوم وطني"
msgstr ""

#. Eid al-Fitr Holiday
msgid "عطلة عيد الفطر"
msgstr ""

#. Arafat Day
msgid "يوم عرفة"
msgstr ""

#. Eid al-Adha Holiday
msgid "عطلة عيد الأضحى"
msgstr ""

#. National Day Holiday
msgid "اليوم الوطني"
msgstr ""

#. Founding Day
msgid "يوم التأسيسي"
msgstr ""

#. %s (observed, estimated).
#, c-format
msgid "(تقدير ملاحظة) %s"
msgstr ""
