# Tunisia holidays.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.29\n"
"POT-Creation-Date: 2023-06-28 00:13+0100\n"
"PO-Revision-Date: 2023-09-12 15:48+0100\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: ar\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Lingua 4.15.0\n"
"X-Generator: Poedit 3.3.2\n"

#. %s (estimated).
#, c-format
msgid "(تقدير) %s"
msgstr ""

#. New Year's Day.
msgid "رأس السنة الميلادية"
msgstr ""

#. Revolution and Youth Day - January 14
msgid "عيد الثورة والشباب"
msgstr ""

#. Independence Day.
msgid "عيد الإستقلال"
msgstr ""

#. Martyrs' Day.
msgid "عيد الشهداء"
msgstr ""

#. Labor Day.
msgid "عيد العمال"
msgstr ""

#. Republic Day.
msgid "عيد الجمهورية"
msgstr ""

#. Women's Day.
msgid "عيد المرأة"
msgstr ""

#. Evacuation Day.
msgid "عيد الجلاء"
msgstr ""

#. Eid al-Fitr.
msgid "عيد الفطر"
msgstr ""

#. Eid al-Fitr Holiday.
msgid "عطلة عيد الفطر"
msgstr ""

#. Eid al-Adha.
msgid "عيد الأضحى"
msgstr ""

#. Arafat Day.
msgid "يوم عرفة"
msgstr ""

#. Eid al-Adha Holiday.
msgid "عطلة عيد الأضحى"
msgstr ""

#. Islamic New Year.
msgid "رأس السنة الهجرية"
msgstr ""

#. Prophet's Birthday.
msgid "عيد المولد النبوي"
msgstr ""
