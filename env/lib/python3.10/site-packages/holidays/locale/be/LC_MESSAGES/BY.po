# Belarus holidays.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.34\n"
"POT-Creation-Date: 2023-02-15 20:06-0800\n"
"PO-Revision-Date: 2023-09-27 18:49+0300\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: be\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : 2);\n"
"Generated-By: Lingua 4.15.0\n"
"X-Generator: Poedit 3.2.2\n"

#. Date format (see strftime() Format Codes)
msgid "%d.%m.%Y"
msgstr ""

#. Day off (substituted from %s).
#, c-format
msgid "Выходны (перанесены з %s)"
msgstr ""

#. New Year's Day.
msgid "Новы год"
msgstr ""

#. Orthodox Christmas Day.
msgid "Нараджэнне Хрыстова (праваслаўнае Раство)"
msgstr ""

#. Women's Day.
msgid "Дзень жанчын"
msgstr ""

#. Radunitsa (Day of Rejoicing).
msgid "Радаўніца"
msgstr ""

#. Labor Day.
msgid "Свята працы"
msgstr ""

#. Victory Day.
msgid "Дзень Перамогі"
msgstr ""

#. Independence Day.
msgid "Дзень Незалежнасці Рэспублікі Беларусь (Дзень Рэспублікі)"
msgstr ""

#. October Revolution Day.
msgid "Дзень Кастрычніцкай рэвалюцыі"
msgstr ""

#. Catholic Christmas Day.
msgid "Нараджэнне Хрыстова (каталіцкае Раство)"
msgstr ""
