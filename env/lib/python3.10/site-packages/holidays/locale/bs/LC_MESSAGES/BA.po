# Bosnia and Herzegovina holidays.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.28\n"
"POT-Creation-Date: 2023-06-26 18:31+0300\n"
"PO-Revision-Date: 2023-06-26 18:34+0300\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: bs\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : 2);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.2.2\n"

#. %s (observed).
#, c-format
msgid "%s (pre<PERSON><PERSON>)"
msgstr ""

#. Orthodox Good Friday.
msgid "V<PERSON><PERSON> petak (Pravoslavni)"
msgstr ""

#. Catholic Easter Monday.
msgid "Uskrsni ponedjeljak (Katolički)"
msgstr ""

#. Eid al-Fitr.
msgid "Ramazanski Bajram"
msgstr ""

#. Eid al-Adha.
msgid "Kurban Bajram"
msgstr ""

#. New Year's Day.
msgid "Nova godina"
msgstr ""

#. Orthodox Christmas Day.
msgid "Božić (Pravoslavni)"
msgstr ""

#. International Labor Day.
msgid "Međunarodni praznik rada"
msgstr ""

#. Catholic Christmas Day.
msgid "Božić (Katolički)"
msgstr ""

#. Orthodox Christmas Eve.
msgid "Badnji dan (Pravoslavni)"
msgstr ""

#. Independence Day.
msgid "Dan nezavisnosti"
msgstr ""

#. Catholic Good Friday.
msgid "Veliki petak (Katolički)"
msgstr ""

#. Catholic Easter Sunday.
msgid "Uskrs (Katolički)"
msgstr ""

#. Orthodox Easter Sunday.
msgid "Vaskrs (Pravoslavni)"
msgstr ""

#. Orthodox Easter Monday.
msgid "Uskrsni ponedjeljak (Pravoslavni)"
msgstr ""

#. Victory Day.
msgid "Dan pobjede nad fašizmom"
msgstr ""

#. Statehood Day.
msgid "Dan državnosti"
msgstr ""

#. Catholic Christmas Eve.
msgid "Badnji dan (Katolički)"
msgstr ""

#. Day of establishment of Brčko District.
msgid "Dan uspostavljanja Brčko distrikta"
msgstr ""

#. Orthodox New Year.
msgid "Pravoslavna Nova godina"
msgstr ""

#. Dayton Agreement Day.
msgid "Dan uspostave Opšteg okvirnog sporazuma za mir u Bosni i Hercegovini"
msgstr ""
