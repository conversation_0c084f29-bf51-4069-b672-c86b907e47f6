# Austria holidays.
# Authors: <AUTHORS>
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.42\n"
"POT-Creation-Date: 2023-04-01 17:13+0300\n"
"PO-Revision-Date: 2023-04-01 17:15+0300\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "Neujahr"
msgstr ""

#. Epiphany.
msgid "Heilige Drei Könige"
msgstr ""

#. Easter Monday.
msgid "Ostermontag"
msgstr ""

#. Labor Day.
msgid "Staatsfeiertag"
msgstr ""

#. Ascension Day.
msgid "Christi Himmelfahrt"
msgstr ""

#. Whit Monday.
msgid "Pfingstmontag"
msgstr ""

#. Corpus Christi.
msgid "Fronleichnam"
msgstr ""

#. Assumption Day.
msgid "Mariä Himmelfahrt"
msgstr ""

#. National Day.
msgid "Nationalfeiertag"
msgstr ""

#. All Saints' Day.
msgid "Allerheiligen"
msgstr ""

#. Immaculate Conception.
msgid "Mariä Empfängnis"
msgstr ""

#. Christmas Day.
msgid "Christtag"
msgstr ""

#. St. Stephen's Day.
msgid "Stefanitag"
msgstr ""

#. Good Friday.
msgid "Karfreitag"
msgstr ""

#. Christmas Eve.
msgid "Heiliger Abend"
msgstr ""

#. New Year's Eve.
msgid "Silvester"
msgstr ""

#. St. Martin's Day.
msgid "Hl. Martin"
msgstr ""

#. St. Joseph's Day.
msgid "Hl. Josef"
msgstr ""

#. 1920 Carinthian plebiscite.
msgid "Tag der Volksabstimmung"
msgstr ""

#. St. Leopold's Day.
msgid "Hl. Leopold"
msgstr ""

#. St. Florian's Day.
msgid "Hl. Florian"
msgstr ""

#. St. Rupert's Day.
msgid "Hl. Rupert"
msgstr ""
