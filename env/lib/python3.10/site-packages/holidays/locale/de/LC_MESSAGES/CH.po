# Switzerland holidays.
# Authors: <AUTHORS>
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.42\n"
"POT-Creation-Date: 2023-04-10 15:41+0300\n"
"PO-Revision-Date: 2024-01-21 14:46+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.4\n"

#. New Year's Day.
msgid "Neujahrestag"
msgstr ""

#. Ascension Day.
msgid "Auffahrt"
msgstr ""

#. National Day.
msgid "Nationalfeiertag"
msgstr ""

#. Christmas Day.
msgid "Weihnachten"
msgstr ""

#. Good Friday.
msgid "Karfreitag"
msgstr ""

#. Easter Monday.
msgid "Ostermontag"
msgstr ""

#. Whit Monday.
msgid "Pfingstmontag"
msgstr ""

#. St. Stephen's Day.
msgid "Stephanstag"
msgstr ""

#. Berchtold's Day.
msgid "Berchtoldstag"
msgstr ""

#. Corpus Christi.
msgid "Fronleichnam"
msgstr ""

#. Assumption Day.
msgid "Mariä Himmelfahrt"
msgstr ""

#. All Saints' Day.
msgid "Allerheiligen"
msgstr ""

#. Immaculate Conception.
msgid "Mariä Empfängnis"
msgstr ""

#. Labor Day.
msgid "Tag der Arbeit"
msgstr ""

#. Genevan Fast.
msgid "Genfer Bettag"
msgstr ""

#. Restoration Day.
msgid "Wiederherstellung der Republik"
msgstr ""

#. Battle of Naefels Victory Day.
msgid "Näfelser Fahrt"
msgstr ""

#. Independence Day.
msgid "Fest der Unabhängigkeit"
msgstr ""

#. Republic Day.
msgid "Jahrestag der Ausrufung der Republik"
msgstr ""

#. St. Joseph's Day.
msgid "Josefstag"
msgstr ""

#. St. Nicholas of Flüe.
msgid "Bruder Klaus"
msgstr ""

#. Epiphany.
msgid "Heilige Drei Könige"
msgstr ""

#. Saints Peter and Paul.
msgid "Peter und Paul"
msgstr ""

#. Prayer Monday.
msgid "Bettagsmontag"
msgstr ""
