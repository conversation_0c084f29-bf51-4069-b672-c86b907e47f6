# Germany holidays.
# Authors: <AUTHORS>
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.23\n"
"POT-Creation-Date: 2023-04-04 16:13+0300\n"
"PO-Revision-Date: 2023-04-09 18:41+0300\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "Neujahr"
msgstr ""

#. Good Friday.
msgid "Karfreitag"
msgstr ""

#. Easter Monday.
msgid "Ostermontag"
msgstr ""

#. Labor Day.
msgid "<PERSON><PERSON><PERSON>"
msgstr ""

#. Ascension Day.
msgid "Christi Him<PERSON>fahrt"
msgstr ""

#. Whit Monday.
msgid "Pfingstmontag"
msgstr ""

#. German Unity Day.
msgid "Tag der Deutschen Einheit"
msgstr ""

#. Reformation Day.
msgid "Reformationstag"
msgstr ""

#. Repentance and Prayer Day.
msgid "Buß- und Bettag"
msgstr ""

#. Christmas Day.
msgid "Erster Weihnachtstag"
msgstr ""

#. Second Day of Christmas.
msgid "Zweiter Weihnachtstag"
msgstr ""

#. Easter Sunday.
msgid "Ostersonntag"
msgstr ""

#. Whit Sunday.
msgid "Pfingstsonntag"
msgstr ""

#. International Women's Day.
msgid "Internationaler Frauentag"
msgstr ""

#. 75th anniversary of the liberation from Nazism and the end of the Second
#. World War in Europe.
msgid ""
"75. Jahrestag der Befreiung vom Nationalsozialismus und der Beendigung des "
"Zweiten Weltkriegs in Europa"
msgstr ""

#. Epiphany.
msgid "Heilige Drei Könige"
msgstr ""

#. Corpus Christi.
msgid "Fronleichnam"
msgstr ""

#. All Saints' Day.
msgid "Allerheiligen"
msgstr ""

#. Assumption Day.
msgid "Mariä Himmelfahrt"
msgstr ""

#. World Children's Day.
msgid "Weltkindertag"
msgstr ""
