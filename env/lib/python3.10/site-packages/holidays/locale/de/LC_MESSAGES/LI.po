# Liechtenstein holidays.
# Authors: <AUTHORS>
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.42\n"
"POT-Creation-Date: 2023-04-01 18:49+0300\n"
"PO-Revision-Date: 2024-01-25 21:16+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.4\n"

#. New Year's Day.
msgid "Neujahr"
msgstr ""

#. <PERSON> Berchtold's Day.
msgid "Berchtoldstag"
msgstr ""

#. Epiphany.
msgid "<PERSON>ilige Drei Könige"
msgstr ""

#. Candlemas.
msgid "Mariä Lichtmess"
msgstr ""

#. Shrove Tuesday.
msgid "Fasnachtsdienstag"
msgstr ""

#. Saint Joseph's Day.
msgid "Josefstag"
msgstr ""

#. Good Friday.
msgid "Karfreitag"
msgstr ""

#. Easter Sunday.
msgid "Ostersonntag"
msgstr ""

#. Easter Monday.
msgid "Ostermontag"
msgstr ""

#. Labor Day.
msgid "Tag der Arbeit"
msgstr ""

#. Ascension Day.
msgid "Auffahrt"
msgstr ""

#. Whit Sunday.
msgid "Pfingstsonntag"
msgstr ""

#. Whit Monday.
msgid "Pfingstmontag"
msgstr ""

#. Corpus Christi.
msgid "Fronleichnam"
msgstr ""

#. National Day.
msgid "Staatsfeiertag"
msgstr ""

#. Nativity of Mary.
msgid "Mariä Geburt"
msgstr ""

#. All Saints' Day.
msgid "Allerheiligen"
msgstr ""

#. Immaculate Conception.
msgid "Mariä Empfängnis"
msgstr ""

#. Christmas Eve.
msgid "Heiligabend"
msgstr ""

#. Christmas Day.
msgid "Weihnachten"
msgstr ""

#. St. Stephen's Day.
msgid "Stephanstag"
msgstr ""

#. New Year's Eve.
msgid "Silvester"
msgstr ""
