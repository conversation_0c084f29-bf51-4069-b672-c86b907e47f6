# Luxembourg holidays de localization.
# <AUTHOR> <EMAIL>, (c) 2023.
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.28\n"
"POT-Creation-Date: 2023-06-27 13:17+0300\n"
"PO-Revision-Date: 2024-01-03 19:43+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: Lingua 4.15.0\n"
"X-Generator: Poedit 3.4\n"

#. New Year's Day.
msgid "Neijoerschdag"
msgstr "Neujahr"

#. Easter Monday.
msgid "Ouschterméindeg"
msgstr "Ostermontag"

#. Labor Day.
msgid "Dag vun der Aarbecht"
msgstr "Tag der Arbeit"

#. Europe Day.
msgid "Europadag"
msgstr "Europatag"

#. Ascension Day.
msgid "Christi Him<PERSON>art"
msgstr "Christi Himmelfahrt"

#. Whit Monday.
msgid "Péngschtméindeg"
msgstr "Pfingstmontag"

#. National Day.
msgid "Nationalfeierdag"
msgstr "Nationalfeiertag"

#. Assumption Day.
msgid "Léiffrawëschdag"
msgstr "Mariä Himmelfahrt"

#. All Saints' Day.
msgid "Allerhellgen"
msgstr "Allerheiligen"

#. Christmas Day.
msgid "Chrëschtdag"
msgstr "Weihnachten"

#. St. Stephen's Day.
msgid "Stiefesdag"
msgstr "Zweiter Weihnachtsfeiertag"
