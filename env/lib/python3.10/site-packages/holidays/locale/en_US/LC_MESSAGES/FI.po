# Finland holidays en_US localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.23\n"
"POT-Creation-Date: 2023-04-08 19:30+0300\n"
"PO-Revision-Date: 2023-04-08 19:47+0300\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "Uudenvuodenpäivä"
msgstr "New Year's Day"

#. Epiphany.
msgid "<PERSON><PERSON><PERSON>inen"
msgstr "Epiphany"

#. Good Friday.
msgid "Pitkäperjantai"
msgstr "Good Friday"

#. Easter Sunday.
msgid "Pääsiäispäivä"
msgstr "Easter Sunday"

#. Easter Monday.
msgid "2. pääsiäispäivä"
msgstr "Easter Monday"

#. May Day.
msgid "Vappu"
msgstr "May Day"

#. Ascension Day.
msgid "Helatorstai"
msgstr "Ascension Day"

#. Whit Sunday.
msgid "Helluntaipäivä"
msgstr "Whit Sunday"

#. Midsummer Eve.
msgid "Juhannusaatto"
msgstr "Midsummer Eve"

#. Midsummer Day.
msgid "Juhannuspäivä"
msgstr "Midsummer Day"

#. All Saints' Day.
msgid "Pyhäinpäivä"
msgstr "All Saints' Day"

#. Independence Day.
msgid "Itsenäisyyspäivä"
msgstr "Independence Day"

#. Christmas Eve.
msgid "Jouluaatto"
msgstr "Christmas Eve"

#. Christmas Day.
msgid "Joulupäivä"
msgstr "Christmas Day"

#. Second Day of Christmas.
msgid "Tapaninpäivä"
msgstr "Second Day of Christmas"
