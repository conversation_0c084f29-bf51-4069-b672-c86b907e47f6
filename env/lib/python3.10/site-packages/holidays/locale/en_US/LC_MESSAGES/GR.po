# Greece holidays en_US localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.30\n"
"POT-Creation-Date: 2023-07-27 18:57+0300\n"
"PO-Revision-Date: 2023-07-27 19:01+0300\n"
"Last-Translator: ~Jhell<PERSON> <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: Lingua 4.15.0\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "Πρωτοχρονιά"
msgstr "New Year's Day"

#. Epiphany.
msgid "Θεοφάνεια"
msgstr "Epiphany"

#. Green Monday.
msgid "Καθαρά Δευτέρα"
msgstr "Green Monday"

#. Independence Day.
msgid "Εικοστή Πέμπτη Μαρτίου"
msgstr "Independence Day"

#. Good Friday.
msgid "Μεγάλη Παρασκευή"
msgstr "Good Friday"

#. Easter Monday.
msgid "Δευτέρα του Πάσχα"
msgstr "Easter Monday"

#. Whit Monday.
msgid "Δευτέρα του Αγίου Πνεύματος"
msgstr "Whit Monday"

#. Labor Day.
msgid "Εργατική Πρωτομαγιά"
msgstr "Labor Day"

#. %s (observed).
#, c-format
msgid "%s (παρατηρήθηκε)"
msgstr "%s (observed)"

#. Dormition of the Mother of God.
msgid "Κοίμηση της Θεοτόκου"
msgstr "Dormition of the Mother of God"

#. Ochi Day.
msgid "Ημέρα του Όχι"
msgstr "Ochi Day"

#. Christmas Day.
msgid "Χριστούγεννα"
msgstr "Christmas Day"

#. Glorifying of the Mother of God.
msgid "Σύναξη της Υπεραγίας Θεοτόκου"
msgstr "Glorifying Mother of God"

#. Christmas Eve.
msgid "Παραμονή Χριστουγέννων"
msgstr "Christmas Eve"

#. New Year's Eve.
msgid "Παραμονή Πρωτοχρονιάς"
msgstr "New Year's Eve"
