# Guatemala holidays en_US localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.42\n"
"POT-Creation-Date: 2023-02-19 17:09+0200\n"
"PO-Revision-Date: 2024-01-22 14:09+0200\n"
"Last-Translator: ~Jhell<PERSON> <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: Lingua 4.15.0\n"
"X-Generator: Poedit 3.4\n"

#. New Year's Day.
msgid "Año Nuevo"
msgstr "New Year's Day"

#. Maundy Thursday.
msgid "Ju<PERSON> Santo"
msgstr "Maundy Thursday"

#. Good Friday.
msgid "Viernes Santo"
msgstr "Good Friday"

#. Army Day.
msgid "Día del Ejército"
msgstr "Army Day"

#. Independence Day.
msgid "Día de la Independencia"
msgstr "Independence Day"

#. All Saints' Day.
msgid "Día de Todos los Santos"
msgstr "All Saints' Day"

#. Assumption Day.
msgid "Día de la Asunción"
msgstr "Assumption Day"

#. Revolution Day.
msgid "Día de la Revolución"
msgstr "Revolution Day"

#. Holy Saturday.
msgid "Sabado Santo"
msgstr "Holy Saturday"

#. Labor Day.
msgid "Día del Trabajo"
msgstr "Labor Day"

#. Christmas Day.
msgid "Día de Navidad"
msgstr "Christmas Day"
