# Honduras holidays en_US localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.25\n"
"POT-Creation-Date: 2023-03-05 12:23+0200\n"
"PO-Revision-Date: 2024-01-05 12:38+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.4\n"

#. New Year's Day.
msgid "Año Nuevo"
msgstr "New Year's Day"

#. Maundy Thursday.
msgid "Jueves Santo"
msgstr "Maundy Thursday"

#. Good Friday.
msgid "Viernes Santo"
msgstr "Good Friday"

#. Holy Saturday.
msgid "Sábado de Gloria"
msgstr "Holy Saturday"

#. Panamerican Day.
msgid "Día de las Américas"
msgstr "Panamerican Day"

#. Labor Day.
msgid "Día del Trabajo"
msgstr "Labor Day"

#. Morazan's Day.
msgid "Día de Morazán"
msgstr "Morazan's Day"

#. Columbus Day.
msgid "Día de la Raza"
msgstr "Columbus Day"

#. Army Day.
msgid "Día de las Fuerzas Armadas"
msgstr "Army Day"

#. Morazan Weekend.
msgid "Semana Morazánica"
msgstr "Morazan Weekend"

#. Christmas Day.
msgid "Navidad"
msgstr "Christmas Day"

#. Independence Day.
msgid "Día de la Independencia"
msgstr "Independence Day"
