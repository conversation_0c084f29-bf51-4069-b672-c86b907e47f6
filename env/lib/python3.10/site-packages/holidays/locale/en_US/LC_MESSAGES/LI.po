# Liechtenstein holidays en_US localization.
# <AUTHOR> <EMAIL>, (c) 2023.
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.42\n"
"POT-Creation-Date: 2023-04-01 17:13+0300\n"
"PO-Revision-Date: 2024-01-25 21:17+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.4\n"

#. New Year's Day.
msgid "Neujahr"
msgstr "New Year's Day"

#. Saint Berchtold's Day.
msgid "Berchtoldstag"
msgstr "Saint Berchtold's Day"

#. Epiphany.
msgid "Heilige Drei Könige"
msgstr "Epiphany"

#. Candlemas.
msgid "Mariä Lichtmess"
msgstr "Candlemas"

#. Shrove Tuesday.
msgid "Fasnachtsdienstag"
msgstr "Shrove Tuesday"

#. Saint Joseph's Day.
msgid "Josefstag"
msgstr "Saint Joseph's Day"

#. Good Friday.
msgid "Karfreitag"
msgstr "Good Friday"

#. Easter Sunday.
msgid "Ostersonntag"
msgstr "Easter Sunday"

#. Easter Monday.
msgid "Ostermontag"
msgstr "Easter Monday"

#. Labor Day.
msgid "Tag der Arbeit"
msgstr "Labor Day"

#. Ascension Day.
msgid "Auffahrt"
msgstr "Ascension Day"

#. Whit Sunday.
msgid "Pfingstsonntag"
msgstr "Whit Sunday"

#. Whit Monday.
msgid "Pfingstmontag"
msgstr "Whit Monday"

#. Corpus Christi.
msgid "Fronleichnam"
msgstr "Corpus Christi"

#. National Day.
msgid "Staatsfeiertag"
msgstr "National Day"

#. Nativity of Mary.
msgid "Mariä Geburt"
msgstr "Nativity of Mary"

#. All Saints' Day.
msgid "Allerheiligen"
msgstr "All Saints' Day"

#. Immaculate Conception.
msgid "Mariä Empfängnis"
msgstr "Immaculate Conception"

#. Christmas Eve.
msgid "Heiligabend"
msgstr "Christmas Eve"

#. Christmas Day.
msgid "Weihnachten"
msgstr "Christmas Day"

#. St. Stephen's Day.
msgid "Stephanstag"
msgstr "St. Stephen's Day"

#. New Year's Eve.
msgid "Silvester"
msgstr "New Year's Eve"
