# Lithuania holidays en_US localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.28\n"
"POT-Creation-Date: 2023-06-27 13:17+0300\n"
"PO-Revision-Date: 2024-01-07 16:02+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: Lingua 4.15.0\n"
"X-Generator: Poedit 3.4\n"

#. New Year's Day.
msgid "Naujųjų metų diena"
msgstr "New Year's Day"

#. Day of Restoration of the State of Lithuania.
msgid "Lietuvos valstybės atkūrimo diena"
msgstr "Day of Restoration of the State of Lithuania"

#. Day of Restoration of Independence of Lithuania.
msgid "Lietuvos nepriklausomybės atkūrimo diena"
msgstr "Day of Restoration of Independence of Lithuania"

#. Easter Sunday.
msgid "Šv. Velykos"
msgstr "Easter Sunday"

#. Easter Monday.
msgid "Antroji šv. Velykų diena"
msgstr "Easter Monday"

#. International Workers' Day.
msgid "Tarptautinė darbo diena"
msgstr "International Workers' Day"

#. Day of Dew and Saint John.
msgid "Rasos ir Joninių diena"
msgstr "Day of Dew and Saint John"

#. Statehood Day.
msgid ""
"Valstybės (Lietuvos karaliaus Mindaugo karūnavimo) ir Tautiškos giesmės "
"diena"
msgstr "Statehood Day"

#. Assumption Day.
msgid "Žolinė (Švč. Mergelės Marijos ėmimo į dangų diena)"
msgstr "Assumption Day"

#. All Saints' Day.
msgid "Visų Šventųjų diena"
msgstr "All Saints' Day"

#. All Souls' Day.
msgid "Mirusiųjų atminimo (Vėlinių) diena"
msgstr "All Souls' Day"

#. Christmas Eve.
msgid "Kūčių diena"
msgstr "Christmas Eve"

#. Christmas Day.
msgid "Šv. Kalėdų pirma diena"
msgstr "Christmas Day"

#. Second Day of Christmas.
msgid "Šv. Kalėdų antra diena"
msgstr "Second Day of Christmas"

#. Mother's Day.
msgid "Motinos diena"
msgstr "Mother's Day"

#. Father's Day.
msgid "Tėvo diena"
msgstr "Father's Day"
