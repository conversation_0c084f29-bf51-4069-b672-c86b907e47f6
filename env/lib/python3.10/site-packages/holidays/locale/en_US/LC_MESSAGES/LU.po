# Luxembourg holidays en_US localization.
# <AUTHOR> <EMAIL>, (c) 2023.
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.28\n"
"POT-Creation-Date: 2023-06-27 13:17+0300\n"
"PO-Revision-Date: 2023-06-27 13:53+0300\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: Lingua 4.15.0\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "Neijoerschdag"
msgstr "New Year's Day"

#. Easter Monday.
msgid "Ouschterméindeg"
msgstr "Easter Monday"

#. Labor Day.
msgid "Dag vun der Aarbecht"
msgstr "Labor Day"

#. Europe Day.
msgid "Europadag"
msgstr "Europe Day"

#. Ascension Day.
msgid "Christi Himmelfaart"
msgstr "Ascension Day"

#. Whit Monday.
msgid "Péngschtméindeg"
msgstr "Whit Monday"

#. National Day.
msgid "Nationalfeierdag"
msgstr "National Day"

#. Assumption Day.
msgid "Léiffrawëschdag"
msgstr "Assumption Day"

#. All Saints' Day.
msgid "Allerhellgen"
msgstr "All Saints' Day"

#. Christmas Day.
msgid "Chrëschtdag"
msgstr "Christmas Day"

#. St. Stephen's Day.
msgid "Stiefesdag"
msgstr "St. Stephen's Day"
