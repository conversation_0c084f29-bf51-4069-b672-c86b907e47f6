# Morocco holidays en_US localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.29\n"
"POT-Creation-Date: 2023-06-28 00:13+0100\n"
"PO-Revision-Date: 2023-07-11 03:44+0100\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: Lingua 4.15.0\n"
"X-Generator: Poedit 3.3.2\n"

#. %s (estimated).
#, c-format
msgid "(تقدير) %s"
msgstr "%s (estimated)"

#. New Year's Day.
msgid "رأس السنة الميلادية"
msgstr "New Year's Day"

#. Proclamation of Independence Day.
msgid "ذكرى تقديم وثيقة الاستقلال"
msgstr "Proclamation of Independence Day"

#. Amazigh New Year.
msgid "رأس السنة الأمازيغية"
msgstr "Amazigh New Year"

#. Labor Day.
msgid "عيد العمال"
msgstr "Labor Day"

#. Throne day.
msgid "عيد العرش"
msgstr "Throne Day"

#. Oued Ed-Dahab Day.
msgid "ذكرى استرجاع إقليم وادي الذهب"
msgstr "Oued Ed-Dahab Day"

#. Revolution Day.
msgid "ذكرى ثورة الملك و الشعب"
msgstr "Revolution Day"

#. Youth Day.
msgid "عيد الشباب"
msgstr "Youth Day"

#. Green March.
msgid "ذكرى المسيرة الخضراء"
msgstr "Green March"

#. Independence Day.
msgid "عيد الإستقلال"
msgstr "Independence Day"

#. Eid al-Fitr.
msgid "عيد الفطر"
msgstr "Eid al-Fitr"

#. Eid al-Adha.
msgid "عيد الأضحى"
msgstr "Eid al-Adha"

#. Islamic New Year.
msgid "رأس السنة الهجرية"
msgstr "Islamic New Year"

#. Prophet's Birthday.
msgid "عيد المولد النبوي"
msgstr "Prophet's Birthday"
