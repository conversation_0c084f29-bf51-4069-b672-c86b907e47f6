# Monaco holidays en_US localization.
# <AUTHOR> <EMAIL>, (c) 2023.
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.41\n"
"POT-Creation-Date: 2023-02-20 11:58+0200\n"
"PO-Revision-Date: 2024-01-02 15:44+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: Lingua 4.15.0\n"
"X-Generator: Poedit 3.4\n"

#. Public holiday.
msgid "Jour férié"
msgstr "Public holiday"

#. %s (observed).
#, c-format
msgid "%s (observé)"
msgstr "%s (observed)"

#. New Year's Day.
msgid "Le jour de l'An"
msgstr "New Year's Day"

#. Saint Devote's Day.
msgid "La Sainte Dévote"
msgstr "Saint Devote's Day"

#. Easter Monday.
msgid "Le lundi de Pâques"
msgstr "Easter Monday"

#. Labor Day.
msgid "Fête de la Travaille"
msgstr "Labor Day"

#. Ascension Day.
msgid "L'Ascension"
msgstr "Ascension Day"

#. Whit Monday.
msgid "Le lundi de Pentecôte"
msgstr "Whit Monday"

#. Corpus Christi.
msgid "La Fête Dieu"
msgstr "Corpus Christi"

#. Assumption Day.
msgid "L'Assomption de Marie"
msgstr "Assumption Day"

#. All Saints' Day.
msgid "La Toussaint"
msgstr "All Saints' Day"

#. Prince's Day.
msgid "La Fête du Prince"
msgstr "Prince's Day"

#. Immaculate Conception.
msgid "L'Immaculée Conception"
msgstr "Immaculate Conception"

#. Christmas Day.
msgid "Noël"
msgstr "Christmas Day"
