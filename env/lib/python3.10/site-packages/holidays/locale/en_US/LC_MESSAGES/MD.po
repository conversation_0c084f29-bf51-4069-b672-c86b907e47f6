# Moldova holidays en_US localization.
# <AUTHOR> <EMAIL>, (c) 2023.
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.22\n"
"POT-Creation-Date: 2023-03-22 21:58+0200\n"
"PO-Revision-Date: 2024-01-05 12:40+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.4\n"

#. New Year's Day.
msgid "Anul Nou"
msgstr "New Year's Day"

#. Christmas Day (by old style).
msgid "Naşterea lui Iisus <PERSON> (Crăciunul pe stil vechi)"
msgstr "Christmas Day (by old style)"

#. Christmas Day.
msgid "Naşterea lui Iis<PERSON> (Crăciunul)"
msgstr "Christmas Day"

#. International Women's Day.
msgid "Ziua internatională a femeii"
msgstr "International Women's Day"

#. Easter.
msgid "Paştele"
msgstr "Easter"

#. Day of Rejoicing.
msgid "Paştele blajinilor"
msgstr "Day of Rejoicing"

#. International Workers' Solidarity Day.
msgid "Ziua internaţională a solidarităţii oamenilor muncii"
msgstr "International Workers' Solidarity Day"

#. Victory Day and Commemoration of the heroes fallen for Independence of
#. Fatherland.
msgid ""
"Ziua Victoriei şi a comemorării eroilor căzuţi pentru Independenţa Patriei"
msgstr ""
"Victory Day and Commemoration of the heroes fallen for Independence of "
"Fatherland"

#. Europe Day.
msgid "Ziua Europei"
msgstr "Europe Day"

#. International Children's Day.
msgid "Ziua Ocrotirii Copilului"
msgstr "International Children's Day"

#. Republic of Moldova Independence Day.
msgid "Ziua independenţei Republicii Moldova"
msgstr "Republic of Moldova Independence Day"

#. National Language Day.
msgid "Limba noastră"
msgstr "National Language Day"

#. Christmas Day (by new style).
msgid "Naşterea lui Iisus Hristos (Crăciunul pe stil nou)"
msgstr "Christmas Day (by new style)"
