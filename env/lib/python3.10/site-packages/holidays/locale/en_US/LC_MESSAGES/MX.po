# Mexico holidays en_US localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.42\n"
"POT-Creation-Date: 2023-03-05 12:33+0200\n"
"PO-Revision-Date: 2024-01-23 20:55+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.4\n"

#. New Year's Day.
msgid "Año Nuevo"
msgstr "New Year's Day"

#. Constitution Day.
msgid "Día de la Constitución"
msgstr "Constitution Day"

#. <PERSON>'s birthday.
msgid "<PERSON><PERSON><PERSON> <PERSON>"
msgstr "<PERSON>'s birthday"

#. Labor Day.
msgid "Día del Trabajo"
msgstr "Labor Day"

#. Independence Day.
msgid "Día de la Independencia"
msgstr "Independence Day"

#. Revolution Day.
msgid "Día de la Revolución"
msgstr "Revolution Day"

#. Change of Federal Government.
msgid "Transmisión del Poder Ejecutivo Federal"
msgstr "Change of Federal Government"

#. Christmas Day.
msgid "Navidad"
msgstr "Christmas Day"
