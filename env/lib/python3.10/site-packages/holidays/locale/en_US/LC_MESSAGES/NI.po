# Nicaragua holidays en_US localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.27\n"
"POT-Creation-Date: 2023-03-05 12:23+0200\n"
"PO-Revision-Date: 2024-01-05 12:43+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.4\n"

#. New Year's Day.
msgid "Año Nuevo"
msgstr "New Year's Day"

#. Maundy Thursday.
msgid "Jueves Santo"
msgstr "Maundy Thursday"

#. Good Friday.
msgid "Viernes Santo"
msgstr "Good Friday"

#. Labor Day.
msgid "Día del Trabajo"
msgstr "Labor Day"

#. Revolution Day.
msgid "Día de la Revolución"
msgstr "Revolution Day"

#. Battle of San Jacinto Day.
msgid "Batalla de San Jacinto"
msgstr "Battle of San Jacinto Day"

#. Independence Day.
msgid "Día de la Independencia"
msgstr "Independence Day"

#. Virgin's Day.
msgid "Concepción de María"
msgstr "Virgin's Day"

#. Christmas Day.
msgid "Navidad"
msgstr "Christmas Day"

#. Descent of Saint Dominic.
msgid "Bajada de Santo Domingo"
msgstr "Descent of Saint Dominic"

#. Ascent of Saint Dominic.
msgid "Subida de Santo Domingo"
msgstr "Ascent of Saint Dominic"
