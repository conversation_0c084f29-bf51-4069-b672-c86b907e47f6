# Netherlands holidays en_US localization.
# <AUTHOR> <EMAIL>, (c) 2023.
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.23\n"
"POT-Creation-Date: 2023-04-09 18:11+0300\n"
"PO-Revision-Date: 2023-04-09 18:15+0300\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "Nieuwjaarsdag"
msgstr "New Year's Day"

#. Good Friday.
msgid "Goede Vrijdag"
msgstr "Good Friday"

#. Easter Sunday.
msgid "Eerste paasdag"
msgstr "Easter Sunday"

#. Easter Monday.
msgid "Tweede paasdag"
msgstr "Easter Monday"

#. King's Day.
msgid "Koningsdag"
msgstr "King's Day"

#. Queen's Day.
msgid "Koninginnedag"
msgstr "Queen's Day"

#. Liberation Day.
msgid "Bevrijdingsdag"
msgstr "Liberation Day"

#. Ascension Day.
msgid "Hemelvaartsdag"
msgstr "Ascension Day"

#. Whit Sunday.
msgid "Eerste Pinksterdag"
msgstr "Whit Sunday"

#. Whit Monday.
msgid "Tweede Pinksterdag"
msgstr "Whit Monday"

#. Christmas Day.
msgid "Eerste Kerstdag"
msgstr "Christmas Day"

#. Second Day of Christmas.
msgid "Tweede Kerstdag"
msgstr "Second Day of Christmas"
