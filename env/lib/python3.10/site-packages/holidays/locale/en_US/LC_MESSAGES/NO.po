# Norway holidays en_US localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.23\n"
"POT-Creation-Date: 2023-04-09 13:25+0300\n"
"PO-Revision-Date: 2023-04-09 13:31+0300\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "Første nyttårsdag"
msgstr "New Year's Day"

#. Maundy Thursday.
msgid "Skjærtorsdag"
msgstr "Maundy Thursday"

#. Good Friday.
msgid "Langfredag"
msgstr "Good Friday"

#. Easter Sunday.
msgid "Første påskedag"
msgstr "Easter Sunday"

#. Easter Monday.
msgid "Andre påskedag"
msgstr "Easter Monday"

#. Labor Day.
msgid "Arbeidernes dag"
msgstr "Labor Day"

#. Constitution Day.
msgid "Grunnlovsdag"
msgstr "Constitution Day"

#. Ascension Day.
msgid "Kristi himmelfartsdag"
msgstr "Ascension Day"

#. Whit Sunday.
msgid "Første pinsedag"
msgstr "Whit Sunday"

#. Whit Monday.
msgid "Andre pinsedag"
msgstr "Whit Monday"

#. Christmas Day.
msgid "Første juledag"
msgstr "Christmas Day"

#. Second Day of Christmas.
msgid "Andre juledag"
msgstr "Second Day of Christmas"

#. Sunday.
msgid "Søndag"
msgstr "Sunday"
