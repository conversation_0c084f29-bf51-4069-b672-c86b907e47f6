# Peru holidays en_US localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.42\n"
"POT-Creation-Date: 2023-02-21 15:18+0200\n"
"PO-Revision-Date: 2024-01-23 20:56+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: Lingua 4.15.0\n"
"X-Generator: Poedit 3.4\n"

#. New Year's Day.
msgid "Año Nuevo"
msgstr "New Year's Day"

#. Maundy Thursday.
msgid "<PERSON><PERSON>"
msgstr "Maundy Thursday"

#. Good Friday.
msgid "Viernes Santo"
msgstr "Good Friday"

#. Easter Sunday.
msgid "Domingo de Resurrección"
msgstr "Easter Sunday"

#. Labor Day.
msgid "Día del Trabajo"
msgstr "Labor Day"

#. Saint Peter and Saint Paul.
msgid "San Pedro y San Pablo"
msgstr "Saint Peter and Saint Paul"

#. Independence Day.
msgid "Día de la Independencia"
msgstr "Independence Day"

#. Great Military Parade Day.
msgid "Día de la Gran Parada Militar"
msgstr "Great Military Parade Day"

#. Battle of Junín.
msgid "Batalla de Junín"
msgstr "Battle of Junín Day"

#. Rose of Lima Day.
msgid "Santa Rosa de Lima"
msgstr "Rose of Lima Day"

#. Battle of Angamos.
msgid "Combate de Angamos"
msgstr "Battle of Angamos Day"

#. All Saints' Day.
msgid "Todos Los Santos"
msgstr "All Saints' Day"

#. Immaculate Conception.
msgid "Inmaculada Concepción"
msgstr "Immaculate Conception Day"

#. Battle of Ayacucho.
msgid "Batalla de Ayacucho"
msgstr "Battle of Ayacucho Day"

#. Christmas Day.
msgid "Navidad del Señor"
msgstr "Christmas Day"
