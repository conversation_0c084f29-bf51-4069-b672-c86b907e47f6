# Romania holidays en_US localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.42\n"
"POT-Creation-Date: 2023-03-22 18:35+0200\n"
"PO-Revision-Date: 2024-01-23 21:09+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.4\n"

#. New Year's Day.
msgid "Anul Nou"
msgstr "New Year's Day"

#. Epiphany.
msgid "Bobotează"
msgstr "Epiphany"

#. Saint <PERSON> the Baptist.
msgid "<PERSON><PERSON><PERSON><PERSON>l Ion"
msgstr "Saint John the Baptist"

#. Unification of the Romanian Principalities Day.
msgid "Ziua Unirii Principatelor Române"
msgstr "Unification of the Romanian Principalities Day"

#. Easter.
msgid "Paștele"
msgstr "Easter"

#. Labor Day.
msgid "Ziua Muncii"
msgstr "Labor Day"

#. Children's Day.
msgid "Ziua Copilului"
msgstr "Children's Day"

#. Pentecost.
msgid "Rusaliile"
msgstr "Pentecost"

#. Dormition of the Mother of God.
msgid "Adormirea Maicii Domnului"
msgstr "Dormition of the Mother of God"

#. Saint Andrew's Day.
msgid "Sfantul Apostol Andrei cel Intai chemat"
msgstr "Saint Andrew's Day"

#. National Day.
msgid "Ziua Națională a României"
msgstr "National Day"

#. Christmas Day.
msgid "Crăciunul"
msgstr "Christmas Day"
