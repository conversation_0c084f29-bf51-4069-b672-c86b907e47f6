# Russia holidays en_US localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.29\n"
"POT-Creation-Date: 2023-02-15 08:13-0800\n"
"PO-Revision-Date: 2023-07-13 15:58+0300\n"
"Last-Translator: ~Jhell<PERSON> <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: Lingua 4.15.0\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "Новый год"
msgstr "New Year's Day"

#. New Year Holidays.
msgid "Новогодние каникулы"
msgstr "New Year Holidays"

#. Christmas Day.
msgid "Рождество Христово"
msgstr "Christmas Day"

#. Defender of the Fatherland Day.
msgid "День защитника Отечества"
msgstr "Fatherland Defender's Day"

#. International Women's Day.
msgid "Международный женский день"
msgstr "International Women's Day"

#. Holiday of Spring and Labor.
msgid "Праздник Весны и Труда"
msgstr "Holiday of Spring and Labor"

#. International Workers' Solidarity Day.
msgid "День международной солидарности трудящихся"
msgstr "International Workers' Solidarity Day"

#. Victory Day.
msgid "День Победы"
msgstr "Victory Day"

#. Russia Day.
msgid "День России"
msgstr "Russia Day"

#. Day of the Adoption of the Declaration of Sovereignty of the Russian
#. Federation.
msgid ""
"День принятия Декларации о государственном суверенитете Российской Федерации"
msgstr ""
"Day of the Adoption of the Declaration of Sovereignty of the Russian "
"Federation"

#. Unity Day.
msgid "День народного единства"
msgstr "Unity Day"

#. Day of consent and reconciliation.
msgid "День согласия и примирения"
msgstr "Day of consent and reconciliation"

#. Anniversary of the Great October Socialist Revolution.
msgid "Годовщина Великой Октябрьской социалистической революции"
msgstr "Anniversary of the Great October Socialist Revolution"
