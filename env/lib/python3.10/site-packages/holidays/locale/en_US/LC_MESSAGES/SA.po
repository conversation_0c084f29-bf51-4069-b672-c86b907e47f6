# Saudi Arabia holidays en_US localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.41\n"
"POT-Creation-Date: 2023-08-04 20:02+0300\n"
"PO-Revision-Date: 2023-08-04 20:03+0300\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: Lingua 4.15.0\n"
"X-Generator: Poedit 3.2.2\n"

#. %s (estimated).
#, c-format
msgid "(تقدير) %s"
msgstr "%s (estimated)"

#. %s (observed).
#, c-format
msgid "(ملاحظة) %s"
msgstr "%s (observed)"

#. Celebrate the country's win against Argentina in the World Cup
msgid "يوم وطني"
msgstr "A National Day"

#. Eid al-Fitr Holiday
msgid "عطلة عيد الفطر"
msgstr "Eid al-Fitr Holiday"

#. Arafat Day
msgid "يوم عرفة"
msgstr "Arafat Day"

#. Eid al-Adha Holiday
msgid "عطلة عيد الأضحى"
msgstr "Eid al-Adha Holiday"

#. National Day Holiday
msgid "اليوم الوطني"
msgstr "National Day Holiday"

#. Founding Day
msgid "يوم التأسيسي"
msgstr "Founding Day Holiday"

#. %s (observed, estimated).
#, c-format
msgid "(تقدير ملاحظة) %s"
msgstr "%s (observed, estimated)"
