# Sweden holidays en_US localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.23\n"
"POT-Creation-Date: 2023-04-08 20:17+0300\n"
"PO-Revision-Date: 2023-04-08 20:23+0300\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "Nyårsdagen"
msgstr "New Year's Day"

#. Epiphany.
msgid "Trettondedag jul"
msgstr "Epiphany"

#. Feast of the Annunciation.
msgid "<PERSON><PERSON><PERSON> <PERSON> be<PERSON>"
msgstr "Feast of the Annunciation"

#. Good Friday.
msgid "Långfredagen"
msgstr "Good Friday"

#. Easter Sunday.
msgid "Påskdagen"
msgstr "Easter Sunday"

#. Easter Monday.
msgid "Annandag påsk"
msgstr "Easter Monday"

#. May Day.
msgid "Första maj"
msgstr "May Day"

#. Ascension Day.
msgid "Kristi himmelsfärdsdag"
msgstr "Ascension Day"

#. National Day of Sweden.
msgid "Sveriges nationaldag"
msgstr "National Day of Sweden"

#. Whit Sunday.
msgid "Pingstdagen"
msgstr "Whit Sunday"

#. Whit Monday.
msgid "Annandag pingst"
msgstr "Whit Monday"

#. Midsummer Eve.
msgid "Midsommarafton"
msgstr "Midsummer Eve"

#. Midsummer Day.
msgid "Midsommardagen"
msgstr "Midsummer Day"

#. All Saints' Day.
msgid "Alla helgons dag"
msgstr "All Saints' Day"

#. Christmas Eve.
msgid "Julafton"
msgstr "Christmas Eve"

#. Christmas Day.
msgid "Juldagen"
msgstr "Christmas Day"

#. Second Day of Christmas.
msgid "Annandag jul"
msgstr "Second Day of Christmas"

#. New Year's Eve.
msgid "Nyårsafton"
msgstr "New Year's Eve"

#. Sunday.
msgid "Söndag"
msgstr "Sunday"
