# Turkey holidays en_US localization.
# <AUTHOR> <EMAIL>, (c) 2023.
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.37\n"
"POT-Creation-Date: 2023-11-12 18:52+0200\n"
"PO-Revision-Date: 2023-11-14 12:56+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.4\n"

#. %s (estimated).
#, c-format
msgid "%s (tahmini)"
msgstr "%s (estimated)"

#. New Year's Day.
msgid "Yılbaşı"
msgstr "New Year's Day"

#. National Sovereignty and Children's Day.
msgid "Ulusal Egemenlik ve Çocuk Bayramı"
msgstr "National Sovereignty and Children's Day"

#. National Sovereignty Day.
msgid "Ulusal Egemenlik Bayramı"
msgstr "National Sovereignty Day"

#. Labour and Solidarity Day.
msgid "Emek ve Dayanışma Günü"
msgstr "Labour and Solidarity Day"

#. Commemoration of Atatürk, Youth and Sports Day.
msgid "Atatürk'ü Anma, Gençlik ve Spor Bayramı"
msgstr "Commemoration of Atatürk, Youth and Sports Day"

#. Youth and Sports Day.
msgid "Gençlik ve Spor Bayramı"
msgstr "Youth and Sports Day"

#. Freedom and Constitution Day.
msgid "Hürriyet ve Anayasa Bayramı"
msgstr "Freedom and Constitution Day"

#. Democracy and National Unity Day.
msgid "Demokrasi ve Millî Birlik Günü"
msgstr "Democracy and National Unity Day"

#. Victory Day.
msgid "Zafer Bayramı"
msgstr "Victory Day"

#. Republic Day.
msgid "Cumhuriyet Bayramı"
msgstr "Republic Day"

#. Eid al-Fitr.
msgid "Ramazan Bayramı"
msgstr "Eid al-Fitr"

#. Eid al-Adha.
msgid "Kurban Bayramı"
msgstr "Eid al-Adha"

#. %s (from 1pm).
#, c-format
msgid "%s (saat 13.00'ten)"
msgstr "%s (from 1pm)"

#. Public holiday.
msgid "Genel tati̇l"
msgstr "Public holiday"
