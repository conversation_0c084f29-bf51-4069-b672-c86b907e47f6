# Uzbekistan holidays en_US localization.
# <AUTHOR> <EMAIL>, (c) 2024.
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.41\n"
"POT-Creation-Date: 2023-12-31 21:12+0200\n"
"PO-Revision-Date: 2024-01-02 12:46+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays Localization Team\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: Lingua 4.15.0\n"
"X-Generator: Poedit 3.4\n"

#. New Year's Day.
msgid "Yangi yil"
msgstr "New Year's Day"

#. Women's Day.
msgid "Xotin-qizlar kuni"
msgstr "Women's Day"

#. Nowruz.
msgid "Navro‘z bayrami"
msgstr "Nowruz"

#. Day of Memory and Honor.
msgid "Xotira va qadrlash kuni"
msgstr "Day of Memory and Honor"

#. Victory Day.
msgid "G‘alaba kuni"
msgstr "Victory Day"

#. Independence Day.
msgid "Mustaqillik kuni"
msgstr "Independence Day"

#. Teachers and Instructors Day.
msgid "O‘qituvchi va murabbiylar kuni"
msgstr "Teachers and Instructors Day"

#. Constitution Day.
msgid "O‘zbekiston Respublikasi Konstitutsiyasi kuni"
msgstr "Constitution Day"

#. Eid al-Fitr.
msgid "Ro‘za hayit"
msgstr "Eid al-Fitr"

#. Eid al-Adha.
msgid "Qurbon hayit"
msgstr "Eid al-Adha"

#. %s (estimated).
#, c-format
msgid "%s (taxminiy)"
msgstr "%s (estimated)"

#. %s (observed).
#, c-format
msgid "%s (ko‘chirilgan)"
msgstr "%s (observed)"

#. Date format (see strftime() Format Codes)
msgid "%d/%m %Y"
msgstr "%m/%d/%Y"

#. Day off (substituted from %s).
#, c-format
msgid "Dam olish kuni (%s dan ko‘chirilgan)"
msgstr "Day off (substituted from %s)"

#. Additional day off by Presidential decree.
msgid "Prezidentining farmoni bilan qo‘shimcha dam olish kuni"
msgstr "Additional day off by Presidential decree"

#. %s (observed, estimated).
#, c-format
msgid "%s (ko‘chirilgan, taxminiy)"
msgstr "%s (observed, estimated)"
