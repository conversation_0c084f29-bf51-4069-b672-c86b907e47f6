# Venezuela holidays en_US localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.22\n"
"POT-Creation-Date: 2023-03-05 12:23+0200\n"
"PO-Revision-Date: 2023-03-05 12:58+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "Año Nuevo"
msgstr "New Year's Day"

#. Monday of Carnival.
msgid "Lunes de Carnaval"
msgstr "Monday of Carnival"

#. Tuesday of Carnival.
msgid "Martes de Carnaval"
msgstr "Tuesday of Carnival"

#. Maundy Thursday.
msgid "<PERSON>eves Santo"
msgstr "Maundy Thursday"

#. Good Friday.
msgid "Viernes Santo"
msgstr "Good Friday"

#. Declaration of Independence.
msgid "Declaración de la Independencia"
msgstr "Declaration of Independence"

#. International Worker's Day.
msgid "Dia Mundial del Trabajador"
msgstr "International Worker's Day"

#. Battle of Carabobo.
msgid "Batalla de Carabobo"
msgstr "Battle of Carabobo"

#. Independence Day.
msgid "Día de la Independencia"
msgstr "Independence Day"

#. Birthday of Simon Bolivar.
msgid "Natalicio de Simón Bolívar"
msgstr "Birthday of Simon Bolivar"

#. Day of Indigenous Resistance.
msgid "Día de la Resistencia Indígena"
msgstr "Day of Indigenous Resistance"

#. Columbus Day.
msgid "Día de la Raza"
msgstr "Columbus Day"

#. Unknown Holiday.
msgid "Día Festivo Desconocido"
msgstr "Unknown Holiday"

#. Christmas Eve.
msgid "Nochebuena"
msgstr "Christmas Eve"

#. Christmas Day.
msgid "Día de Navidad"
msgstr "Christmas Day"

#. New Year's Eve.
msgid "Fiesta de Fin de Año"
msgstr "New Year's Eve"
