Metadata-Version: 2.1
Name: maxminddb
Version: 2.5.2
Summary: Reader for the MaxMind DB format
Author-email: <PERSON> <go<PERSON><PERSON>@maxmind.com>
License: Apache License, Version 2.0
Project-URL: Homepage, https://www.maxmind.com/
Project-URL: Documentation, https://maxminddb.readthedocs.org/
Project-URL: Source Code, https://github.com/maxmind/MaxMind-DB-Reader-python
Project-URL: Issue Tracker, https://github.com/maxmind/MaxMind-DB-Reader-python/issues
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Web Environment
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: System Administrators
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Internet
Classifier: Topic :: Internet :: Proxy Servers
Requires-Python: >=3.8
Description-Content-Type: text/x-rst
License-File: LICENSE
Requires-Dist: setuptools >=68.2.2

========================
MaxMind DB Python Module
========================

Description
-----------

This is a Python module for reading MaxMind DB files. The module includes both
a pure Python reader and an optional C extension.

MaxMind DB is a binary file format that stores data indexed by IP address
subnets (IPv4 or IPv6).

Installation
------------

To install maxminddb, type:

.. code-block:: bash

    $ pip install maxminddb

If you are not able to use pip, you may also use easy_install from the
source directory:

.. code-block:: bash

    $ easy_install .

The installer will attempt to build the C extension. If this fails, the
module will fall-back to the pure Python implementation.

Usage
-----

To use this module, you must first download or create a MaxMind DB file. We
provide `free GeoLite2 databases
<https://dev.maxmind.com/geoip/geolocate-an-ip/databases?lang=en>`_. These
files must be decompressed with ``gunzip``.

After you have obtained a database and imported the module, call
``open_database`` with a path, or file descriptor (in the case of ``MODE_FD``),
to the database as the first argument. Optionally, you may pass a mode as the
second argument. The modes are exported from ``maxminddb``. Valid modes are:

* ``MODE_MMAP_EXT`` - use the C extension with memory map.
* ``MODE_MMAP`` - read from memory map. Pure Python.
* ``MODE_FILE`` - read database as standard file. Pure Python.
* ``MODE_MEMORY`` - load database into memory. Pure Python.
* ``MODE_FD`` - load database into memory from a file descriptor. Pure Python.
* ``MODE_AUTO`` - try ``MODE_MMAP_EXT``, ``MODE_MMAP``, ``MODE_FILE`` in that
  order. Default.

**NOTE**: When using ``MODE_FD``, it is the *caller's* responsibility to be
sure that the file descriptor gets closed properly. The caller may close the
file descriptor immediately after the ``Reader`` object is created.

The ``open_database`` function returns a ``Reader`` object. To look up an IP
address, use the ``get`` method on this object. The method will return the
corresponding values for the IP address from the database (e.g., a dictionary
for GeoIP2/GeoLite2 databases). If the database does not contain a record for
that IP address, the method will return ``None``.

If you wish to also retrieve the prefix length for the record, use the
``get_with_prefix_len`` method. This returns a tuple containing the record
followed by the network prefix length associated with the record.

You may also iterate over the whole database. The ``Reader`` class implements
the ``__iter__`` method that returns an iterator. This iterator yields a
tuple containing the network and the record.

Example
-------

.. code-block:: pycon

    >>> import maxminddb
    >>>
    >>> with maxminddb.open_database('GeoLite2-City.mmdb') as reader:
    >>>
    >>>     reader.get('*************')
    {'country': ... }
    >>>
    >>>     reader.get_with_prefix_len('*************')
    ({'country': ... }, 24)
    >>>
    >>>     for network, record in reader:
    >>>         ...

Exceptions
----------

The module will return an ``InvalidDatabaseError`` if the database is corrupt
or otherwise invalid. A ``ValueError`` will be thrown if you look up an
invalid IP address or an IPv6 address in an IPv4 database.

Requirements
------------

This code requires Python 3.8+. Older versions are not supported. The C
extension requires CPython.

Versioning
----------

The MaxMind DB Python module uses `Semantic Versioning <https://semver.org/>`_.

Support
-------

Please report all issues with this code using the `GitHub issue tracker
<https://github.com/maxmind/MaxMind-DB-Reader-python/issues>`_

If you are having an issue with a MaxMind service that is not specific to this
API, please contact `MaxMind support <https://www.maxmind.com/en/support>`_ for
assistance.
