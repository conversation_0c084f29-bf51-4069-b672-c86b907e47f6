Metadata-Version: 2.1
Name: mypy-extensions
Version: 1.0.0
Summary: Type system extensions for programs checked with the mypy type checker.
Home-page: https://github.com/python/mypy_extensions
Author: The mypy developers
Author-email: <EMAIL>
License: MIT License
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Topic :: Software Development
Requires-Python: >=3.5
License-File: LICENSE

Mypy Extensions
===============

The "mypy_extensions" module defines extensions to the standard "typing" module
that are supported by the mypy type checker and the mypyc compiler.
