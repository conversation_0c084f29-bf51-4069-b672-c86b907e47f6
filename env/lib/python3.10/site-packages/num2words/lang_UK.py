# -*- coding: utf-8 -*-
# Copyright (c) 2003, <PERSON><PERSON>.  All Rights Reserved.
# Copyright (c) 2013, Savoir-faire Linux inc.  All Rights Reserved.

# This library is free software; you can redistribute it and/or
# modify it under the terms of the GNU Lesser General Public
# License as published by the Free Software Foundation; either
# version 2.1 of the License, or (at your option) any later version.
# This library is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
# Lesser General Public License for more details.
# You should have received a copy of the GNU Lesser General Public
# License along with this library; if not, write to the Free Software
# Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston,
# MA 02110-1301 USA

from __future__ import unicode_literals

from .base import Num2Word_Base
from .utils import get_digits, splitbyx

ZERO = ('нуль',)

ONES_FEMININE = {
    1: ('одна',    "однієї",   "одній",    "одну",    "однією",    "одній"),
    2: ('дві',     "двох",     "двом",     "дві",     "двома",     "двох"),
    3: ('три',     "трьох",    "трьом",    "три",     "трьома",    "трьох"),
    4: ('чотири',  "чотирьох", "чотирьом", "чотири",  "чотирма",   "чотирьох"),
    5: ('п\'ять',  "п'яти",    "п'яти",    "п'ять",   "п'ятьма",   "п'яти"),
    6: ('шість',   "шести",    "шести",    "шість",   "шістьма",   "шести"),
    7: ('сім',     "семи",     "семи",     "сім",     "сьома",     "семи"),
    8: ('вісім',   "восьми",   "восьми",   "вісім",   "вісьма",    "восьми"),
    9: ("дев'ять", "дев'яти",  "дев'яти",  "дев'ять", "дев'ятьма", "дев'яти"),
}

ONES = {
    1: ('один',    'одного',   "одному",   "один",    "одним",     "одному"),
    2: ('два',     'двох',     "двом",     "два",     "двома",     "двох"),
    3: ('три',     'трьох',    "трьом",    "три",     "трьома",    "трьох"),
    4: ('чотири',  'чотирьох', "чотирьом", "чотири",  "чотирма",   "чотирьох"),
    5: ('п\'ять',  "п'яти",    "п'яти",    "п'ять",   "п'ятьма",   "п'яти"),
    6: ('шість',   'шести',    "шести",    "шість",   "шістьма",   "шести"),
    7: ('сім',     'семи',     "семи",     "сім",     "сьома",     "семи"),
    8: ('вісім',   'восьми',   "восьми",   "вісім",   "вісьма",    "восьми"),
    9: ("дев'ять", "дев'яти",  "дев'яти",  "дев'ять", "дев'ятьма", "дев'яти"),
}

ONES_ORDINALS = {
    1: ("перший", "одно"),
    2: ("другий", "двох"),
    3: ("третій", "трьох"),
    4: ("четвертий", "чотирьох"),
    5: ("п'ятий", "п'яти"),
    6: ("шостий", "шести"),
    7: ("сьомий", "семи"),
    8: ("восьмий", "восьми"),
    9: ("дев'ятий", "дев'яти"),
    10: ("десятий", "десяти"),
    11: ("одинадцятий", "одинадцяти"),
    12: ("дванадцятий", "дванадцяти"),
    13: ("тринадцятий", "тринадцяти"),
    14: ("чотирнадцятий", "чотирнадцяти"),
    15: ("п'ятнадцятий", "п'ятнадцяти"),
    16: ("шістнадцятий", "шістнадцяти"),
    17: ("сімнадцятий", "сімнадцяти"),
    18: ("вісімнадцятий", "вісімнадцяти"),
    19: ("дев'ятнадцятий", "дев'ятнадцяти"),
}
TENS = {
    0: ('десять',
        'десяти',
        "десяти",
        "десять",
        "десятьма",
        "десяти"),
    1: ('одинадцять',
        'одинадцяти',
        "одинадцяти",
        "одинадцять",
        "одинадцятьма",
        "одинадцяти"),
    2: ('дванадцять',
        'дванадцяти',
        "дванадцяти",
        "дванадцять",
        "дванадцятьма",
        "дванадцяти"),
    3: ('тринадцять',
        'тринадцяти',
        "тринадцяти",
        "тринадцять",
        "тринадцятьма",
        "тринадцяти"),
    4: ('чотирнадцять',
        'чотирнадцяти',
        "чотирнадцяти",
        "чотирнадцять",
        "чотирнадцятьма",
        "чотирнадцяти"),
    5: ("п'ятнадцять",
        "п'ятнадцяти",
        "п'ятнадцяти",
        "п'ятнадцять",
        "п'ятнадцятьма",
        "п'ятнадцяти"),
    6: ('шістнадцять',
        'шістнадцяти',
        "шістнадцяти",
        "шістнадцять",
        "шістнадцятьма",
        "шістнадцяти"),
    7: ('сімнадцять',
        'сімнадцяти',
        "сімнадцяти",
        "сімнадцять",
        "сімнадцятьма",
        "сімнадцяти"),
    8: ('вісімнадцять',
        'вісімнадцяти',
        "вісімнадцяти",
        "вісімнадцять",
        "вісімнадцятьма",
        "вісімнадцяти"),
    9: ("дев'ятнадцять",
        "дев'ятнадцяти",
        "дев'ятнадцяти",
        "дев'ятнадцять",
        "дев'ятнадцятьма",
        "дев'ятнадцяти"),
}

TWENTIES = {
    2: ('двадцять',
        "двадцяти",
        "двадцяти",
        "двадцять",
        "двадцятьма",
        "двадцяти"),
    3: ('тридцять',
        "тридцяти",
        "тридцяти",
        "тридцять",
        "тридцятьма",
        "тридцяти"),
    4: ('сорок',
        "сорока",
        "сорока",
        "сорок",
        "сорока",
        "сорока"),
    5: ('п\'ятдесят',
        "п'ятдесяти",
        "п'ятдесяти",
        "п'ятдесят",
        "п'ятдесятьма",
        "п'ятдесяти"),
    6: ('шістдесят',
        "шістдесяти",
        "шістдесяти",
        "шістдесят",
        "шістдесятьма",
        "шістдесяти"),
    7: ('сімдесят',
        "сімдесяти",
        "сімдесяти",
        "сімдесят",
        "сімдесятьма",
        "сімдесяти"),
    8: ('вісімдесят',
        "вісімдесяти",
        "вісімдесяти",
        "вісімдесят",
        "вісімдесятьма",
        "вісімдесяти"),
    9: ('дев\'яносто',
        "дев'яноста",
        "дев'яноста",
        "дев'яносто",
        "дев'яностами",
        "дев'яноста"),
}

TWENTIES_ORDINALS = {
    2: ("двадцятий", "двадцяти"),
    3: ("тридцятий", "тридцяти"),
    4: ("сороковий", "сорока"),
    5: ("п'ятдесятий", "п'ятдесяти"),
    6: ("шістдесятий", "шістдесяти"),
    7: ("сімдесятий", "сімдесяти"),
    8: ("вісімдесятий", "вісімдесяти"),
    9: ("дев'яностий", "дев'яности"),
}

HUNDREDS = {
    1: ('сто',
        "ста",
        "ста",
        "сто",
        "стами",
        "стах"),
    2: ('двісті',
        "двохста",
        "двомстам",
        "двісті",
        "двомастами",
        "двохстах"),
    3: ('триста',
        "трьохста",
        "трьомстам",
        "триста",
        "трьомастами",
        "трьохстах"),
    4: ('чотириста',
        "чотирьохста",
        "чотирьомстам",
        "чотириста",
        "чотирмастами",
        "чотирьохстах"),
    5: ('п\'ятсот',
        "п'ятиста",
        "п'ятистам",
        "п'ятсот",
        "п'ятьмастами",
        "п'ятистах"),
    6: ('шістсот',
        "шестиста",
        "шестистам",
        "шістсот",
        "шістьмастами",
        "шестистах"),
    7: ('сімсот',
        "семиста",
        "семистам",
        "сімсот",
        "сьомастами",
        "семистах"),
    8: ('вісімсот',
        "восьмиста",
        "восьмистам",
        "вісімсот",
        "восьмастами",
        "восьмистах"),
    9: ("дев'ятсот",
        "дев'ятиста",
        "дев'ятистам",
        "дев'ятсот",
        "дев'ятьмастами",
        "дев'ятистах"),
}

HUNDREDS_ORDINALS = {
    1: ("сотий", "сто"),
    2: ("двохсотий", "двохсот"),
    3: ("трьохсотий", "трьохсот"),
    4: ("чотирьохсотий", "чотирьохсот"),
    5: ("п'ятисотий", "п'ятсот"),
    6: ("шестисотий", "шістсот"),
    7: ("семисотий", "сімсот"),
    8: ("восьмисотий", "вісімсот"),
    9: ("дев'ятисотий", "дев'ятсот"),
}

THOUSANDS = {
    # 10^3
    1: (('тисяча', 'тисячі', 'тисяч'),
        ('тисячи', 'тисяч', 'тисяч'),
        ('тисячі', 'тисячам', 'тисячам'),
        ('тисячу', 'тисячі', 'тисяч'),
        ('тисячею', 'тисячами', 'тисячами'),
        ('тисячі', 'тисячах', 'тисячах'),),
    # 10^6
    2: (('мільйон', 'мільйони', 'мільйонів'),
        ('мільйона', 'мільйонів', 'мільйонів'),
        ('мільйону', 'мільйонам', 'мільйонам'),
        ('мільйон', 'мільйони', 'мільйонів'),
        ('мільйоном', 'мільйонами', 'мільйонів'),
        ('мільйоні', 'мільйонах', 'мільйонах'),),
    # 10^9
    3: (('мільярд', 'мільярди', 'мільярдів'),
        ('мільярда', 'мільярдів', 'мільярдів'),
        ('мільярду', 'мільярдам', 'мільярдам'),
        ('мільярд', 'мільярди', 'мільярдів'),
        ('мільярдом', 'мільярдами', 'мільярдів'),
        ('мільярді', 'мільярдах', 'мільярдах'),),
    # 10^12
    4: (('трильйон', 'трильйони', 'трильйонів'),
        ('трильйона', 'трильйонів', 'трильйонів'),
        ('трильйону', 'трильйонам', 'трильйонам'),
        ('трильйон', 'трильйони', 'трильйонів'),
        ('трильйоном', 'трильйонами', 'трильйонів'),
        ('трильйоні', 'трильйонах', 'трильйонах'),),
    # 10^15
    5: (('квадрильйон', 'квадрильйони', 'квадрильйонів'),
        ('квадрильйона', 'квадрильйонів', 'квадрильйонів'),
        ('квадрильйону', 'квадрильйонам', 'квадрильйонам'),
        ('квадрильйон', 'квадрильйони', 'квадрильйонів'),
        ('квадрильйоном', 'квадрильйонами', 'квадрильйонів'),
        ('квадрильйоні', 'квадрильйонах', 'квадрильйонах'),),
    # 10^18
    6: (('квінтильйон', 'квінтильйони', 'квінтильйонів'),
        ('квінтильйона', 'квінтильйонів', 'квінтильйонів'),
        ('квінтильйону', 'квінтильйонам', 'квінтильйонам'),
        ('квінтильйон', 'квінтильйони', 'квінтильйонів'),
        ('квінтильйоном', 'квінтильйонами', 'квінтильйонів'),
        ('квінтильйоні', 'квінтильйонах', 'квінтильйонах'),),
    # 10^21
    7: (('секстильйон', 'секстильйони', 'секстильйонів'),
        ('секстильйона', 'секстильйонів', 'секстильйонів'),
        ('секстильйону', 'секстильйонам', 'секстильйонам'),
        ('секстильйон', 'секстильйони', 'секстильйонів'),
        ('секстильйоном', 'секстильйонами', 'секстильйонів'),
        ('секстильйоні', 'секстильйонах', 'секстильйонах'),),
    # 10^24
    8: (('септильйон', 'септильйони', 'септильйонів'),
        ('септильйона', 'септильйонів', 'септильйонів'),
        ('септильйону', 'септильйонам', 'септильйонам'),
        ('септильйон', 'септильйони', 'септильйонів'),
        ('септильйоном', 'септильйонами', 'септильйонів'),
        ('септильйоні', 'септильйонах', 'септильйонах'),),
    # 10^27
    9: (('октильйон', 'октильйони', 'октильйонів'),
        ('октильйона', 'октильйонів', 'октильйонів'),
        ('октильйону', 'октильйонам', 'октильйонам'),
        ('октильйон', 'октильйони', 'октильйонів'),
        ('октильйоном', 'октильйонами', 'октильйонів'),
        ('октильйоні', 'октильйонах', 'октильйонах'),),
    # 10^30
    10: (('нонільйон', 'нонільйони', 'нонільйонів'),
         ('нонільйона', 'нонільйонів', 'нонільйонів'),
         ('нонільйону', 'нонільйонам', 'нонільйонам'),
         ('нонільйон', 'нонільйони', 'нонільйонів'),
         ('нонільйоном', 'нонільйонами', 'нонільйонів'),
         ('нонільйоні', 'нонільйонах', 'нонільйонах'),),
}

prefixes_ordinal = {
    1: "тисячний",
    2: "мільйонний",
    3: "мільярдний",
    4: "трильйонний",
    5: "квадрильйонний",
    6: "квінтильйонний",
    7: "секстильйонний",
    8: "септильйонний",
    9: "октильйонний",
    10: "нонільйонний",
}

FEMININE_MONEY = ('AOA', 'BAM', 'BDT', 'BWP', 'CZK', 'DKK',
                  'ERN', 'HNL', 'HRK', 'IDR', 'INR', 'ISK',
                  'JPY', 'KPW', 'KRW', 'LKR', 'MOP', 'MRU',
                  'MUR', 'MVR', 'MWK', 'NGN', 'NIO', 'NOK',
                  'NPR', 'PKR', 'SCR', 'SEK', 'STN', 'TRY',
                  'WST', 'UAH', 'ZMW')
FEMININE_CENTS = ('ALL', 'BDT', 'BGN', 'BYN', 'GHS', 'HRK',
                  'ILS', 'INR', 'NPR', 'OMR', 'OMR', 'PKR',
                  'RSD', 'RUB', 'UAH')

GENERIC_DOLLARS = ('долар', 'долари', 'доларів')
GENERIC_CENTS = ('цент', 'центи', 'центів')


class Num2Word_UK(Num2Word_Base):
    CURRENCY_FORMS = {
        'AED': (
            ('дирхам', 'дирхами', 'дирхамів'),
            ('філс', 'філси', 'філсів')
        ),
        'AFN': (
            ('афгані', 'афгані', 'афгані'),
            ('пул', 'пули', 'пулів')
        ),
        'ALL': (
            ('лек', 'леки', 'леків'),
            ('кіндарка', 'кіндарки', 'кіндарок')
        ),
        'AMD': (
            ('драм', 'драми', 'драмів'),
            ('лум', 'лум', 'лум')
        ),
        'ANG': (
            ('гульден', 'гульдени', 'гульденів'),
            GENERIC_CENTS
        ),
        'AOA': (
            ('кванза', 'кванзи', 'кванз'),
            ('сентимо', 'сентимо', 'сентимо')
        ),
        'ARS': (
            ('песо', 'песо', 'песо'),
            ('сентаво', 'сентаво', 'сентаво')
        ),
        'AUD': (GENERIC_DOLLARS, GENERIC_CENTS),
        'AWG': (
            ('флорин', 'флорини', 'флоринів'),
            GENERIC_CENTS
        ),
        'AZN': (
            ('манат', 'манати', 'манатів'),
            ('гяпік', 'гяпіки', 'гяпіків')
        ),
        'BAM': (
            ('марка', 'марки', 'марок'),
            ('фенінг', 'фенінги', 'фенінгів')
        ),
        'BBD': (GENERIC_DOLLARS, GENERIC_CENTS),
        'BDT': (
            ('така', 'таки', 'так'),
            ('пойша', 'пойші', 'пойш')
        ),
        'BGN': (
            ('лев', 'леви', 'левів'),
            ('стотинка', 'стотинки', 'стотинок')
        ),
        'BHD': (
            ('динар', 'динари', 'динарів'),
            ('філс', 'філси', 'філсів')
        ),
        'BIF': (
            ('франк', 'франки', 'франків'),
            ('сантим', 'сантими', 'сантимів')
        ),
        'BMD': (GENERIC_DOLLARS, GENERIC_CENTS),
        'BND': (GENERIC_DOLLARS, GENERIC_CENTS),
        'BOB': (
            ('болівіано', 'болівіано', 'болівіано'),
            ('сентаво', 'сентаво', 'сентаво')
        ),
        'BRL': (
            ('реал', 'реали', 'реалів'),
            ('сентаво', 'сентаво', 'сентаво')
        ),
        'BSD': (GENERIC_DOLLARS, GENERIC_CENTS),
        'BTN': (
            ('нгултрум', 'нгултруми', 'нгултрумів'),
            ('четрум', 'четруми', 'четрумів')
        ),
        'BWP': (
            ('пула', 'пули', 'пул'),
            ('тхебе', 'тхебе', 'тхебе')
        ),
        'BYN': (
            ('рубель', 'рублі', 'рублів'),
            ('копійка', 'копійки', 'копійок')
        ),
        'BZD': (GENERIC_DOLLARS, GENERIC_CENTS),
        'CAD': (GENERIC_DOLLARS, GENERIC_CENTS),
        'CDF': (
            ('франк', 'франки', 'франків'),
            ('сантим', 'сантими', 'сантимів')
        ),
        'CHF': (
            ('франк', 'франки', 'франків'),
            ('сантим', 'сантими', 'сантимів')
        ),
        'CLP': (
            ('песо', 'песо', 'песо'),
            ('сентаво', 'сентаво', 'сентаво')
        ),
        'CNY': (
            ('юань', 'юані', 'юанів'),
            ('финь', 'фині', 'финів')
        ),
        'COP': (
            ('песо', 'песо', 'песо'),
            ('сентаво', 'сентаво', 'сентаво')
        ),
        'CRC': (
            ('колон', 'колони', 'колонів'),
            ('сентімо', 'сентімо', 'сентімо')
        ),
        'CUC': (
            ('песо', 'песо', 'песо'),
            ('сентаво', 'сентаво', 'сентаво')
        ),
        'CUP': (
            ('песо', 'песо', 'песо'),
            ('сентаво', 'сентаво', 'сентаво')
        ),
        'CVE': (
            ('ескудо', 'ескудо', 'ескудо'),
            ('сентаво', 'сентаво', 'сентаво')
        ),
        'CZK': (
            ('крона', 'крони', 'крон'),
            ('гелер', 'гелери', 'гелерів')
        ),
        'DJF': (
            ('франк', 'франки', 'франків'),
            ('сантим', 'сантими', 'сантимів')
        ),
        'DKK': (
            ('крона', 'крони', 'крон'),
            ('ере', 'ере', 'ере')
        ),
        'DOP': (
            ('песо', 'песо', 'песо'),
            ('сентаво', 'сентаво', 'сентаво')
        ),
        'DZD': (
            ('динар', 'динари', 'динарів'),
            ('сантим', 'сантими', 'сантимів')
        ),
        'EGP': (
            ('фунт', 'фунти', 'фунтів'),
            ('піастр', 'піастри', 'піастрів')
        ),
        'ERN': (
            ('накфа', 'накфи', 'накф'),
            GENERIC_CENTS
        ),
        'ETB': (
            ('бир', 'бири', 'бирів'),
            GENERIC_CENTS
        ),
        'EUR': (
            ('євро', 'євро', 'євро'),
            GENERIC_CENTS
        ),
        'FJD': (GENERIC_DOLLARS, GENERIC_CENTS),
        'FKP': (
            ('фунт', 'фунти', 'фунтів'),
            ('пенс', 'пенси', 'пенсів')
        ),
        'GBP': (
            ('фунт', 'фунти', 'фунтів'),
            ('пенс', 'пенси', 'пенсів')
        ),
        'GEL': (
            ('ларі', 'ларі', 'ларі'),
            ('тетрі', 'тетрі', 'тетрі')
        ),
        'GHS': (
            ('седі', 'седі', 'седі'),
            ('песева', 'песеви', 'песев')
        ),
        'GIP': (
            ('фунт', 'фунти', 'фунтів'),
            ('пенс', 'пенси', 'пенсів')
        ),
        'GMD': (
            ('даласі', 'даласі', 'даласі'),
            ('бутут', 'бутути', 'бутутів')
        ),
        'GNF': (
            ('франк', 'франки', 'франків'),
            ('сантим', 'сантими', 'сантимів')
        ),
        'GTQ': (
            ('кетсаль', 'кетсалі', 'кетсалів'),
            ('сентаво', 'сентаво', 'сентаво')
        ),
        'GYD': (GENERIC_DOLLARS, GENERIC_CENTS),
        'HKD': (GENERIC_DOLLARS, GENERIC_CENTS),
        'HNL': (
            ('лемпіра', 'лемпіри', 'лемпір'),
            ('сентаво', 'сентаво', 'сентаво')
        ),
        'HRK': (
            ('куна', 'куни', 'кун'),
            ('ліпа', 'ліпи', 'ліп')
        ),
        'HTG': (
            ('гурд', 'гурди', 'гурдів'),
            ('сантим', 'сантими', 'сантимів')
        ),
        'HUF': (
            ('форинт', 'форинти', 'форинтів'),
            ('філлер', 'філлери', 'філлерів')
        ),
        'IDR': (
            ('рупія', 'рупії', 'рупій'),
            GENERIC_CENTS
        ),
        'ILS': (
            ('шекель', 'шекелі', 'шекелів'),
            ('агора', 'агори', 'агор')
        ),
        'INR': (
            ('рупія', 'рупії', 'рупій'),
            ('пайса', 'пайси', 'пайс')
        ),
        'IQD': (
            ('динар', 'динари', 'динарів'),
            ('філс', 'філси', 'філсів')
        ),
        'IRR': (
            ('ріал', 'ріали', 'ріалів'),
            ('динар', 'динари', 'динарів')
        ),
        'ISK': (
            ('крона', 'крони', 'крон'),
            ('ейре', 'ейре', 'ейре')
        ),
        'JMD': (GENERIC_DOLLARS, GENERIC_CENTS),
        'JOD': (
            ('динар', 'динари', 'динарів'),
            ('філс', 'філси', 'філсів')
        ),
        'JPY': (
            ('єна', 'єни', 'єн'),
            ('сен', 'сен', 'сен')
        ),
        'KES': (
            ('шилінг', 'шилінги', 'шилінгів'),
            GENERIC_CENTS
        ),
        'KGS': (
            ('сом', 'соми', 'сомів'),
            ('тиїн', 'тиїни', 'тиїнів')
        ),
        'KHR': (
            ('рієль', 'рієлі', 'рієлів'),
            ('су', 'су', 'су')
        ),
        'KMF': (
            ('франк', 'франки', 'франків'),
            ('сантим', 'сантими', 'сантимів')
        ),
        'KPW': (
            ('вона', 'вони', 'вон'),
            ('чон', 'чони', 'чонів')
        ),
        'KRW': (
            ('вона', 'вони', 'вон'),
            ('джеон', 'джеони', 'джеонів')
        ),
        'KWD': (
            ('динар', 'динари', 'динарів'),
            ('філс', 'філси', 'філсів')
        ),
        'KYD': (GENERIC_DOLLARS, GENERIC_CENTS),
        'KZT': (
            ('теньге', 'теньге', 'теньге'),
            ('тиїн', 'тиїни', 'тиїнів')),
        'LAK': (
            ('кіп', 'кіпи', 'кіпів'),
            ('ат', 'ати', 'атів')
        ),
        'LBP': (
            ('фунт', 'фунти', 'фунтів'),
            ('піастр', 'піастри', 'піастрів')
        ),
        'LKR': (
            ('рупія', 'рупії', 'рупій'),
            GENERIC_CENTS
        ),
        'LRD': (GENERIC_DOLLARS, GENERIC_CENTS),
        'LSL': (
            ('лоті', 'малоті', 'малоті'),
            ('сенте', 'лісенте', 'лісенте')
        ),
        'LYD': (
            ('динар', 'динари', 'динарів'),
            ('дирхам', 'дирхами', 'дирхамів')
        ),
        'MAD': (
            ('дирхам', 'дирхами', 'дирхамів'),
            ('сантим', 'сантими', 'сантимів')
        ),
        'MDL': (
            ('лей', 'леї', 'леї'),
            ('бан', 'бані', 'бані')
        ),
        'MGA': (
            ('аріарі', 'аріарі', 'аріарі'),
            ('іраймбіланья', 'іраймбіланья', 'іраймбіланья')
        ),
        'MKD': (
            ('денар', 'денари', 'денарів'),
            ('дені', 'дені', 'дені')
        ),
        'MMK': (
            ('к\'ят', 'к\'ят', 'к\'ят'),
            ('п\'я', 'п\'я', 'п\'я')
        ),
        'MNT': (
            ('тугрик', 'тугрики', 'тугриків'),
            ('мунгу', 'мунгу', 'мунгу')
        ),
        'MOP': (
            ('патака', 'патакі', 'патак'),
            ('аво', 'аво', 'аво')
        ),
        'MRU': (
            ('угія', 'угії', 'угій'),
            ('хумс', 'хумс', 'хумс')
        ),
        'MUR': (
            ('рупія', 'рупії', 'рупій'),
            GENERIC_CENTS
        ),
        'MVR': (
            ('руфія', 'руфії', 'руфій'),
            ('ларі', 'ларі', 'ларі')
        ),
        'MWK': (
            ('квача', 'квачі', 'квач'),
            ('тамбала', 'тамбала', 'тамбала')
        ),
        'MXN': (
            ('песо', 'песо', 'песо'),
            ('сентаво', 'сентаво', 'сентаво')
        ),
        'MYR': (
            ('рингіт', 'рингіти', 'рингітів'),
            GENERIC_CENTS
        ),
        'MZN': (
            ('метікал', 'метікали', 'метікалів'),
            ('сентаво', 'сентаво', 'сентаво')
        ),
        'NAD': (GENERIC_DOLLARS, GENERIC_CENTS),
        'NGN': (
            ('найра', 'найри', 'найр'),
            ('кобо', 'кобо', 'кобо')
        ),
        'NIO': (
            ('кордоба', 'кордоби', 'кордоб'),
            ('сентаво', 'сентаво', 'сентаво')
        ),
        'NOK': (
            ('крона', 'крони', 'крон'),
            ('ере', 'ере', 'ере')
        ),
        'NPR': (
            ('рупія', 'рупії', 'рупій'),
            ('пайса', 'пайси', 'пайс')
        ),
        'NZD': (GENERIC_DOLLARS, GENERIC_CENTS),
        'OMR': (
            ('ріал', 'ріали', 'ріалів'),
            ('байза', 'байзи', 'байз')
        ),
        'PAB': (
            ('бальбоа', 'бальбоа', 'бальбоа'),
            ('сентесімо', 'сентесімо', 'сентесімо')
        ),
        'PEN': (
            ('соль', 'соль', 'соль'),
            ('сентімо', 'сентімо', 'сентімо')
        ),
        'PGK': (
            ('кіна', 'кіна', 'кіна'),
            ('тойя', 'тойя', 'тойя')
        ),
        'PHP': (
            ('песо', 'песо', 'песо'),
            ('сентаво', 'сентаво', 'сентаво')
        ),
        'PKR': (
            ('рупія', 'рупії', 'рупій'),
            ('пайса', 'пайси', 'пайс')
        ),
        'PLN': (
            ('злотий', 'злоті', 'злотих'),
            ('грош', 'гроші', 'грошів')
        ),
        'PYG': (
            ('гуарані', 'гуарані', 'гуарані'),
            ('сентімо', 'сентімо', 'сентімо')
        ),
        'QAR': (
            ('ріал', 'ріали', 'ріалів'),
            ('дирхам', 'дирхами', 'дирхамів')
        ),
        'RON': (
            ('лей', 'леї', 'леї'),
            ('бан', 'бані', 'бані')
        ),
        'RSD': (
            ('динар', 'динари', 'динарів'),
            ('пара', 'пари', 'пар')
        ),
        'RUB': (
            ('рубль', 'рублі', 'рублів'),
            ('копійка', 'копійки', 'копійок')
        ),
        'RWF': (
            ('франк', 'франки', 'франків'),
            ('сантим', 'сантими', 'сантимів')
        ),
        'SAR': (
            ('ріал', 'ріали', 'ріалів'),
            ('халал', 'халали', 'халалів')
        ),
        'SBD': (GENERIC_DOLLARS, GENERIC_CENTS),
        'SCR': (
            ('рупія', 'рупії', 'рупій'),
            GENERIC_CENTS
        ),
        'SDG': (
            ('фунт', 'фунти', 'фунтів'),
            ('піастр', 'піастри', 'піастрів')
        ),
        'SEK': (
            ('крона', 'крони', 'крон'),
            ('ере', 'ере', 'ере')
        ),
        'SGD': (GENERIC_DOLLARS, GENERIC_CENTS),
        'SHP': (
            ('фунт', 'фунти', 'фунтів'),
            ('пенс', 'пенси', 'пенсів')
        ),
        'SLL': (
            ('леоне', 'леоне', 'леоне'),
            GENERIC_CENTS
        ),
        'SOS': (
            ('шилінг', 'шилінги', 'шилінгів'),
            GENERIC_CENTS
        ),
        'SRD': (GENERIC_DOLLARS, GENERIC_CENTS),
        'SSP': (
            ('фунт', 'фунти', 'фунтів'),
            ('піастр', 'піастри', 'піастрів')
        ),
        'STN': (
            ('добра', 'добри', 'добр'),
            ('сентімо', 'сентімо', 'сентімо')
        ),
        'SYP': (
            ('фунт', 'фунти', 'фунтів'),
            ('піастр', 'піастри', 'піастрів')
        ),
        'SZL': (
            ('ліланґені', 'ліланґені', 'ліланґені'),
            GENERIC_CENTS
        ),
        'THB': (
            ('бат', 'бати', 'батів'),
            ('сатанг', 'сатанги', 'сатангів')
        ),
        'TJS': (
            ('сомоні', 'сомоні', 'сомоні'),
            ('дірам', 'дірами', 'дірамів')
        ),
        'TMT': (
            ('манат', 'манати', 'манатів'),
            ('тенге', 'тенге', 'тенге')
        ),
        'TND': (
            ('динар', 'динари', 'динарів'),
            ('міллім', 'мілліми', 'міллімів')
        ),
        'TOP': (
            ('паанга', 'паанга', 'паанга'),
            ('сеніті', 'сеніті', 'сеніті')
        ),
        'TRY': (
            ('ліра', 'ліри', 'лір'),
            ('куруш', 'куруші', 'курушів')
        ),
        'TTD': (GENERIC_DOLLARS, GENERIC_CENTS),
        'TWD': (
            ('новий долар', 'нові долари', 'нових доларів'),
            GENERIC_CENTS
        ),
        'TZS': (
            ('шилінг', 'шилінги', 'шилінгів'),
            GENERIC_CENTS
        ),
        'UAH': (
            ('гривня', 'гривні', 'гривень'),
            ('копійка', 'копійки', 'копійок')
        ),
        'UGX': (
            ('шилінг', 'шилінги', 'шилінгів'),
            GENERIC_CENTS
        ),
        'USD': (GENERIC_DOLLARS, GENERIC_CENTS),
        'UYU': (
            ('песо', 'песо', 'песо'),
            ('сентесімо', 'сентесімо', 'сентесімо')
        ),
        'UZS': (
            ('сум', 'суми', 'сумів'),
            ('тиїн', 'тиїни', 'тиїнів')
        ),
        'VND': (
            ('донг', 'донги', 'донгів'),
            ('су', 'су', 'су')
        ),
        'WST': (
            ('тала', 'тали', 'тал'),
            ('сене', 'сене', 'сене')
        ),
        'XCD': (GENERIC_DOLLARS, GENERIC_CENTS),
        'YER': (
            ('ріал', 'ріали', 'ріалів'),
            ('філс', 'філси', 'філсів')
        ),
        'ZAR': (
            ('ранд', 'ранди', 'рандів'),
            GENERIC_CENTS
        ),
        'ZMW': (
            ('квача', 'квачі', 'квач'),
            ('нгве', 'нгве', 'нгве')
        ),
    }

    def setup(self):
        self.negword = "мінус"
        self.pointword = "кома"

    def to_cardinal(self, number, **kwargs):
        if 'case' in kwargs:
            case = kwargs['case']
            morphological_case = [
                "nominative",
                "genitive",
                "dative",
                "accusative",
                "instrumental",
                "locative"].index(case)
        else:
            morphological_case = 0

        if 'gender' in kwargs:
            gender = kwargs['gender'] == 'feminine'
        else:
            gender = False

        n = str(number).replace(',', '.')
        if '.' in n:
            left, right = n.split('.')
            leading_zero_count = len(right) - len(right.lstrip('0'))
            right_side = self._int2word(int(right), gender, morphological_case)
            decimal_part = ((ZERO[0] + ' ') * leading_zero_count +
                            right_side)
            return u'%s %s %s' % (
                self._int2word(int(left), gender, morphological_case),
                self.pointword,
                decimal_part
            )
        else:
            return self._int2word(int(n), gender, morphological_case)

    def pluralize(self, n, forms):
        if n % 100 < 10 or n % 100 > 20:
            if n % 10 == 1:
                form = 0
            elif 5 > n % 10 > 1:
                form = 1
            else:
                form = 2
        else:
            form = 2

        return forms[form]

    def _int2word(self, n, feminine=False, morphological_case=0):
        if n < 0:
            n_value = self._int2word(abs(n), feminine, morphological_case)
            return ' '.join([self.negword,
                             n_value])

        if n == 0:
            return ZERO[0]

        words = []
        chunks = list(splitbyx(str(n), 3))
        i = len(chunks)
        for x in chunks:
            i -= 1

            if x == 0:
                continue

            n1, n2, n3 = get_digits(x)

            if n3 > 0:
                words.append(HUNDREDS[n3][morphological_case])

            if n2 > 1:
                words.append(TWENTIES[n2][morphological_case])

            if n2 == 1:
                words.append(TENS[n1][morphological_case])
            # elif n1 > 0 and not (i > 0 and x == 1):
            elif n1 > 0:
                ones = ONES_FEMININE if i == 1 or feminine and i == 0 else ONES
                words.append(ones[n1][morphological_case])

            if i > 0:
                thousands_val = THOUSANDS[i][morphological_case]
                words.append(self.pluralize(x, thousands_val))

        return ' '.join(words)

    def _money_verbose(self, number, currency):
        return self._int2word(number, currency in FEMININE_MONEY)

    def _cents_verbose(self, number, currency):
        return self._int2word(number, currency in FEMININE_CENTS)

    @staticmethod
    def last_fragment_to_ordinal(last, words, level):
        n1, n2, n3 = get_digits(last)
        last_two = n2*10+n1
        if last_two == 0:
            words.append(HUNDREDS_ORDINALS[n3][level])
        elif level == 1 and last == 1:
            return
        elif last_two < 20:
            if level == 0:
                if n3 > 0:
                    words.append(HUNDREDS[n3][0])
                words.append(ONES_ORDINALS[last_two][0])
            else:
                last_fragment_string = ''
                if n3 > 0:
                    last_fragment_string += HUNDREDS_ORDINALS[n3][1]
                last_fragment_string += ONES_ORDINALS[last_two][1]
                words.append(last_fragment_string)
        elif last_two % 10 == 0:
            if level == 0:
                if n3 > 0:
                    words.append(HUNDREDS[n3][0])
                words.append(TWENTIES_ORDINALS[n2][0])
            else:
                last_fragment_string = ''
                if n3 > 0:
                    last_fragment_string += HUNDREDS_ORDINALS[n3][1]
                last_fragment_string += TWENTIES_ORDINALS[n2][1]
                words.append(last_fragment_string)
        else:
            if level == 0:
                if n3 > 0:
                    words.append(HUNDREDS[n3][0])
                words.append(TWENTIES[n2][0])
                words.append(ONES_ORDINALS[n1][0])
            else:
                last_fragment_string = ''
                if n3 > 0:
                    last_fragment_string += HUNDREDS_ORDINALS[n3][1]
                last_fragment_string += TWENTIES_ORDINALS[n2][1]
                last_fragment_string += ONES_ORDINALS[n1][1]
                words.append(last_fragment_string)

    def to_ordinal(self, number):
        self.verify_ordinal(number)

        words = []
        fragments = list(splitbyx(str(number), 3))
        level = 0
        last = fragments[-1]
        while last == 0:
            level = level + 1
            fragments.pop()
            last = fragments[-1]
        if len(fragments) > 1:
            pre_part = self._int2word(number - (last * 1000 ** level))
            words.append(pre_part)
        Num2Word_UK.last_fragment_to_ordinal(
            last,
            words,
            0 if level == 0 else 1
        )
        output = " ".join(words)
        if last == 1 and level > 0 and output != "":
            output = output + " "
        if level > 0:
            output = output + prefixes_ordinal[level]
        return output
