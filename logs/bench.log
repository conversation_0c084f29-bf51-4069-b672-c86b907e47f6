2025-05-20 20:45:24,525 INFO /usr/local/bin/bench start
2025-05-20 20:45:25,288 INFO /usr/local/bin/bench watch
2025-05-20 20:45:25,330 INFO /usr/local/bin/bench schedule
2025-05-20 20:45:25,346 INFO /usr/local/bin/bench serve --port 8000
2025-05-20 20:45:25,378 INFO /usr/local/bin/bench worker
2025-05-20 21:11:44,782 INFO /usr/local/bin/bench --site site1.local install-app whatsapp_integration
2025-05-20 21:12:01,009 INFO /usr/local/bin/bench --site site1.local list-apps
2025-05-20 21:12:19,628 INFO /usr/local/bin/bench --site site1.local migrate
2025-05-20 21:14:30,591 INFO /usr/local/bin/bench --site site1.local migrate
2025-05-20 21:15:38,020 INFO /usr/local/bin/bench --site site1.local uninstall-app whatsapp_integration
2025-05-20 21:16:34,267 INFO /usr/local/bin/bench --site site1.local list-apps
2025-05-20 21:18:00,396 INFO /usr/local/bin/bench --site site1.local install-app whatsapp_integration
2025-05-20 21:20:24,461 INFO /usr/local/bin/bench --site site1.local install-app whatsapp_integration
2025-05-20 21:20:53,682 INFO /usr/local/bin/bench --site site1.local install-app whatsapp_integration
2025-05-20 21:21:06,920 INFO /usr/local/bin/bench --site site1.local list-apps
2025-05-20 21:21:18,720 INFO /usr/local/bin/bench restart
2025-05-20 21:21:37,433 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-20 21:21:38,591 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-20 21:21:38,602 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-05-20 21:21:40,633 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-20 21:21:50,078 INFO /usr/local/bin/bench restart
2025-05-20 21:21:52,841 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-20 21:21:54,146 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-20 21:21:54,146 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-05-20 21:21:55,620 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-20 21:22:08,793 INFO /usr/local/bin/bench start
2025-05-20 21:22:13,505 INFO /usr/local/bin/bench --site site1.local list-apps
2025-05-20 21:22:25,035 INFO /usr/local/bin/bench --site site1.local console
2025-05-20 21:24:11,960 INFO /usr/local/bin/bench make-app whatsapp_integration
2025-05-20 21:24:38,053 INFO /usr/local/bin/bench --site site1.local make-doctype WhatsApp Account --module WhatsApp Integration
2025-05-20 21:24:47,499 INFO /usr/local/bin/bench --site site1.local add-to-hosts
2025-05-20 21:36:56,278 INFO /usr/local/bin/bench --site site1.local migrate
2025-05-20 21:38:02,895 INFO /usr/local/bin/bench restart
2025-05-20 21:38:30,143 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-20 21:38:32,225 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-20 21:38:32,226 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-05-20 21:38:33,578 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-20 21:40:20,193 INFO /usr/local/bin/bench --site site1.local list-apps
2025-05-20 21:40:42,560 INFO /usr/local/bin/bench --site site1.local console
2025-05-20 21:42:42,922 INFO /usr/local/bin/bench --site site1.local --force reinstall
2025-05-20 21:48:12,561 INFO /usr/local/bin/bench --site site1.local build
2025-05-20 21:50:12,606 INFO /usr/local/bin/bench --site site1.local migrate
2025-05-20 21:50:39,190 INFO /usr/local/bin/bench restart
2025-05-20 21:50:51,882 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-20 21:50:52,491 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-20 21:50:52,495 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-05-20 21:50:54,528 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-20 21:51:06,916 INFO /usr/local/bin/bench start
2025-05-20 21:51:34,825 INFO /usr/local/bin/bench stop
2025-05-20 21:51:44,486 INFO /usr/local/bin/bench setup supervisor
2025-05-20 21:51:45,112 LOG Updated supervisord.conf: 'chmod' changed from '0700                       ; sockef file mode (default 0700)' to '0760'
2025-05-20 21:51:45,112 LOG Updated supervisord.conf: 'chown' changed from '' to 'newsmart:newsmart'
2025-05-20 21:51:46,418 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-20 21:51:54,534 INFO /usr/local/bin/bench restart
2025-05-20 21:51:55,560 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-20 21:51:56,212 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-20 21:51:56,213 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-05-20 21:51:57,049 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-20 21:52:07,014 INFO /usr/local/bin/bench start
2025-05-20 21:54:09,562 INFO /usr/local/bin/bench --site site1.local console
2025-05-20 21:58:58,532 INFO /usr/local/bin/bench restart
2025-05-20 21:58:59,414 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-20 21:58:59,963 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-20 21:58:59,964 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-05-20 21:59:01,005 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-20 22:00:03,241 INFO /usr/local/bin/bench start
2025-05-20 22:01:12,389 INFO /usr/local/bin/bench start
2025-05-20 22:01:28,259 INFO /usr/local/bin/bench --site site1.local install-app whatsapp_integration
2025-05-20 22:01:40,111 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-20 22:01:51,626 INFO /usr/local/bin/bench start
2025-05-20 22:03:04,664 INFO /usr/local/bin/bench --site site1.local mariadb
2025-05-20 22:04:09,489 INFO /usr/local/bin/bench --site site1.local rebuild-global-search
2025-05-20 22:04:20,755 INFO /usr/local/bin/bench --site site1.local migrate
2025-05-20 22:04:33,572 INFO /usr/local/bin/bench --site site1.local reinstall
2025-05-20 22:05:58,792 INFO /usr/local/bin/bench start
2025-05-20 22:06:15,993 INFO /usr/local/bin/bench --site site1.local mariadb
2025-05-20 22:06:33,213 INFO /usr/local/bin/bench --site site1.local mariadb
2025-05-20 22:06:48,903 INFO /usr/local/bin/bench --site site1.local set-config maintenance_mode 1
2025-05-20 22:06:59,884 INFO /usr/local/bin/bench --site site1.local --force reinstall
2025-05-20 22:07:24,489 INFO /usr/local/bin/bench --site site1.local set-config maintenance_mode 0
2025-05-20 22:07:34,851 INFO /usr/local/bin/bench start
2025-05-20 22:08:44,199 INFO /usr/local/bin/bench --site site1.local --force reinstall
2025-05-20 22:11:52,323 INFO /usr/local/bin/bench start
2025-05-20 22:11:52,708 INFO /usr/local/bin/bench watch
2025-05-20 22:11:52,748 INFO /usr/local/bin/bench worker
2025-05-20 22:11:52,839 INFO /usr/local/bin/bench serve --port 8000
2025-05-20 22:11:52,955 INFO /usr/local/bin/bench schedule
2025-05-20 22:13:34,739 INFO /usr/local/bin/bench --site site1.local list-apps
2025-05-20 22:15:28,103 INFO /usr/local/bin/bench --site site1.local install-app erpnext
2025-05-20 22:16:40,353 INFO /usr/local/bin/bench --site site1.local reinstall-app erpnext
2025-05-20 22:16:55,148 INFO /usr/local/bin/bench --site site1.local migrate
2025-05-20 22:17:22,033 INFO /usr/local/bin/bench --site site1.local install-app erpnext
2025-05-20 22:18:07,990 INFO /usr/local/bin/bench --site site1.local backup
2025-05-20 22:18:29,809 INFO /usr/local/bin/bench --site site1.local backup
2025-05-20 22:18:44,687 INFO /usr/local/bin/bench --site site1.local --force reinstall
2025-05-20 22:20:03,050 INFO /usr/local/bin/bench --site site1.local backup
2025-05-20 22:20:18,195 INFO /usr/local/bin/bench --site site1.local --force reinstall
2025-05-20 22:23:31,128 INFO /usr/local/bin/bench --site site1.local install-app erpnext
2025-05-20 22:28:41,543 INFO /usr/local/bin/bench --site site1.local install-app whatsapp_integration
2025-05-20 22:32:36,311 INFO /usr/local/bin/bench --site site1.local console
2025-05-20 22:34:35,240 INFO /usr/local/bin/bench --site site1.local install-app whatsapp_integration
2025-05-20 22:34:59,052 INFO /usr/local/bin/bench --site site1.local install-app whatsapp_integration
2025-05-20 22:36:14,579 INFO /usr/local/bin/bench --site site1.local install-app whatsapp_integration
2025-05-20 22:37:11,873 INFO /usr/local/bin/bench --site site1.local install-app whatsapp_integration
2025-05-20 22:37:24,465 INFO /usr/local/bin/bench setup requirements
2025-05-20 22:37:24,497 DEBUG /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade pip
2025-05-20 22:37:30,699 LOG Installing frappe
2025-05-20 22:37:30,703 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/frappe 
2025-05-20 22:37:39,142 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade coverage~=6.5.0 Faker~=18.10.1 pyngrok~=6.0.0 unittest-xml-reporting~=3.2.0 watchdog~=3.0.0 hypothesis~=6.77.0 responses==0.23.1 freezegun~=1.2.2 
2025-05-20 22:37:47,708 DEBUG cd /home/<USER>/frappe-bench/apps/frappe && yarn install --check-files
2025-05-20 22:38:07,756 LOG Installing webshop
2025-05-20 22:38:07,758 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/webshop 
2025-05-20 22:38:16,464 LOG Installing custom_erp
2025-05-20 22:38:16,465 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/custom_erp 
2025-05-20 22:38:25,024 LOG Installing saudi_phase2_api
2025-05-20 22:38:25,027 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/saudi_phase2_api 
2025-05-20 22:38:33,567 LOG Installing payments
2025-05-20 22:38:33,569 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/payments 
2025-05-20 22:38:43,609 LOG Installing zatca_erpgulf
2025-05-20 22:38:43,610 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/zatca_erpgulf 
2025-05-20 22:38:51,691 LOG Installing erpnext
2025-05-20 22:38:51,692 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/erpnext 
2025-05-20 22:39:05,075 DEBUG cd /home/<USER>/frappe-bench/apps/erpnext && yarn install --check-files
2025-05-20 22:39:06,167 LOG Installing smart_theme
2025-05-20 22:39:06,170 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/smart_theme 
2025-05-20 22:39:16,298 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-20 22:39:26,192 INFO /usr/local/bin/bench start
2025-05-20 22:42:46,704 INFO /usr/local/bin/bench --site site1.local install-app whatsapp_integration
2025-05-20 22:43:02,047 INFO /usr/local/bin/bench new-app whatsapp_integration
2025-05-20 22:43:02,057 LOG creating new app whatsapp_integration
2025-05-20 22:44:48,828 LOG Installing whatsapp_integration
2025-05-20 22:44:48,858 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/whatsapp_integration 
2025-05-20 22:44:59,543 DEBUG bench build --app whatsapp_integration
2025-05-20 22:45:00,100 INFO /usr/local/bin/bench build --app whatsapp_integration
2025-05-20 22:45:30,225 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-20 22:45:30,963 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-20 22:45:30,963 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-05-20 22:45:32,075 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-20 22:48:58,652 INFO /usr/local/bin/bench --site site1.local install-app whatsapp_integration
2025-05-20 22:49:12,084 INFO /usr/local/bin/bench restart
2025-05-20 22:49:22,671 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-20 22:49:23,400 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-20 22:49:23,401 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-05-20 22:49:24,493 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-20 22:49:36,694 INFO /usr/local/bin/bench start
                                                                                                      2025-05-21 12:51:59,440 INFO /usr/local/bin/bench stsrt
2025-05-21 12:52:07,755 INFO /usr/local/bin/bench start
2025-05-21 12:52:09,157 INFO /usr/local/bin/bench schedule
2025-05-21 12:52:09,283 INFO /usr/local/bin/bench worker
2025-05-21 12:52:09,323 INFO /usr/local/bin/bench watch
2025-05-21 12:52:09,493 INFO /usr/local/bin/bench serve --port 8000
2025-05-21 13:08:32,106 INFO /usr/local/bin/bench version
2025-05-21 13:08:43,942 INFO /usr/local/bin/bench list-apps
2025-05-21 13:08:59,933 INFO /usr/local/bin/bench list-sites
2025-05-21 13:09:09,205 INFO /usr/local/bin/bench --site list
2025-05-21 13:13:01,794 INFO /usr/local/bin/bench new-app whatsapp_integration
2025-05-21 13:13:01,833 LOG creating new app whatsapp_integration
2025-05-21 13:13:16,141 WARNING /usr/local/bin/bench new-app whatsapp_integration executed with exit code 1
2025-05-21 13:13:18,479 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-21 13:13:48,300 INFO /usr/local/bin/bench --site site1.local install-app whatsapp_integration
2025-05-21 13:15:14,724 INFO /usr/local/bin/bench new-app whatsapp_integration
2025-05-21 13:15:14,742 LOG creating new app whatsapp_integration
2025-05-21 13:16:27,998 LOG Installing whatsapp_integration
2025-05-21 13:16:28,017 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/whatsapp_integration 
2025-05-21 13:16:57,194 DEBUG bench build --app whatsapp_integration
2025-05-21 13:16:58,182 INFO /usr/local/bin/bench build --app whatsapp_integration
2025-05-21 13:18:18,693 WARNING /usr/local/bin/bench new-app whatsapp_integration executed with exit code 1
2025-05-21 13:18:20,388 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-21 13:22:28,032 INFO /usr/local/bin/bench --site site1.local install-app whatsapp_integration
2025-05-21 13:22:57,768 INFO /usr/local/bin/bench setup requirements
2025-05-21 13:22:57,791 DEBUG /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade pip
2025-05-21 13:23:03,741 LOG Installing frappe
2025-05-21 13:23:03,742 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/frappe 
2025-05-21 13:23:19,612 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade coverage~=6.5.0 Faker~=18.10.1 pyngrok~=6.0.0 unittest-xml-reporting~=3.2.0 watchdog~=3.0.0 hypothesis~=6.77.0 responses==0.23.1 freezegun~=1.2.2 
2025-05-21 13:23:34,101 DEBUG cd /home/<USER>/frappe-bench/apps/frappe && yarn install --check-files
2025-05-21 13:24:15,986 LOG Installing webshop
2025-05-21 13:24:15,986 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/webshop 
2025-05-21 13:24:32,999 LOG Installing custom_erp
2025-05-21 13:24:33,013 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/custom_erp 
2025-05-21 13:24:48,148 LOG Installing saudi_phase2_api
2025-05-21 13:24:48,150 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/saudi_phase2_api 
2025-05-21 13:25:04,215 LOG Installing payments
2025-05-21 13:25:04,216 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/payments 
2025-05-21 13:25:23,230 LOG Installing whatsapp_integration
2025-05-21 13:25:23,231 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/whatsapp_integration 
2025-05-21 13:25:40,547 LOG Installing zatca_erpgulf
2025-05-21 13:25:40,614 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/zatca_erpgulf 
2025-05-21 13:25:56,565 LOG Installing erpnext
2025-05-21 13:25:56,566 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/erpnext 
2025-05-21 13:26:13,823 DEBUG cd /home/<USER>/frappe-bench/apps/erpnext && yarn install --check-files
2025-05-21 13:26:16,927 LOG Installing smart_theme
2025-05-21 13:26:16,934 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/smart_theme 
2025-05-21 13:26:39,972 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-21 13:26:53,316 INFO /usr/local/bin/bench --site site1.local install-app whatsapp_integration
2025-05-21 13:27:07,441 INFO /usr/local/bin/bench restart
2025-05-21 13:27:15,721 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-21 13:27:16,994 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-21 13:27:16,994 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-05-21 13:27:19,786 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-21 13:27:33,536 INFO /usr/local/bin/bench start
2025-05-21 13:27:43,396 INFO /usr/local/bin/bench --site site1.local add-to-hosts
2025-05-21 13:33:41,917 INFO /usr/local/bin/bench --site site1.local install-app whatsapp_integration
2025-05-21 13:35:41,707 INFO /usr/local/bin/bench new-app whatsapp_integration
2025-05-21 13:35:41,886 LOG creating new app whatsapp_integration
2025-05-21 13:36:47,849 LOG Installing whatsapp_integration
2025-05-21 13:36:47,861 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/whatsapp_integration 
2025-05-21 13:37:03,522 DEBUG bench build --app whatsapp_integration
2025-05-21 13:37:04,197 INFO /usr/local/bin/bench build --app whatsapp_integration
2025-05-21 13:37:26,469 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-21 13:37:27,706 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-21 13:37:27,706 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-05-21 13:37:28,961 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-21 13:39:52,190 INFO /usr/local/bin/bench new-app whatsapp
2025-05-21 13:39:52,222 LOG creating new app whatsapp
2025-05-21 13:41:35,394 LOG Installing whatsapp
2025-05-21 13:41:35,837 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/whatsapp 
2025-05-21 13:41:52,787 DEBUG bench build --app whatsapp
2025-05-21 13:41:53,287 INFO /usr/local/bin/bench build --app whatsapp
2025-05-21 13:42:09,354 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-21 13:42:10,482 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-21 13:42:10,483 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-05-21 13:42:11,798 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-21 13:43:10,521 INFO /usr/local/bin/bench start
2025-05-21 13:43:10,942 INFO /usr/local/bin/bench watch
2025-05-21 13:43:10,951 INFO /usr/local/bin/bench schedule
2025-05-21 13:43:11,007 INFO /usr/local/bin/bench serve --port 8000
2025-05-21 13:43:11,033 INFO /usr/local/bin/bench worker
2025-05-21 14:12:29,796 INFO /usr/local/bin/bench --site site1.local install-app whatsapp
2025-05-21 15:38:41,892 INFO /usr/local/bin/bench restart
2025-05-21 15:38:52,718 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-21 15:38:53,337 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-21 15:38:53,337 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-05-21 15:38:54,603 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-21 15:39:23,948 INFO /usr/local/bin/bench start
2025-05-24 12:32:06,149 INFO /usr/local/bin/bench start
2025-05-24 12:32:06,617 INFO /usr/local/bin/bench schedule
2025-05-24 12:32:06,701 INFO /usr/local/bin/bench worker
2025-05-24 12:32:06,788 INFO /usr/local/bin/bench watch
2025-05-24 12:32:06,797 INFO /usr/local/bin/bench serve --port 8000
2025-05-24 12:42:24,838 INFO /usr/local/bin/bench start
2025-05-24 12:42:26,918 INFO /usr/local/bin/bench watch
2025-05-24 12:42:27,057 INFO /usr/local/bin/bench serve --port 8000
2025-05-24 12:42:27,068 INFO /usr/local/bin/bench worker
2025-05-24 12:42:27,163 INFO /usr/local/bin/bench schedule
2025-05-24 12:42:53,525 INFO /usr/local/bin/bench start
2025-05-24 12:42:54,536 INFO /usr/local/bin/bench serve --port 8000
2025-05-24 12:42:54,695 INFO /usr/local/bin/bench worker
2025-05-24 12:42:54,696 INFO /usr/local/bin/bench schedule
2025-05-24 12:42:54,922 INFO /usr/local/bin/bench watch
2025-05-24 13:02:42,502 INFO /usr/local/bin/bench start
2025-05-24 13:02:43,565 INFO /usr/local/bin/bench watch
2025-05-24 13:02:43,631 INFO /usr/local/bin/bench serve --port 8000
2025-05-24 13:02:43,656 INFO /usr/local/bin/bench schedule
2025-05-24 13:02:43,694 INFO /usr/local/bin/bench worker
2025-05-24 13:11:38,338 INFO /usr/local/bin/bench start
2025-05-24 13:11:38,685 INFO /usr/local/bin/bench worker
2025-05-24 13:11:38,696 INFO /usr/local/bin/bench watch
2025-05-24 13:11:38,702 INFO /usr/local/bin/bench serve --port 8000
2025-05-24 13:11:39,159 INFO /usr/local/bin/bench schedule
2025-05-24 13:30:42,361 INFO /usr/local/bin/bench build
2025-05-24 13:33:51,603 INFO /usr/local/bin/bench migrate
2025-05-24 13:34:35,257 INFO /usr/local/bin/bench migrate
2025-05-24 13:35:25,735 INFO /usr/local/bin/bench start
2025-05-24 13:35:27,011 INFO /usr/local/bin/bench worker
2025-05-24 13:35:27,018 INFO /usr/local/bin/bench watch
2025-05-24 13:35:27,072 INFO /usr/local/bin/bench schedule
2025-05-24 13:35:27,107 INFO /usr/local/bin/bench serve --port 8000
2025-05-24 13:45:21,939 INFO /usr/local/bin/bench start
2025-05-24 13:45:23,718 INFO /usr/local/bin/bench schedule
2025-05-24 13:45:23,730 INFO /usr/local/bin/bench watch
2025-05-24 13:45:23,731 INFO /usr/local/bin/bench worker
2025-05-24 13:45:23,755 INFO /usr/local/bin/bench serve --port 8000
2025-05-24 13:56:33,683 INFO /usr/local/bin/bench restart
2025-05-24 13:56:42,334 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-24 13:56:43,600 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-24 13:56:43,609 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-05-24 13:56:45,842 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-24 13:56:57,518 INFO /usr/local/bin/bench restart
2025-05-24 13:56:58,324 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-24 13:56:58,702 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-24 13:56:58,703 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-05-24 13:57:00,760 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-24 14:13:24,460 INFO /usr/local/bin/bench --site newsmart.local execute frappe.db.get_single_value --args ['WhatsApp Settings', 'whatsapp_server_url']
2025-05-24 14:13:44,392 INFO /usr/local/bin/bench --site newsmart.local list-apps
2025-05-24 14:14:02,514 INFO /usr/local/bin/bench --site newsmart.local install-app whatsapp
2025-05-24 14:14:58,623 INFO /usr/local/bin/bench --site newsmart.local execute frappe.db.get_single_value --args ['WhatsApp Settings', 'whatsapp_server_url']
2025-05-24 14:15:15,256 INFO /usr/local/bin/bench --site newsmart.local execute frappe.db.get_single_value --args ['WhatsApp Settings', 'whatsapp_server_port']
2025-05-24 14:15:31,257 INFO /usr/local/bin/bench --site newsmart.local execute frappe.db.get_single_value --args ['WhatsApp Settings', 'enable_whatsapp']
2025-05-24 14:15:46,935 INFO /usr/local/bin/bench --site newsmart.local execute frappe.db.set_single_value --args ['WhatsApp Settings', 'enable_whatsapp', 1]
2025-05-24 14:16:03,978 INFO /usr/local/bin/bench --site newsmart.local execute whatsapp.whatsapp.api.connect_account --args ['buy']
2025-05-24 14:17:15,338 INFO /usr/local/bin/bench --site site1.local list-apps
2025-05-24 14:17:46,114 INFO /usr/local/bin/bench --site site1.local execute frappe.db.get_single_value --args ['WhatsApp Settings', 'enable_whatsapp']
2025-05-24 14:17:59,729 INFO /usr/local/bin/bench --site site1.local execute frappe.db.get_single_value --args ['WhatsApp Settings', 'whatsapp_server_url']
2025-05-24 14:18:14,318 INFO /usr/local/bin/bench --site site1.local execute frappe.db.get_single_value --args ['WhatsApp Settings', 'whatsapp_server_port']
2025-05-24 14:18:28,238 INFO /usr/local/bin/bench --site site1.local execute whatsapp.whatsapp.api.connect_account --args ['buy']
2025-05-24 14:19:00,249 INFO /usr/local/bin/bench --site site1.local execute frappe.db.get_value --args ['WhatsApp Sender Account', 'buy', ['status', 'qr_code_html']]
2025-05-24 14:19:16,631 INFO /usr/local/bin/bench --site site1.local execute frappe.db.get_value --args ['WhatsApp Sender Account', 'buy', 'status']
2025-05-24 14:19:33,781 INFO /usr/local/bin/bench --site site1.local migrate
2025-05-24 14:21:02,806 INFO /usr/local/bin/bench --site site1.local execute whatsapp.whatsapp.api.connect_account --args ['buy']
2025-05-24 14:21:33,431 INFO /usr/local/bin/bench --site site1.local execute frappe.db.get_value --args ['WhatsApp Sender Account', 'buy', ['status', 'qr_code_html']]
2025-05-24 14:21:50,529 INFO /usr/local/bin/bench --site site1.local execute frappe.db.get_value --args ['WhatsApp Sender Account', 'buy', 'status']
2025-05-24 14:22:11,807 INFO /usr/local/bin/bench --site site1.local execute frappe.db.sql --args ['ALTER TABLE  ADD COLUMN qr_code_html LONGTEXT']
2025-05-24 14:22:33,782 INFO /usr/local/bin/bench --site site1.local mariadb
2025-05-24 14:24:38,729 INFO /usr/local/bin/bench --site site1.local reload-doctype WhatsApp Sender Account
2025-05-24 14:24:56,787 INFO /usr/local/bin/bench --site site1.local execute frappe.db.get_value --args ['WhatsApp Sender Account', 'buy', ['status', 'qr_code_html']]
2025-05-24 14:25:14,233 INFO /usr/local/bin/bench --site site1.local migrate
2025-05-24 14:26:18,144 INFO /usr/local/bin/bench --site site1.local execute frappe.db.get_value --args ['WhatsApp Sender Account', 'buy', ['status', 'qr_code_html']]
2025-05-24 14:26:37,769 INFO /usr/local/bin/bench --site site1.local execute whatsapp.whatsapp.api.connect_account --args ['buy']
2025-05-24 14:31:13,840 INFO /usr/local/bin/bench --site site1.local execute whatsapp.whatsapp.api.connect_account --args ['seling']
2025-05-24 14:32:04,208 INFO /usr/local/bin/bench --site site1.local execute frappe.db.get_value --args ['WhatsApp Sender Account', 'seling', 'status']
2025-05-24 14:49:44,130 INFO /usr/local/bin/bench --site site1.local execute whatsapp.whatsapp.api.connect_account --args ['seling']
2025-05-24 14:51:18,496 INFO /usr/local/bin/bench --site site1.local execute frappe.db.get_value --args ['WhatsApp Sender Account', 'seling', 'status']
2025-05-24 14:52:35,416 INFO /usr/local/bin/bench --site site1.local execute frappe.get_doc --args ['WhatsApp Sender Account', 'seling']
2025-05-25 13:27:08,362 INFO /usr/local/bin/bench migrate
2025-05-25 13:28:18,927 INFO /usr/local/bin/bench migrate
2025-05-25 13:47:29,007 INFO /usr/local/bin/bench build --app whatsapp
2025-05-25 13:49:07,845 INFO /usr/local/bin/bench restart
2025-05-25 13:49:19,399 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-25 13:49:20,957 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-25 13:49:20,959 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-05-25 13:49:22,756 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-25 13:50:09,857 INFO /usr/local/bin/bench start
2025-05-25 13:54:00,662 INFO /usr/local/bin/bench --site site1.local execute frappe.get_doc --args [{'doctype': 'Sales Invoice', 'customer': 'Test Customer', 'items': [{'item_code': 'Test Item', 'qty': 1, 'rate': 100}]}]
2025-05-25 13:54:36,648 INFO /usr/local/bin/bench --site site1.local execute frappe.get_doc --args [{'doctype': 'Customer', 'customer_name': 'Test Customer', 'mobile_no': '+967782333271'}]
2025-05-25 13:55:07,821 INFO /usr/local/bin/bench --site site1.local execute 
import frappe
customer = frappe.new_doc('Customer')
customer.customer_name = 'Test Customer'
customer.mobile_no = '+967782333271'
customer.save()
print('Customer created:', customer.name)

2025-05-25 14:02:18,289 INFO /usr/local/bin/bench clear-cache
2025-05-25 14:05:56,920 INFO /usr/local/bin/bench build --app whatsapp
2025-05-25 14:07:06,213 INFO /usr/local/bin/bench clear-cache
2025-05-25 14:08:29,859 INFO /usr/local/bin/bench build --app whatsapp --force
2025-05-25 14:11:15,145 INFO /usr/local/bin/bench start
2025-05-25 14:11:20,084 INFO /usr/local/bin/bench schedule
2025-05-25 14:11:20,238 INFO /usr/local/bin/bench worker
2025-05-25 14:11:20,246 INFO /usr/local/bin/bench watch
2025-05-25 14:11:20,270 INFO /usr/local/bin/bench serve --port 8000
2025-05-25 14:16:26,312 INFO /usr/local/bin/bench build --app whatsapp --force
2025-05-25 14:38:42,433 INFO /usr/local/bin/bench start
2025-05-25 14:38:44,017 INFO /usr/local/bin/bench schedule
2025-05-25 14:38:44,047 INFO /usr/local/bin/bench serve --port 8000
2025-05-25 14:38:44,170 INFO /usr/local/bin/bench watch
2025-05-25 14:38:44,222 INFO /usr/local/bin/bench worker
2025-05-25 14:43:34,306 INFO /usr/local/bin/bench start
2025-05-25 14:43:35,013 INFO /usr/local/bin/bench serve --port 8000
2025-05-25 14:43:35,163 INFO /usr/local/bin/bench worker
2025-05-25 14:43:35,222 INFO /usr/local/bin/bench schedule
2025-05-25 14:43:35,344 INFO /usr/local/bin/bench watch
2025-05-25 14:45:23,631 INFO /usr/local/bin/bench build
2025-05-25 14:46:16,388 INFO /usr/local/bin/bench start
2025-05-25 14:46:16,866 INFO /usr/local/bin/bench watch
2025-05-25 14:46:16,879 INFO /usr/local/bin/bench worker
2025-05-25 14:46:16,924 INFO /usr/local/bin/bench schedule
2025-05-25 14:46:16,952 INFO /usr/local/bin/bench serve --port 8000
2025-05-25 14:49:25,224 INFO /usr/local/bin/bench migrate
2025-05-25 14:51:07,037 INFO /usr/local/bin/bench redis_cashe
2025-05-25 14:51:20,414 INFO /usr/local/bin/bench redis cashe
2025-05-25 14:51:32,873 INFO /usr/local/bin/bench start
2025-05-25 14:51:33,262 INFO /usr/local/bin/bench serve --port 8000
2025-05-25 14:51:33,263 INFO /usr/local/bin/bench watch
2025-05-25 14:51:33,264 INFO /usr/local/bin/bench worker
2025-05-25 14:51:33,507 INFO /usr/local/bin/bench schedule
2025-05-25 14:53:07,896 INFO /usr/local/bin/bench --site site1.local console
2025-05-25 14:56:09,386 INFO /usr/local/bin/bench start
2025-05-25 14:56:10,306 INFO /usr/local/bin/bench serve --port 8000
2025-05-25 14:56:10,328 INFO /usr/local/bin/bench worker
2025-05-25 14:56:10,343 INFO /usr/local/bin/bench schedule
2025-05-25 14:56:10,660 INFO /usr/local/bin/bench watch
2025-05-25 15:11:51,373 INFO /usr/local/bin/bench build --app whatsapp
2025-05-25 15:12:16,246 INFO /usr/local/bin/bench restart
2025-05-25 15:12:24,328 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-25 15:12:25,318 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-25 15:12:25,319 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-05-25 15:12:29,240 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-25 15:12:56,304 INFO /usr/local/bin/bench start
2025-05-25 15:13:19,328 INFO /usr/local/bin/bench --site newsmart.local clear-cache
2025-05-25 15:16:31,476 INFO /usr/local/bin/bench --site newsmart.local list-apps
2025-05-25 15:16:33,134 INFO /usr/local/bin/bench --site newsmart.local execute frappe.db.get_single_value --args ['WhatsApp Settings', 'enable_whatsapp']
2025-05-25 15:16:34,745 INFO /usr/local/bin/bench --site newsmart.local execute frappe.get_meta --args ['WhatsApp Sender Account']
2025-05-25 15:16:36,735 INFO /usr/local/bin/bench --site newsmart.local execute frappe.get_meta --args ['WhatsApp Message Log']
2025-05-25 15:16:39,203 INFO /usr/local/bin/bench build --app whatsapp
2025-05-25 15:25:15,066 INFO /usr/local/bin/bench build --app whatsapp
2025-05-25 15:25:41,638 INFO /usr/local/bin/bench --site newsmart.local clear-cache
2025-05-25 15:26:42,705 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-25 15:27:05,740 INFO /usr/local/bin/bench restart
2025-05-25 15:27:07,357 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-25 15:27:08,280 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-25 15:27:08,281 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-05-25 15:27:10,885 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-25 15:27:30,002 INFO /usr/local/bin/bench start
2025-05-25 15:29:15,686 INFO /usr/local/bin/bench start
2025-05-25 15:29:16,229 INFO /usr/local/bin/bench worker
2025-05-25 15:29:16,325 INFO /usr/local/bin/bench schedule
2025-05-25 15:29:16,358 INFO /usr/local/bin/bench watch
2025-05-25 15:29:16,379 INFO /usr/local/bin/bench serve --port 8000
2025-05-25 15:32:11,464 INFO /usr/local/bin/bench build --app whatsapp
2025-05-25 15:32:21,243 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-25 15:58:21,703 INFO /usr/local/bin/bench build --app whatsapp
2025-05-25 15:58:56,953 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-25 16:04:46,431 INFO /usr/local/bin/bench --site site1.local console
2025-05-25 16:07:02,048 INFO /usr/local/bin/bench build --app whatsapp
2025-05-25 16:07:19,516 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-25 16:14:22,822 INFO /usr/local/bin/bench build --app whatsapp
2025-05-25 16:14:28,543 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-25 16:18:36,025 INFO /usr/local/bin/bench build --app whatsapp
2025-05-25 16:19:20,368 INFO /usr/local/bin/bench build --app whatsapp
2025-05-25 16:19:48,389 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-25 17:02:03,904 INFO /usr/local/bin/bench build --app whatsapp
2025-05-25 17:02:22,876 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-26 13:01:18,833 INFO /usr/local/bin/bench start
2025-05-26 13:01:20,664 INFO /usr/local/bin/bench schedule
2025-05-26 13:01:20,683 INFO /usr/local/bin/bench serve --port 8000
2025-05-26 13:01:20,684 INFO /usr/local/bin/bench worker
2025-05-26 13:01:20,898 INFO /usr/local/bin/bench watch
2025-05-26 13:09:31,294 INFO /usr/local/bin/bench new-app interface_customization
2025-05-26 13:09:31,333 LOG creating new app interface_customization
2025-05-26 13:11:06,614 LOG Installing interface_customization
2025-05-26 13:11:06,621 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/interface_customization 
2025-05-26 13:11:18,501 DEBUG bench build --app interface_customization
2025-05-26 13:11:18,885 INFO /usr/local/bin/bench build --app interface_customization
2025-05-26 13:11:45,273 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-26 13:11:46,969 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-26 13:11:46,970 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-05-26 13:11:50,439 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-26 13:11:58,422 INFO /usr/local/bin/bench --site newsmart.local install-app interface_customization
2025-05-26 13:12:46,160 INFO /usr/local/bin/bench --site site1.local install-app interface_customization
2025-05-26 13:20:11,263 INFO /usr/local/bin/bench build --app interface_customization
2025-05-26 13:20:27,723 INFO /usr/local/bin/bench restart
2025-05-26 13:20:34,925 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-26 13:20:35,393 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-26 13:20:35,393 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-05-26 13:20:36,374 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-26 13:20:49,239 INFO /usr/local/bin/bench start
2025-05-26 13:20:50,347 INFO /usr/local/bin/bench serve --port 8000
2025-05-26 13:20:50,366 INFO /usr/local/bin/bench watch
2025-05-26 13:20:50,408 INFO /usr/local/bin/bench worker
2025-05-26 13:20:50,432 INFO /usr/local/bin/bench schedule
2025-05-26 13:21:10,964 INFO /usr/local/bin/bench --site site1.local migrate
2025-05-26 13:26:15,462 INFO /usr/local/bin/bench --site site1.local execute interface_customization.install.after_install
2025-05-26 13:26:42,517 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-26 13:27:10,277 INFO /usr/local/bin/bench start
2025-05-26 13:49:44,025 INFO /usr/local/bin/bench build --app interface_customization
2025-05-26 13:50:26,486 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-26 14:09:30,050 INFO /usr/local/bin/bench start
2025-05-26 14:09:38,811 INFO /usr/local/bin/bench start
2025-05-26 14:29:58,770 INFO /usr/local/bin/bench build --app interface_customization
2025-05-26 14:30:26,399 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-26 14:51:37,302 INFO /usr/local/bin/bench start
2025-05-26 14:51:37,997 INFO /usr/local/bin/bench watch
2025-05-26 14:51:38,012 INFO /usr/local/bin/bench serve --port 8000
2025-05-26 14:51:38,085 INFO /usr/local/bin/bench worker
2025-05-26 14:51:38,352 INFO /usr/local/bin/bench schedule
2025-05-26 14:58:23,016 INFO /usr/local/bin/bench build --app interface_customization
2025-05-26 14:58:51,237 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-26 15:02:24,147 INFO /usr/local/bin/bench build --app interface_customization
2025-05-26 15:02:50,888 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-26 15:19:09,751 INFO /usr/local/bin/bench build --app interface_customization
2025-05-26 15:19:29,924 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-26 15:29:11,088 INFO /usr/local/bin/bench build --app interface_customization
2025-05-26 15:29:37,254 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-26 15:31:10,041 INFO /usr/local/bin/bench build --app interface_customization
2025-05-26 15:31:36,362 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-26 15:32:32,446 INFO /usr/local/bin/bench build --app interface_customization
2025-05-26 15:33:02,001 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-26 15:41:07,704 INFO /usr/local/bin/bench build --app interface_customization
2025-05-26 15:41:40,525 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-26 16:42:47,667 INFO /usr/local/bin/bench build --app interface_customization
2025-05-26 16:43:12,259 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-26 16:48:41,994 INFO /usr/local/bin/bench --site site1.local console
2025-05-26 17:02:02,742 INFO /usr/local/bin/bench start
2025-05-26 17:02:03,866 INFO /usr/local/bin/bench watch
2025-05-26 17:02:03,906 INFO /usr/local/bin/bench worker
2025-05-26 17:02:03,990 INFO /usr/local/bin/bench serve --port 8000
2025-05-26 17:02:04,092 INFO /usr/local/bin/bench schedule
2025-05-26 17:11:06,595 INFO /usr/local/bin/bench build --app interface_customization
2025-05-26 17:12:01,506 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-26 17:18:57,310 INFO /usr/local/bin/bench build --app interface_customization
2025-05-26 17:19:40,057 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-26 17:20:17,619 INFO /usr/local/bin/bench restart
2025-05-26 17:20:25,465 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-26 17:20:25,999 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-26 17:20:26,000 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-05-26 17:20:27,641 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-26 17:27:28,840 INFO /usr/local/bin/bench start --skip-redis-config-generation
2025-05-26 17:27:28,870 WARNING /usr/local/bin/bench start --skip-redis-config-generation executed with exit code 2
2025-05-26 17:27:31,540 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-26 17:27:48,559 INFO /usr/local/bin/bench start
2025-05-27 12:45:17,300 INFO /usr/local/bin/bench start
2025-05-27 12:45:17,820 INFO /usr/local/bin/bench schedule
2025-05-27 12:45:17,887 INFO /usr/local/bin/bench serve --port 8000
2025-05-27 12:45:17,983 INFO /usr/local/bin/bench worker
2025-05-27 12:45:18,056 INFO /usr/local/bin/bench watch
2025-05-27 12:55:16,390 INFO /usr/local/bin/bench build --app interface_customization
2025-05-27 12:56:00,059 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-27 13:05:49,076 INFO /usr/local/bin/bench build --app interface_customization
2025-05-27 13:06:11,432 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-27 13:18:28,252 INFO /usr/local/bin/bench build --app interface_customization
2025-05-27 13:18:54,630 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-27 16:51:13,661 INFO /usr/local/bin/bench start
2025-05-27 16:51:14,366 INFO /usr/local/bin/bench worker
2025-05-27 16:51:14,376 INFO /usr/local/bin/bench watch
2025-05-27 16:51:14,383 INFO /usr/local/bin/bench schedule
2025-05-27 16:51:14,436 INFO /usr/local/bin/bench serve --port 8000
2025-05-28 14:22:08,673 INFO /usr/local/bin/bench start
2025-05-28 14:22:11,749 INFO /usr/local/bin/bench serve --port 8000
2025-05-28 14:22:11,848 INFO /usr/local/bin/bench schedule
2025-05-28 14:22:12,056 INFO /usr/local/bin/bench worker
2025-05-28 14:22:12,382 INFO /usr/local/bin/bench watch
2025-05-28 14:31:07,961 INFO /usr/local/bin/bench start
2025-05-28 14:31:09,182 INFO /usr/local/bin/bench watch
2025-05-28 14:31:09,221 INFO /usr/local/bin/bench serve --port 8000
2025-05-28 14:31:09,331 INFO /usr/local/bin/bench worker
2025-05-28 14:31:09,387 INFO /usr/local/bin/bench schedule
2025-05-28 14:52:42,889 INFO /usr/local/bin/bench restart
2025-05-28 14:52:50,884 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-28 14:52:51,765 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-28 14:52:51,768 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-05-28 14:52:54,237 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-28 14:53:08,351 INFO /usr/local/bin/bench clear-cache
2025-05-28 14:55:12,963 INFO /usr/local/bin/bench start
2025-05-28 14:55:13,405 INFO /usr/local/bin/bench watch
2025-05-28 14:55:13,447 INFO /usr/local/bin/bench serve --port 8000
2025-05-28 14:55:13,467 INFO /usr/local/bin/bench schedule
2025-05-28 14:55:13,505 INFO /usr/local/bin/bench worker
2025-05-28 14:57:25,437 INFO /usr/local/bin/bench start
2025-05-28 14:57:25,785 INFO /usr/local/bin/bench serve --port 8000
2025-05-28 14:57:25,836 INFO /usr/local/bin/bench worker
2025-05-28 14:57:25,884 INFO /usr/local/bin/bench watch
2025-05-28 14:57:25,886 INFO /usr/local/bin/bench schedule
2025-05-28 14:59:05,597 INFO /usr/local/bin/bench start
2025-05-28 15:00:54,959 INFO /usr/local/bin/bench build --app interface_customization
2025-05-28 15:01:51,816 INFO /usr/local/bin/bench clear-cache
2025-05-28 15:01:57,589 INFO /usr/local/bin/bench restart
2025-05-28 15:01:59,075 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-28 15:01:59,899 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-28 15:01:59,899 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-05-28 15:02:00,953 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-28 15:02:20,137 INFO /usr/local/bin/bench --site all clear-cache
2025-05-28 15:02:46,637 INFO /usr/local/bin/bench start
2025-05-28 15:05:17,067 INFO /usr/local/bin/bench --site all clear-website-cache
2025-05-28 15:05:42,716 INFO /usr/local/bin/bench start --skip-redis-config-generation
2025-05-28 15:05:42,864 WARNING /usr/local/bin/bench start --skip-redis-config-generation executed with exit code 2
2025-05-28 15:05:45,489 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-28 15:06:16,912 INFO /usr/local/bin/bench status
2025-05-28 15:06:50,483 INFO /usr/local/bin/bench --site all list-apps
2025-05-31 13:18:41,662 INFO /usr/local/bin/bench start
2025-05-31 13:18:43,142 INFO /usr/local/bin/bench schedule
2025-05-31 13:18:43,193 INFO /usr/local/bin/bench serve --port 8000
2025-05-31 13:18:43,309 INFO /usr/local/bin/bench watch
2025-05-31 13:18:43,443 INFO /usr/local/bin/bench worker
2025-05-31 13:37:36,000 INFO /usr/local/bin/bench start
2025-05-31 13:37:36,914 INFO /usr/local/bin/bench worker
2025-05-31 13:37:36,918 INFO /usr/local/bin/bench schedule
2025-05-31 13:37:36,950 INFO /usr/local/bin/bench watch
2025-05-31 13:37:37,065 INFO /usr/local/bin/bench serve --port 8000
2025-05-31 14:03:49,355 INFO /usr/local/bin/bench --site site1.local migrate
2025-05-31 14:05:16,543 INFO /usr/local/bin/bench restart
2025-05-31 14:05:30,778 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-31 14:05:32,774 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-31 14:05:32,776 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-05-31 14:05:40,076 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-31 14:05:51,824 INFO /usr/local/bin/bench start
2025-05-31 14:06:54,220 INFO /usr/local/bin/bench --site site1.local execute frappe.db.get_value --args ['DocType', 'Download Backup', 'name']
2025-05-31 14:07:16,025 INFO /usr/local/bin/bench --site site1.local execute frappe.get_doc --args {'doctype': 'Download Backup', 'enable_auto_backup': 0, 'backup_count': 7, 'backup_format': 'SQL'}
2025-05-31 14:07:56,542 INFO /usr/local/bin/bench --site site1.local console
2025-05-31 14:35:46,661 INFO /usr/local/bin/bench start
2025-05-31 14:35:47,576 INFO /usr/local/bin/bench serve --port 8000
2025-05-31 14:35:47,616 INFO /usr/local/bin/bench watch
2025-05-31 14:35:47,628 INFO /usr/local/bin/bench worker
2025-05-31 14:35:48,000 INFO /usr/local/bin/bench schedule
2025-05-31 14:38:33,154 INFO /usr/local/bin/bench start
2025-05-31 14:38:34,037 INFO /usr/local/bin/bench serve --port 8000
2025-05-31 14:38:34,210 INFO /usr/local/bin/bench worker
2025-05-31 14:38:34,224 INFO /usr/local/bin/bench schedule
2025-05-31 14:38:34,674 INFO /usr/local/bin/bench watch
2025-05-31 14:41:11,214 INFO /usr/local/bin/bench start
2025-05-31 14:41:12,952 INFO /usr/local/bin/bench schedule
2025-05-31 14:41:13,018 INFO /usr/local/bin/bench serve --port 8000
2025-05-31 14:41:13,053 INFO /usr/local/bin/bench watch
2025-05-31 14:41:13,160 INFO /usr/local/bin/bench worker
2025-05-31 14:49:20,158 INFO /usr/local/bin/bench start
2025-05-31 14:49:21,151 INFO /usr/local/bin/bench schedule
2025-05-31 14:49:21,193 INFO /usr/local/bin/bench watch
2025-05-31 14:49:21,216 INFO /usr/local/bin/bench worker
2025-05-31 14:49:21,478 INFO /usr/local/bin/bench serve --port 8000
2025-05-31 14:52:50,013 INFO /usr/local/bin/bench start
2025-05-31 14:52:51,232 INFO /usr/local/bin/bench watch
2025-05-31 14:52:51,492 INFO /usr/local/bin/bench schedule
2025-05-31 14:52:51,544 INFO /usr/local/bin/bench serve --port 8000
2025-05-31 14:52:51,545 INFO /usr/local/bin/bench worker
2025-05-31 15:08:49,020 INFO /usr/local/bin/bench build --app interface_customization
2025-05-31 15:09:50,280 INFO /usr/local/bin/bench restart
2025-05-31 15:10:20,207 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-31 15:10:20,994 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-31 15:10:20,995 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-05-31 15:10:24,589 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-31 15:15:58,890 INFO /usr/local/bin/bench build --app interface_customization
2025-05-31 15:17:00,058 INFO /usr/local/bin/bench clear-cache
2025-05-31 15:17:02,209 INFO /usr/local/bin/bench clear-website-cache
2025-05-31 15:26:54,899 INFO /usr/local/bin/bench build --app interface_customization
2025-05-31 15:40:59,170 INFO /usr/local/bin/bench build --app interface_customization
2025-05-31 16:52:58,512 INFO /usr/local/bin/bench build --app interface_customization
2025-05-31 16:58:28,525 INFO /usr/local/bin/bench build --app interface_customization
2025-05-31 16:59:38,412 INFO /usr/local/bin/bench start
2025-05-31 16:59:39,183 INFO /usr/local/bin/bench schedule
2025-05-31 16:59:39,200 INFO /usr/local/bin/bench worker
2025-05-31 16:59:39,220 INFO /usr/local/bin/bench serve --port 8000
2025-05-31 16:59:39,264 INFO /usr/local/bin/bench watch
2025-06-01 12:37:54,829 INFO /usr/local/bin/bench start
2025-06-01 12:37:55,550 INFO /usr/local/bin/bench serve --port 8000
2025-06-01 12:37:55,555 INFO /usr/local/bin/bench schedule
2025-06-01 12:37:55,699 INFO /usr/local/bin/bench watch
2025-06-01 12:37:55,822 INFO /usr/local/bin/bench worker
2025-06-01 13:11:44,961 INFO /usr/local/bin/bench build --app interface_customization
2025-06-01 13:16:59,473 INFO /usr/local/bin/bench build --app interface_customization
2025-06-01 13:29:56,074 INFO /usr/local/bin/bench build --app interface_customization
2025-06-01 13:37:52,516 INFO /usr/local/bin/bench build --app interface_customization
2025-06-01 13:39:43,283 INFO /usr/local/bin/bench clear-cache
2025-06-01 14:07:27,741 INFO /usr/local/bin/bench build --app interface_customization
2025-06-01 14:08:06,812 INFO /usr/local/bin/bench clear-cache
2025-06-01 14:14:37,639 INFO /usr/local/bin/bench build --app interface_customization
2025-06-01 14:14:46,695 INFO /usr/local/bin/bench clear-cache
2025-06-01 14:32:18,873 INFO /usr/local/bin/bench build --app interface_customization
2025-06-01 14:32:25,686 INFO /usr/local/bin/bench clear-cache
2025-06-01 14:55:52,071 INFO /usr/local/bin/bench build --app interface_customization
2025-06-01 14:56:17,014 INFO /usr/local/bin/bench clear-cache
2025-06-01 15:00:27,641 INFO /usr/local/bin/bench build --app interface_customization
2025-06-01 15:08:52,575 INFO /usr/local/bin/bench build --app interface_customization
2025-06-01 15:14:46,749 INFO /usr/local/bin/bench build --app interface_customization
