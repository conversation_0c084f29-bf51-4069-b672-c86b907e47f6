2025-05-25 13:24:23,048 ERROR frappe Could not take error snapshot: (2003, "Can't connect to MySQL server on '127.0.0.1' ([<PERSON><PERSON><PERSON> 111] Connection refused)")
Site: site1.local
Form Dict: {'doctype': 'WhatsApp Sender Account', 'fields': '["`tabWhatsApp Sender Account`.`name`","`tabWhatsApp Sender Account`.`owner`","`tabWhatsApp Sender Account`.`creation`","`tabWhatsApp Sender Account`.`modified`","`tabWhatsApp Sender Account`.`modified_by`","`tabWhatsApp Sender Account`.`_user_tags`","`tabWhatsApp Sender Account`.`_comments`","`tabWhatsApp Sender Account`.`_assign`","`tabWhatsApp Sender Account`.`_liked_by`","`tabWhatsApp Sender Account`.`docstatus`","`tabWhatsApp Sender Account`.`idx`","`tabWhatsApp Sender Account`.`account_name`","`tabWhatsApp Sender Account`.`phone_number`","`tabWhatsApp Sender Account`.`status`"]', 'filters': '[]', 'order_by': '`tabWhatsApp Sender Account`.`modified` desc', 'start': '0', 'page_length': '20', 'view': 'List', 'group_by': '', 'with_comment_count': '1', 'cmd': 'frappe.desk.reportview.get'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 649, in connect
    sock = socket.create_connection(
  File "/usr/lib/python3.10/socket.py", line 845, in create_connection
    raise err
  File "/usr/lib/python3.10/socket.py", line 833, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 114, in application
    response = frappe.api.handle(request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/api/__init__.py", line 49, in handle
    data = endpoint(**arguments)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/api/v1.py", line 36, in handle_rpc_call
    return frappe.handler.handle()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/handler.py", line 50, in handle
    data = execute_cmd(cmd)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/handler.py", line 86, in execute_cmd
    return frappe.call(method, **frappe.form_dict)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/typing_validations.py", line 31, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 879, in wrapper_fn
    retval = fn(*args, **get_newargs(fn, kwargs))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/desk/reportview.py", line 28, in get
    if is_virtual_doctype(args.doctype):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/caching.py", line 121, in site_cache_wrapper
    _SITE_CACHE[func_key][frappe.local.site][func_call_key] = func(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/utils/__init__.py", line 135, in is_virtual_doctype
    return frappe.db.get_value("DocType", doctype, "is_virtual")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 515, in get_value
    result = self.get_values(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 619, in get_values
    out = self._get_values_from_table(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 892, in _get_values_from_table
    return query.run(as_dict=as_dict, debug=debug, update=update, run=run, pluck=pluck)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 716, in connect
    raise exc
pymysql.err.OperationalError: (2003, "Can't connect to MySQL server on '127.0.0.1' ([Errno 111] Connection refused)")

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 649, in connect
    sock = socket.create_connection(
  File "/usr/lib/python3.10/socket.py", line 845, in create_connection
    raise err
  File "/usr/lib/python3.10/socket.py", line 833, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 86, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 61, in log_error
    error_log = frappe.get_doc(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1291, in get_doc
    doc = frappe.model.document.get_doc(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 67, in get_controller
    return import_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 82, in import_controller
    doctype_info = frappe.db.get_value("DocType", doctype, fieldname="*")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 515, in get_value
    result = self.get_values(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 619, in get_values
    out = self._get_values_from_table(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 892, in _get_values_from_table
    return query.run(as_dict=as_dict, debug=debug, update=update, run=run, pluck=pluck)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 716, in connect
    raise exc
pymysql.err.OperationalError: (2003, "Can't connect to MySQL server on '127.0.0.1' ([Errno 111] Connection refused)")
2025-05-25 13:25:18,542 ERROR frappe Could not take error snapshot: (2003, "Can't connect to MySQL server on '127.0.0.1' ([Errno 111] Connection refused)")
Site: site1.local
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 649, in connect
    sock = socket.create_connection(
  File "/usr/lib/python3.10/socket.py", line 845, in create_connection
    raise err
  File "/usr/lib/python3.10/socket.py", line 833, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/website/serve.py", line 19, in get_response
    endpoint, renderer_instance = path_resolver.resolve()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/website/path_resolver.py", line 38, in resolve
    resolve_redirect(self.path, request.query_string)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/website/path_resolver.py", line 118, in resolve_redirect
    redirects += frappe.get_all(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 2015, in get_all
    return get_list(doctype, *args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1990, in get_list
    return frappe.model.db_query.DatabaseQuery(doctype).execute(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/db_query.py", line 168, in execute
    if is_virtual_doctype(self.doctype):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/caching.py", line 121, in site_cache_wrapper
    _SITE_CACHE[func_key][frappe.local.site][func_call_key] = func(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/utils/__init__.py", line 135, in is_virtual_doctype
    return frappe.db.get_value("DocType", doctype, "is_virtual")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 515, in get_value
    result = self.get_values(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 619, in get_values
    out = self._get_values_from_table(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 892, in _get_values_from_table
    return query.run(as_dict=as_dict, debug=debug, update=update, run=run, pluck=pluck)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 716, in connect
    raise exc
pymysql.err.OperationalError: (2003, "Can't connect to MySQL server on '127.0.0.1' ([Errno 111] Connection refused)")

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 649, in connect
    sock = socket.create_connection(
  File "/usr/lib/python3.10/socket.py", line 845, in create_connection
    raise err
  File "/usr/lib/python3.10/socket.py", line 833, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 123, in application
    response = get_response()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/website/serve.py", line 28, in get_response
    response = ErrorPage(exception=e).render()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/website/page_renderers/template_page.py", line 84, in render
    html = self.get_html()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/website/utils.py", line 531, in cache_html_decorator
    html = func(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/website/page_renderers/template_page.py", line 92, in get_html
    self.init_context()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/website/page_renderers/error_page.py", line 14, in init_context
    super().init_context()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/website/page_renderers/base_template_page.py", line 15, in init_context
    self.context.update(get_website_settings())
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/website/doctype/website_settings/website_settings.py", line 263, in get_website_settings
    context.boot = get_boot_data()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/website/utils.py", line 169, in get_boot_data
    apps = get_apps() or []
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/typing_validations.py", line 31, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/apps.py", line 15, in get_apps
    allowed_workspaces = get_workspace_sidebar_items().get("pages")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/typing_validations.py", line 31, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/desk/desktop.py", line 451, in get_workspace_sidebar_items
    all_pages = frappe.get_all(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 2015, in get_all
    return get_list(doctype, *args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1990, in get_list
    return frappe.model.db_query.DatabaseQuery(doctype).execute(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/db_query.py", line 168, in execute
    if is_virtual_doctype(self.doctype):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/caching.py", line 121, in site_cache_wrapper
    _SITE_CACHE[func_key][frappe.local.site][func_call_key] = func(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/utils/__init__.py", line 135, in is_virtual_doctype
    return frappe.db.get_value("DocType", doctype, "is_virtual")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 515, in get_value
    result = self.get_values(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 619, in get_values
    out = self._get_values_from_table(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 892, in _get_values_from_table
    return query.run(as_dict=as_dict, debug=debug, update=update, run=run, pluck=pluck)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 716, in connect
    raise exc
pymysql.err.OperationalError: (2003, "Can't connect to MySQL server on '127.0.0.1' ([Errno 111] Connection refused)")

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 649, in connect
    sock = socket.create_connection(
  File "/usr/lib/python3.10/socket.py", line 845, in create_connection
    raise err
  File "/usr/lib/python3.10/socket.py", line 833, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 86, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 61, in log_error
    error_log = frappe.get_doc(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1291, in get_doc
    doc = frappe.model.document.get_doc(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 67, in get_controller
    return import_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 82, in import_controller
    doctype_info = frappe.db.get_value("DocType", doctype, fieldname="*")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 515, in get_value
    result = self.get_values(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 619, in get_values
    out = self._get_values_from_table(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 892, in _get_values_from_table
    return query.run(as_dict=as_dict, debug=debug, update=update, run=run, pluck=pluck)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 716, in connect
    raise exc
pymysql.err.OperationalError: (2003, "Can't connect to MySQL server on '127.0.0.1' ([Errno 111] Connection refused)")
2025-05-25 13:25:22,495 ERROR frappe Could not take error snapshot: (2003, "Can't connect to MySQL server on '127.0.0.1' ([Errno 111] Connection refused)")
Site: site1.local
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 649, in connect
    sock = socket.create_connection(
  File "/usr/lib/python3.10/socket.py", line 845, in create_connection
    raise err
  File "/usr/lib/python3.10/socket.py", line 833, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/website/serve.py", line 19, in get_response
    endpoint, renderer_instance = path_resolver.resolve()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/website/path_resolver.py", line 38, in resolve
    resolve_redirect(self.path, request.query_string)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/website/path_resolver.py", line 118, in resolve_redirect
    redirects += frappe.get_all(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 2015, in get_all
    return get_list(doctype, *args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1990, in get_list
    return frappe.model.db_query.DatabaseQuery(doctype).execute(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/db_query.py", line 168, in execute
    if is_virtual_doctype(self.doctype):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/caching.py", line 121, in site_cache_wrapper
    _SITE_CACHE[func_key][frappe.local.site][func_call_key] = func(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/utils/__init__.py", line 135, in is_virtual_doctype
    return frappe.db.get_value("DocType", doctype, "is_virtual")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 515, in get_value
    result = self.get_values(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 619, in get_values
    out = self._get_values_from_table(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 892, in _get_values_from_table
    return query.run(as_dict=as_dict, debug=debug, update=update, run=run, pluck=pluck)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 716, in connect
    raise exc
pymysql.err.OperationalError: (2003, "Can't connect to MySQL server on '127.0.0.1' ([Errno 111] Connection refused)")

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 649, in connect
    sock = socket.create_connection(
  File "/usr/lib/python3.10/socket.py", line 845, in create_connection
    raise err
  File "/usr/lib/python3.10/socket.py", line 833, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 123, in application
    response = get_response()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/website/serve.py", line 28, in get_response
    response = ErrorPage(exception=e).render()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/website/page_renderers/template_page.py", line 84, in render
    html = self.get_html()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/website/utils.py", line 531, in cache_html_decorator
    html = func(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/website/page_renderers/template_page.py", line 92, in get_html
    self.init_context()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/website/page_renderers/error_page.py", line 14, in init_context
    super().init_context()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/website/page_renderers/base_template_page.py", line 15, in init_context
    self.context.update(get_website_settings())
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/website/doctype/website_settings/website_settings.py", line 263, in get_website_settings
    context.boot = get_boot_data()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/website/utils.py", line 169, in get_boot_data
    apps = get_apps() or []
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/typing_validations.py", line 31, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/apps.py", line 15, in get_apps
    allowed_workspaces = get_workspace_sidebar_items().get("pages")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/typing_validations.py", line 31, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/desk/desktop.py", line 451, in get_workspace_sidebar_items
    all_pages = frappe.get_all(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 2015, in get_all
    return get_list(doctype, *args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1990, in get_list
    return frappe.model.db_query.DatabaseQuery(doctype).execute(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/db_query.py", line 168, in execute
    if is_virtual_doctype(self.doctype):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/caching.py", line 121, in site_cache_wrapper
    _SITE_CACHE[func_key][frappe.local.site][func_call_key] = func(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/utils/__init__.py", line 135, in is_virtual_doctype
    return frappe.db.get_value("DocType", doctype, "is_virtual")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 515, in get_value
    result = self.get_values(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 619, in get_values
    out = self._get_values_from_table(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 892, in _get_values_from_table
    return query.run(as_dict=as_dict, debug=debug, update=update, run=run, pluck=pluck)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 716, in connect
    raise exc
pymysql.err.OperationalError: (2003, "Can't connect to MySQL server on '127.0.0.1' ([Errno 111] Connection refused)")

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 649, in connect
    sock = socket.create_connection(
  File "/usr/lib/python3.10/socket.py", line 845, in create_connection
    raise err
  File "/usr/lib/python3.10/socket.py", line 833, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 86, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 61, in log_error
    error_log = frappe.get_doc(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1291, in get_doc
    doc = frappe.model.document.get_doc(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 67, in get_controller
    return import_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 82, in import_controller
    doctype_info = frappe.db.get_value("DocType", doctype, fieldname="*")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 515, in get_value
    result = self.get_values(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 619, in get_values
    out = self._get_values_from_table(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 892, in _get_values_from_table
    return query.run(as_dict=as_dict, debug=debug, update=update, run=run, pluck=pluck)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 716, in connect
    raise exc
pymysql.err.OperationalError: (2003, "Can't connect to MySQL server on '127.0.0.1' ([Errno 111] Connection refused)")
2025-05-25 13:25:32,473 ERROR frappe Could not take error snapshot: (2003, "Can't connect to MySQL server on '127.0.0.1' ([Errno 111] Connection refused)")
Site: site1.local
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 649, in connect
    sock = socket.create_connection(
  File "/usr/lib/python3.10/socket.py", line 845, in create_connection
    raise err
  File "/usr/lib/python3.10/socket.py", line 833, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/website/serve.py", line 19, in get_response
    endpoint, renderer_instance = path_resolver.resolve()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/website/path_resolver.py", line 38, in resolve
    resolve_redirect(self.path, request.query_string)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/website/path_resolver.py", line 118, in resolve_redirect
    redirects += frappe.get_all(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 2015, in get_all
    return get_list(doctype, *args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1990, in get_list
    return frappe.model.db_query.DatabaseQuery(doctype).execute(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/db_query.py", line 168, in execute
    if is_virtual_doctype(self.doctype):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/caching.py", line 121, in site_cache_wrapper
    _SITE_CACHE[func_key][frappe.local.site][func_call_key] = func(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/utils/__init__.py", line 135, in is_virtual_doctype
    return frappe.db.get_value("DocType", doctype, "is_virtual")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 515, in get_value
    result = self.get_values(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 619, in get_values
    out = self._get_values_from_table(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 892, in _get_values_from_table
    return query.run(as_dict=as_dict, debug=debug, update=update, run=run, pluck=pluck)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 716, in connect
    raise exc
pymysql.err.OperationalError: (2003, "Can't connect to MySQL server on '127.0.0.1' ([Errno 111] Connection refused)")

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 649, in connect
    sock = socket.create_connection(
  File "/usr/lib/python3.10/socket.py", line 845, in create_connection
    raise err
  File "/usr/lib/python3.10/socket.py", line 833, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 123, in application
    response = get_response()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/website/serve.py", line 28, in get_response
    response = ErrorPage(exception=e).render()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/website/page_renderers/template_page.py", line 84, in render
    html = self.get_html()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/website/utils.py", line 531, in cache_html_decorator
    html = func(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/website/page_renderers/template_page.py", line 92, in get_html
    self.init_context()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/website/page_renderers/error_page.py", line 14, in init_context
    super().init_context()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/website/page_renderers/base_template_page.py", line 15, in init_context
    self.context.update(get_website_settings())
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/website/doctype/website_settings/website_settings.py", line 263, in get_website_settings
    context.boot = get_boot_data()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/website/utils.py", line 169, in get_boot_data
    apps = get_apps() or []
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/typing_validations.py", line 31, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/apps.py", line 15, in get_apps
    allowed_workspaces = get_workspace_sidebar_items().get("pages")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/typing_validations.py", line 31, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/desk/desktop.py", line 451, in get_workspace_sidebar_items
    all_pages = frappe.get_all(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 2015, in get_all
    return get_list(doctype, *args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1990, in get_list
    return frappe.model.db_query.DatabaseQuery(doctype).execute(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/db_query.py", line 168, in execute
    if is_virtual_doctype(self.doctype):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/caching.py", line 121, in site_cache_wrapper
    _SITE_CACHE[func_key][frappe.local.site][func_call_key] = func(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/utils/__init__.py", line 135, in is_virtual_doctype
    return frappe.db.get_value("DocType", doctype, "is_virtual")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 515, in get_value
    result = self.get_values(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 619, in get_values
    out = self._get_values_from_table(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 892, in _get_values_from_table
    return query.run(as_dict=as_dict, debug=debug, update=update, run=run, pluck=pluck)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 716, in connect
    raise exc
pymysql.err.OperationalError: (2003, "Can't connect to MySQL server on '127.0.0.1' ([Errno 111] Connection refused)")

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 649, in connect
    sock = socket.create_connection(
  File "/usr/lib/python3.10/socket.py", line 845, in create_connection
    raise err
  File "/usr/lib/python3.10/socket.py", line 833, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 86, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 61, in log_error
    error_log = frappe.get_doc(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1291, in get_doc
    doc = frappe.model.document.get_doc(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 67, in get_controller
    return import_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 82, in import_controller
    doctype_info = frappe.db.get_value("DocType", doctype, fieldname="*")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 515, in get_value
    result = self.get_values(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 619, in get_values
    out = self._get_values_from_table(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 892, in _get_values_from_table
    return query.run(as_dict=as_dict, debug=debug, update=update, run=run, pluck=pluck)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 716, in connect
    raise exc
pymysql.err.OperationalError: (2003, "Can't connect to MySQL server on '127.0.0.1' ([Errno 111] Connection refused)")
2025-05-26 14:54:05,084 ERROR frappe New Exception collected in error log
Site: site1.local
Form Dict: {'doc': '{"docstatus":0,"doctype":"POS Invoice","name":"new-pos-invoice-uzpatgcmeg","__islocal":1,"__unsaved":1,"owner":"Administrator","naming_series":"ACC-PSINV-.YYYY.-","is_pos":1,"is_return":0,"update_billed_amount_in_sales_order":0,"update_billed_amount_in_delivery_note":1,"company":"نيو سمارت","posting_date":"2025-05-26","set_posting_time":0,"currency":"YER","selling_price_list":"البيع القياسية","price_list_currency":"YER","ignore_pricing_rule":0,"update_stock":1,"items":[{"docstatus":0,"doctype":"POS Invoice Item","name":"new-pos-invoice-item-seeefszvvr","__islocal":1,"__unsaved":1,"owner":"Administrator","has_item_scanned":0,"stock_uom":"Nos","margin_type":"","is_free_item":0,"grant_commission":1,"delivered_by_supplier":0,"is_fixed_asset":0,"enable_deferred_revenue":0,"use_serial_batch_fields":1,"allow_zero_valuation_rate":0,"cost_center":"رئيسي - نس","page_break":0,"parent":"new-pos-invoice-uzpatgcmeg","parentfield":"items","parenttype":"POS Invoice","idx":1,"qty":2,"uom":"Nos","conversion_factor":1,"stock_qty":2,"price_list_rate":500,"base_price_list_rate":500,"margin_rate_or_amount":0,"rate_with_margin":500,"discount_percentage":0,"discount_amount":0,"base_rate_with_margin":500,"rate":500,"amount":1000,"base_rate":500,"base_amount":1000,"net_rate":500,"net_amount":1000,"base_net_rate":500,"base_net_amount":1000,"weight_per_unit":0,"total_weight":0,"actual_batch_qty":0,"actual_qty":48,"delivered_qty":0,"item_code":"test","weight_uom":null,"barcode":null,"pricing_rules":"","item_name":"test","description":"test","image":"","warehouse":"مخازن - نس","income_account":"4110 - مبيعات - نس","expense_account":"5111 - تكلفة البضاعة المباعة - نس","discount_account":null,"provisional_expense_account":null,"has_serial_no":0,"has_batch_no":0,"batch_no":null,"min_order_qty":"","update_stock":0,"last_purchase_rate":0,"transaction_date":"2025-05-26","against_blanket_order":null,"bom_no":null,"item_group":"كل مجموعات الأصناف","brand":null,"manufacturer":null,"manufacturer_part_no":null,"item_tax_rate":"{}","customer_item_code":null,"projected_qty":48,"reserved_qty":0,"has_margin":false,"free_item_data":[],"child_docname":"new-pos-invoice-item-seeefszvvr","stock_uom_rate":500}],"pricing_rules":[],"packed_items":[],"timesheets":[],"total_billing_amount":0,"taxes":[],"redeem_loyalty_points":0,"apply_discount_on":"Grand Total","allocate_advances_automatically":0,"advances":[],"payment_schedule":[],"payments":[{"docstatus":0,"idx":1,"default":1,"mode_of_payment":"نقد","amount":1000,"account":"1110 - نقد - نس","type":"Cash","base_amount":1000,"parent":"new-pos-invoice-uzpatgcmeg","parentfield":"payments","parenttype":"POS Invoice","doctype":"Sales Invoice Payment","__islocal":1,"name":"new-sales-invoice-payment-akxwbduypd"}],"write_off_outstanding_amount_automatically":0,"group_same_items":0,"is_discounted":0,"status":"Draft","party_account_currency":"YER","is_opening":"No","sales_team":[],"posting_time":"17:52:22","set_warehouse":"مخازن - نس","conversion_rate":1,"plc_conversion_rate":1,"idx":0,"pos_profile":"نقد","total_qty":2,"base_total":1000,"base_net_total":1000,"total":1000,"net_total":1000,"total_net_weight":0,"base_total_taxes_and_charges":0,"total_taxes_and_charges":0,"loyalty_points":0,"loyalty_amount":0,"base_discount_amount":0,"additional_discount_percentage":0,"discount_amount":0,"base_grand_total":1000,"base_rounding_adjustment":0,"base_rounded_total":1000,"grand_total":1000,"rounding_adjustment":0,"rounded_total":1000,"total_advance":0,"outstanding_amount":0,"base_paid_amount":1000,"paid_amount":1000,"base_change_amount":0,"change_amount":0,"account_for_change_amount":"1110 - نقد - نس","write_off_amount":0,"base_write_off_amount":0,"write_off_account":"5212 - تقريب - نس","write_off_cost_center":"رئيسي - نس","debit_to":"1310 - مدينون - نس","amount_eligible_for_commission":0,"commission_rate":0,"total_commission":0,"campaign":null,"in_words":"","base_in_words":"","customer_name":"moneer","loyalty_program":null,"customer":"moneer","due_date":"2025-05-26","customer_address":null,"address_display":null,"shipping_address_name":null,"shipping_address":null,"company_address":null,"company_address_display":null,"tax_category":null,"contact_person":null,"contact_display":null,"contact_email":null,"contact_mobile":null,"customer_group":null,"territory":null,"language":"ar","payment_terms_template":null}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-06-01 14:20:47,410 ERROR frappe New Exception collected in error log
Site: site1.local
Form Dict: {'start': '2025-06-01 17:20:51', 'end': '2025-06-01 17:20:23', 'pos_profile': 'نقد', 'cmd': 'erpnext.accounts.doctype.pos_closing_entry.pos_closing_entry.get_pos_invoices'}
2025-06-01 14:21:45,629 ERROR frappe New Exception collected in error log
Site: site1.local
Form Dict: {'start': '2025-06-01 17:21:49', 'end': '2025-06-01 17:21:03', 'pos_profile': 'نقد', 'cmd': 'erpnext.accounts.doctype.pos_closing_entry.pos_closing_entry.get_pos_invoices'}
2025-06-01 14:23:40,955 ERROR frappe New Exception collected in error log
Site: site1.local
Form Dict: {'start': '2025-06-01 17:23:45', 'end': '2025-06-01 17:21:58', 'user': '<EMAIL>', 'cmd': 'erpnext.accounts.doctype.pos_closing_entry.pos_closing_entry.get_pos_invoices'}
