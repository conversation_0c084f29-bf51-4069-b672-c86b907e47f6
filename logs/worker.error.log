/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:58: DeprecationWarning: The `push_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/braintree/dispute.py:80: DeprecationWarning: Use ProtectionLevel enum instead
  warnings.warn("Use ProtectionLevel enum instead", DeprecationWarning)
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/braintree/search.py:79: DeprecationWarning: Use protection_level parameter instead
  warnings.warn("Use protection_level parameter instead", DeprecationWarning)
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:72: DeprecationWarning: The `pop_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:58: DeprecationWarning: The `push_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py:1664: UserWarning: WARNING: module `whatsapp_integration` found in apps `whatsapp_integration` and `erpnext`
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:72: DeprecationWarning: The `pop_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:58: DeprecationWarning: The `push_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py:1664: UserWarning: WARNING: module `whatsapp_integration` found in apps `whatsapp_integration` and `erpnext`
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/braintree/dispute.py:80: DeprecationWarning: Use ProtectionLevel enum instead
  warnings.warn("Use ProtectionLevel enum instead", DeprecationWarning)
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/braintree/search.py:79: DeprecationWarning: Use protection_level parameter instead
  warnings.warn("Use protection_level parameter instead", DeprecationWarning)
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:72: DeprecationWarning: The `pop_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:58: DeprecationWarning: The `push_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py:1664: UserWarning: WARNING: module `whatsapp_integration` found in apps `whatsapp_integration` and `erpnext`
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/braintree/dispute.py:80: DeprecationWarning: Use ProtectionLevel enum instead
  warnings.warn("Use ProtectionLevel enum instead", DeprecationWarning)
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/braintree/search.py:79: DeprecationWarning: Use protection_level parameter instead
  warnings.warn("Use protection_level parameter instead", DeprecationWarning)
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:72: DeprecationWarning: The `pop_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:58: DeprecationWarning: The `push_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py:1664: UserWarning: WARNING: module `whatsapp_integration` found in apps `whatsapp_integration` and `erpnext`
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:72: DeprecationWarning: The `pop_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:58: DeprecationWarning: The `push_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py:1664: UserWarning: WARNING: module `whatsapp_integration` found in apps `whatsapp_integration` and `erpnext`
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:72: DeprecationWarning: The `pop_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 699, in connect
    sock = self.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 700, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1002, in _connect
    raise err
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 990, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib/python3.10/runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/lib/python3.10/runpy.py", line 86, in _run_code
    exec(code, run_globals)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 114, in <module>
    main()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 20, in main
    click.Group(commands=commands)(prog_name="bench")
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1078, in main
    rv = self.invoke(ctx)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 783, in invoke
    return __callback(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/commands/scheduler.py", line 205, in start_worker
    start_worker(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/background_jobs.py", line 318, in start_worker
    worker.work(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 456, in work
    self.teardown()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 1047, in teardown
    self.unsubscribe()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 624, in unsubscribe
    self.pubsub.unsubscribe()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1659, in unsubscribe
    return self.execute_command("UNSUBSCRIBE", *args)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1469, in execute_command
    self.connection = self.connection_pool.get_connection(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1457, in get_connection
    connection.connect()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 705, in connect
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 111 connecting to 127.0.0.1:11000. Connection refused.
/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py:1664: UserWarning: WARNING: module `whatsapp_integration` found in apps `whatsapp_integration` and `erpnext`
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:58: DeprecationWarning: The `push_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:72: DeprecationWarning: The `pop_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
Exception in thread Thread-1:
Traceback (most recent call last):
  File "/usr/lib/python3.10/threading.py", line 1016, in _bootstrap_inner
    self.run()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1799, in run
    pubsub.get_message(ignore_subscribe_messages=True, timeout=sleep_time)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1690, in get_message
    response = self.parse_response(block=(timeout is None), timeout=timeout)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1542, in parse_response
    response = self._execute(conn, try_read)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1518, in _execute
    return conn.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 49, in call_with_retry
    fail(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1520, in <lambda>
    lambda error: self._disconnect_raise_connect(conn, error),
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1507, in _disconnect_raise_connect
    raise error
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1519, in <lambda>
    lambda: command(*args, **kwargs),
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1536, in try_read
    if not conn.can_read(timeout=timeout):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 861, in can_read
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 699, in connect
    return self._parser.can_read(timeout)    sock = self.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry

    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 700, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1002, in _connect
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 444, in can_read
    return self.read_from_socket(timeout=timeout, raise_on_timeout=False)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 455, in read_from_socket
    raise ConnectionError(SERVER_CLOSED_CONNECTION_ERROR)
    raise err
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 990, in _connect
    sock.connect(socket_address)
redis.exceptionsConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib/python3.10/runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/lib/python3.10/runpy.py", line 86, in _run_code
.ConnectionError: Connection closed by server.
    exec(code, run_globals)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 114, in <module>
    main()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 20, in main
    click.Group(commands=commands)(prog_name="bench")
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1078, in main
    rv = self.invoke(ctx)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 783, in invoke
    return __callback(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/commands/scheduler.py", line 205, in start_worker
    start_worker(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/background_jobs.py", line 318, in start_worker
    worker.work(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 456, in work
    self.teardown()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 1046, in teardown
    self.register_death()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 832, in register_death
    p.execute()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 2114, in execute
    conn = self.connection_pool.get_connection("MULTI", self.shard_hint)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1457, in get_connection
    connection.connect()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 705, in connect
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 111 connecting to 127.0.0.1:11000. Connection refused.
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:58: DeprecationWarning: The `push_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:72: DeprecationWarning: The `pop_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:58: DeprecationWarning: The `push_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:72: DeprecationWarning: The `pop_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:58: DeprecationWarning: The `push_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:72: DeprecationWarning: The `pop_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:58: DeprecationWarning: The `push_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:72: DeprecationWarning: The `pop_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:58: DeprecationWarning: The `push_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:72: DeprecationWarning: The `pop_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:58: DeprecationWarning: The `push_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:72: DeprecationWarning: The `pop_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:58: DeprecationWarning: The `push_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:72: DeprecationWarning: The `pop_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:58: DeprecationWarning: The `push_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:72: DeprecationWarning: The `pop_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 874, in read_response
Exception in thread     response = self._parser.read_response(disable_decoding=disable_decoding)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 493, in read_response
    self.read_from_socket()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 453, in read_from_socket
    bufflen = self._sock.recv_into(self._buffer)
ConnectionResetError: [Errno 104] Connection reset by peer

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib/python3.10/runpy.py", line 196, in _run_module_as_main
Thread-1:
Traceback (most recent call last):
  File "/usr/lib/python3.10/threading.py", line 1016, in _bootstrap_inner
    return _run_code(code, main_globals, None,
  File "/usr/lib/python3.10/runpy.py", line 86, in _run_code
    exec(code, run_globals)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 114, in <module>
    main()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 20, in main
    click.Group(commands=commands)(prog_name="bench")
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1157, in __call__
    self.run()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1799, in run
    pubsub.get_message(ignore_subscribe_messages=True, timeout=sleep_time)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1690, in get_message
    response = self.parse_response(block=(timeout is None), timeout=timeout)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1542, in parse_response
    response = self._execute(conn, try_read)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1518, in _execute
    return conn.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 49, in call_with_retry
    fail(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.pyException in thread Thread-1:
Traceback (most recent call last):
  File "/usr/lib/python3.10/threading.py", line 1016, in _bootstrap_inner
    self.run()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1799, in run
    pubsub.get_message(ignore_subscribe_messages=True, timeout=sleep_time)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1690, in get_message
    response = self.parse_response(block=(timeout is None), timeout=timeout)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1542, in parse_response
    response = self._execute(conn, try_read)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1518, in _execute
    return conn.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 49, in call_with_retry
    fail(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1520, in <lambda>
    lambda error: self._disconnect_raise_connect(conn, error),
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1507, in _disconnect_raise_connect
    raise error
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1519, in <lambda>
    lambda: command(*args, **kwargs),
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1536, in try_read
    if not conn.can_read(timeout=timeout):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 861, in can_read
    return self._parser.can_read(timeout)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 444, in can_read
    return self.read_from_socket(timeout=timeout, raise_on_timeout=False)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 455, in read_from_socket
    raise ConnectionError(SERVER_CLOSED_CONNECTION_ERROR)
redis.exceptions.ConnectionError: Connection closed by server.
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 699, in connect
    sock = self.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 700, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1002, in _connect
    raise err
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 990, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib/python3.10/runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/lib/python3.10/runpy.py", line 86, in _run_code
    exec(code, run_globals)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 114, in <module>
    main()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 20, in main
    click.Group(commands=commands)(prog_name="bench")
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1078, in main
    rv = self.invoke(ctx)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 783, in invoke
    return __callback(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/commands/scheduler.py", line 205, in start_worker
    start_worker(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/background_jobs.py", line 318, in start_worker
    worker.work(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 456, in work
    self.teardown()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 1046, in teardown
    self.register_death()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 832, in register_death
    p.execute()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 2114, in execute
    conn = self.connection_pool.get_connection("MULTI", self.shard_hint)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1457, in get_connection
    connection.connect()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 705, in connect
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 111 connecting to 127.0.0.1:11000. Connection refused.
2025-05-24 13:44:52,844 Could not connect to Redis instance: Connection closed by server. Retrying in 1 seconds...
Exception in thread Thread-1:
Traceback (most recent call last):
  File "/usr/lib/python3.10/threading.py", line 1016, in _bootstrap_inner
    self.run()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1799, in run
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 699, in connect
    sock = self.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 700, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1002, in _connect
    raise err
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 990, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib/python3.10/runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/lib/python3.10/runpy.py", line 86, in _run_code
    exec(code, run_globals)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 114, in <module>
    main()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 20, in main
    click.Group(commands=commands)(prog_name="bench")
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1157, in __call__
    pubsub.get_message(ignore_subscribe_messages=True, timeout=sleep_time)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1690, in get_message
    response = self.parse_response(block=(timeout is None), timeout=timeout)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1542, in parse_response
    response = self._execute(conn, try_read)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1518, in _execute
    return conn.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 49, in call_with_retry
    fail(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1520, in <lambda>
    lambda error: self._disconnect_raise_connect(conn, error),
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1507, in _disconnect_raise_connect
    raise error
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1519, in <lambda>
    lambda: command(*args, **kwargs),
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1536, in try_read
    if not conn.can_read(timeout=timeout):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 861, in can_read
    return self._parser.can_read(timeout)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 444, in can_read
    return self.read_from_socket(timeout=timeout, raise_on_timeout=False)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 455, in read_from_socket
    raise ConnectionError(SERVER_CLOSED_CONNECTION_ERROR)
redis.exceptions.ConnectionError: Connection closed by server.
    return self.main(*args, **kwargs)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1078, in main
    rv = self.invoke(ctx)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 783, in invoke
    return __callback(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/commands/scheduler.py", line 205, in start_worker
    start_worker(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/background_jobs.py", line 318, in start_worker
    worker.work(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 456, in work
    self.teardown()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 1046, in teardown
    self.register_death()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 832, in register_death
    p.execute()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 2114, in execute
    conn = self.connection_pool.get_connection("MULTI", self.shard_hint)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1457, in get_connection
    connection.connect()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 705, in connect
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 111 connecting to 127.0.0.1:11000. Connection refused.
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:58: DeprecationWarning: The `push_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:72: DeprecationWarning: The `pop_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:58: DeprecationWarning: The `push_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:72: DeprecationWarning: The `pop_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:58: DeprecationWarning: The `push_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:72: DeprecationWarning: The `pop_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:58: DeprecationWarning: The `push_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:72: DeprecationWarning: The `pop_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:58: DeprecationWarning: The `push_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:72: DeprecationWarning: The `pop_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:58: DeprecationWarning: The `push_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:72: DeprecationWarning: The `pop_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:58: DeprecationWarning: The `push_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:72: DeprecationWarning: The `pop_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:58: DeprecationWarning: The `push_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:72: DeprecationWarning: The `pop_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:58: DeprecationWarning: The `push_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:72: DeprecationWarning: The `pop_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:58: DeprecationWarning: The `push_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:72: DeprecationWarning: The `pop_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:58: DeprecationWarning: The `push_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:72: DeprecationWarning: The `pop_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:58: DeprecationWarning: The `push_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:72: DeprecationWarning: The `pop_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:58: DeprecationWarning: The `push_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:72: DeprecationWarning: The `pop_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
Exception in thread Thread-1:
Traceback (most recent call last):
  File "/usr/lib/python3.10/threading.py", line 1016, in _bootstrap_inner
    self.run()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1799, in run
    pubsub.get_message(ignore_subscribe_messages=True, timeout=sleep_time)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1690, in get_message
    response = self.parse_response(block=(timeout is None), timeout=timeout)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1542, in parse_response
    response = self._execute(conn, try_read)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1518, in _execute
    return conn.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 49, in call_with_retry
    fail(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1520, in <lambda>
    lambda error: self._disconnect_raise_connect(conn, error),
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1507, in _disconnect_raise_connect
    raise error
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1519, in <lambda>
    lambda: command(*args, **kwargs),
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1536, in try_read
    if not conn.can_read(timeout=timeout):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 861, in can_read
    return self._parser.can_read(timeout)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 444, in can_read
    return self.read_from_socket(timeout=timeout, raise_on_timeout=False)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 455, in read_from_socket
Traceback (most recent call last):
      File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 699, in connect
raise ConnectionError(SERVER_CLOSED_CONNECTION_ERROR)
redis.exceptions.    ConnectionErrorsock = self.retry.call_with_retry(: 
Connection closed by server.  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 700, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1002, in _connect

    raise err
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 990, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib/python3.10/runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/lib/python3.10/runpy.py", line 86, in _run_code
    exec(code, run_globals)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 114, in <module>
    main()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 20, in main
    click.Group(commands=commands)(prog_name="bench")
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1078, in main
    rv = self.invoke(ctx)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 783, in invoke
    return __callback(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/commands/scheduler.py", line 205, in start_worker
    start_worker(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/background_jobs.py", line 318, in start_worker
    worker.work(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 456, in work
    self.teardown()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 1046, in teardown
    self.register_death()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 832, in register_death
    p.execute()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 2114, in execute
    conn = self.connection_pool.get_connection("MULTI", self.shard_hint)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1457, in get_connection
    connection.connect()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 705, in connect
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 111 connecting to 127.0.0.1:11000. Connection refused.
Exception in thread Thread-1:
Traceback (most recent call last):
  File "/usr/lib/python3.10/threading.py", line 1016, in _bootstrap_inner
    self.run()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1799, in run
    pubsub.get_message(ignore_subscribe_messages=True, timeout=sleep_time)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1690, in get_message
    response = self.parse_response(block=(timeout is None), timeout=timeout)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1542, in parse_response
    response = self._execute(conn, try_read)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1518, in _execute
    return conn.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 49, in call_with_retry
    fail(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1520, in <lambda>
    lambda error: self._disconnect_raise_connect(conn, error),
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1507, in _disconnect_raise_connect
    raise error
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1519, in <lambda>
    lambda: command(*args, **kwargs),
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1536, in try_read
    if not conn.can_read(timeout=timeout):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 861, in can_read
    return self._parser.can_read(timeout)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 444, in can_read
    return self.read_from_socket(timeout=timeout, raise_on_timeout=False)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 455, in read_from_socket
    raise ConnectionError(SERVER_CLOSED_CONNECTION_ERROR)
redis.exceptions.ConnectionError: Connection closed by server.
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 699, in connect
    sock = self.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 700, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1002, in _connect
    raise err
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 990, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib/python3.10/runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/lib/python3.10/runpy.py", line 86, in _run_code
    exec(code, run_globals)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 114, in <module>
    main()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 20, in main
    click.Group(commands=commands)(prog_name="bench")
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1078, in main
    rv = self.invoke(ctx)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 783, in invoke
    return __callback(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/commands/scheduler.py", line 205, in start_worker
    start_worker(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/background_jobs.py", line 318, in start_worker
    worker.work(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 456, in work
    self.teardown()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 1046, in teardown
    self.register_death()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 832, in register_death
    p.execute()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 2114, in execute
    conn = self.connection_pool.get_connection("MULTI", self.shard_hint)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1457, in get_connection
    connection.connect()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 705, in connect
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 111 connecting to 127.0.0.1:11000. Connection refused.
Exception in thread Thread-1:
Traceback (most recent call last):
  File "/usr/lib/python3.10/threading.py", line 1016, in _bootstrap_inner
    self.run()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1799, in run
    pubsub.get_message(ignore_subscribe_messages=True, timeout=sleep_time)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1690, in get_message
    response = self.parse_response(block=(timeout is None), timeout=timeout)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1542, in parse_response
    response = self._execute(conn, try_read)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1518, in _execute
    return conn.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 49, in call_with_retry
    fail(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1520, in <lambda>
    lambda error: self._disconnect_raise_connect(conn, error),
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1507, in _disconnect_raise_connect
    raise error
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1519, in <lambda>
    lambda: command(*args, **kwargs),
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1536, in try_read
    if not conn.can_read(timeout=timeout):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 861, in can_read
    return self._parser.can_read(timeout)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 444, in can_read
    return self.read_from_socket(timeout=timeout, raise_on_timeout=False)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 455, in read_from_socket
    raise ConnectionError(SERVER_CLOSED_CONNECTION_ERROR)
redis.exceptions.ConnectionError: Connection closed by server.
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 699, in connect
    sock = self.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 700, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1002, in _connect
    raise err
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 990, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib/python3.10/runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/lib/python3.10/runpy.py", line 86, in _run_code
    exec(code, run_globals)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 114, in <module>
    main()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 20, in main
    click.Group(commands=commands)(prog_name="bench")
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1078, in main
    rv = self.invoke(ctx)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 783, in invoke
    return __callback(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/commands/scheduler.py", line 205, in start_worker
    start_worker(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/background_jobs.py", line 318, in start_worker
    worker.work(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 456, in work
    self.teardown()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 1046, in teardown
    self.register_death()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 832, in register_death
    p.execute()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 2114, in execute
    conn = self.connection_pool.get_connection("MULTI", self.shard_hint)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1457, in get_connection
    connection.connect()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 705, in connect
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 111 connecting to 127.0.0.1:11000. Connection refused.
Exception in thread Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 699, in connect
Thread-1:
Traceback (most recent call last):
  File "/usr/lib/python3.10/threading.py", line 1016, in _bootstrap_inner
    self.run()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1799, in run
    sock = self.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 700, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1002, in _connect
    raise err
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 990, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib/python3.10/runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/lib/python3.10/runpy.py", line 86, in _run_code
    exec(code, run_globals)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 114, in <module>
    main()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 20, in main
    click.Group(commands=commands)(prog_name="bench")
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1157, in __call__
    pubsub.get_message(ignore_subscribe_messages=True, timeout=sleep_time)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1690, in get_message
    response = self.parse_response(block=(timeout is None), timeout=timeout)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1542, in parse_response
    response = self._execute(conn, try_read)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1518, in _execute
    return conn.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 49, in call_with_retry
        fail(error)return self.main(*args, **kwargs)

  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1520, in <lambda>
      File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1078, in main
lambda error: self._disconnect_raise_connect(conn, error),    rv = self.invoke(ctx)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 783, in invoke

      File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1507, in _disconnect_raise_connect
    return __callback(*args, **kwargs)raise error

  File "/home/<USER>/frappe-bench/apps/frappe/frappe/commands/scheduler.py", line 205, in start_worker
    start_worker(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/background_jobs.py", line 318, in start_worker
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
        worker.work(return do()

  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 456, in work
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1519, in <lambda>
    lambda: command(*args, **kwargs),
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1536, in try_read
    if not conn.can_read(timeout=timeout):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 861, in can_read
    return self._parser.can_read(timeout)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 444, in can_read
    return self.read_from_socket(timeout=timeout, raise_on_timeout=False)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 455, in read_from_socket
    raise ConnectionError(SERVER_CLOSED_CONNECTION_ERROR)
redis.exceptions.ConnectionError: Connection closed by server.
    self.teardown()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 1046, in teardown
    self.register_death()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 832, in register_death
    p.execute()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 2114, in execute
    conn = self.connection_pool.get_connection("MULTI", self.shard_hint)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1457, in get_connection
    connection.connect()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 705, in connect
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 111 connecting to 127.0.0.1:11000. Connection refused.
Exception in thread Thread-1:
Traceback (most recent call last):
  File "/usr/lib/python3.10/threading.py", line 1016, in _bootstrap_inner
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 699, in connect
    sock = self.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 700, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1002, in _connect
    raise err
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 990, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib/python3.10/runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/lib/python3.10/runpy.py", line 86, in _run_code
    exec(code, run_globals)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 114, in <module>
    main()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 20, in main
    click.Group(commands=commands)(prog_name="bench")
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1078, in main
    rv = self.invoke(ctx)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
        return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 783, in invoke
    return __callback(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/commands/scheduler.py", line 205, in start_worker
self.run()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1799, in run
    pubsub.get_message(ignore_subscribe_messages=True, timeout=sleep_time)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1690, in get_message
    response = self.parse_response(block=(timeout is None), timeout=timeout)    start_worker(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/background_jobs.py", line 318, in start_worker

  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1542, in parse_response
    response = self._execute(conn, try_read)    worker.work(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 456, in work
    self.teardown()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 1046, in teardown

  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1518, in _execute
        self.register_death()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 832, in register_death
    p.execute()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 2114, in execute
return conn.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 49, in call_with_retry
    fail(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1520, in <lambda>
    lambda error: self._disconnect_raise_connect(conn, error),
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1507, in _disconnect_raise_connect
    raise error
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1519, in <lambda>
    lambda: command(*args, **kwargs),
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1536, in try_read
    if not conn.can_read(timeout=timeout):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 861, in can_read
    return self._parser.can_read(timeout)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 444, in can_read
    return self.read_from_socket(timeout=timeout, raise_on_timeout=False)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 455, in read_from_socket
    raise ConnectionError(SERVER_CLOSED_CONNECTION_ERROR)
redis.exceptions.ConnectionError: Connection closed by server.
    conn = self.connection_pool.get_connection("MULTI", self.shard_hint)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1457, in get_connection
    connection.connect()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 705, in connect
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 111 connecting to 127.0.0.1:11000. Connection refused.
2025-05-25 14:52:10,937 Could not connect to Redis instance: Connection closed by server. Retrying in 1 seconds...
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 699, in connect
    sock = self.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 700, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1002, in _connect
    raise err
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 990, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib/python3.10/runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/lib/python3.10/runpy.py", line 86, in _run_code
    exec(code, run_globals)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 114, in <module>
    main()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 20, in main
    click.Group(commands=commands)(prog_name="bench")
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1157, in __call__
Exception in thread     return self.main(*args, **kwargs)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1078, in main
    rv = self.invoke(ctx)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
Thread-1    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
:
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1434, in invoke
Traceback (most recent call last):
    return ctx.invoke(self.callback, **ctx.params)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 783, in invoke
    return __callback(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/commands/scheduler.py", line 205, in start_worker
    start_worker(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/background_jobs.py", line 318, in start_worker
  File "/usr/lib/python3.10/threading.py", line 1016, in _bootstrap_inner
    worker.work(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 456, in work
    self.teardown()    self.run()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1799, in run

  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 1046, in teardown
    self.register_death()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 832, in register_death
    p.execute()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 2114, in execute
    conn = self.connection_pool.get_connection("MULTI", self.shard_hint)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1457, in get_connection
    connection.connect()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 705, in connect
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 111 connecting to 127.0.0.1:11000. Connection refused.
    Exception in thread Thread-1:
Traceback (most recent call last):
  File "/usr/lib/python3.10/threading.py", line 1016, in _bootstrap_inner
    self.run()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1799, in run
    pubsub.get_message(ignore_subscribe_messages=True, timeout=sleep_time)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1690, in get_message
    response = self.parse_response(block=(timeout is None), timeout=timeout)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1542, in parse_response
    response = self._execute(conn, try_read)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1518, in _execute
    return conn.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 49, in call_with_retry
    fail(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1520, in <lambda>
    lambda error: self._disconnect_raise_connect(conn, error),
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1507, in _disconnect_raise_connect
    raise error
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1519, in <lambda>
    lambda: command(*args, **kwargs),
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1536, in try_read
    if not conn.can_read(timeout=timeout):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 861, in can_read
    return self._parser.can_read(timeout)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 444, in can_read
    return self.read_from_socket(timeout=timeout, raise_on_timeout=False)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 455, in read_from_socket
    raise ConnectionError(SERVER_CLOSED_CONNECTION_ERROR)
redis.exceptions.ConnectionError: Connection closed by server.
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 699, in connect
    sock = self.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 700, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1002, in _connect
    raise err
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 990, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib/python3.10/runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/lib/python3.10/runpy.py", line 86, in _run_code
    exec(code, run_globals)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 114, in <module>
    main()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 20, in main
    click.Group(commands=commands)(prog_name="bench")
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1078, in main
    rv = self.invoke(ctx)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 783, in invoke
    return __callback(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/commands/scheduler.py", line 205, in start_worker
    start_worker(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/background_jobs.py", line 318, in start_worker
    worker.work(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 456, in work
    self.teardown()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 1046, in teardown
    self.register_death()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 832, in register_death
    p.execute()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 2114, in execute
    conn = self.connection_pool.get_connection("MULTI", self.shard_hint)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1457, in get_connection
    connection.connect()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 705, in connect
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 111 connecting to 127.0.0.1:11000. Connection refused.
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:58: DeprecationWarning: The `push_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:72: DeprecationWarning: The `pop_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:58: DeprecationWarning: The `push_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:72: DeprecationWarning: The `pop_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
Exception in thread Thread-1:
Traceback (most recent call last):
  File "/usr/lib/python3.10/threading.py", line 1016, in _bootstrap_inner
    self.run()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1799, in run
    pubsub.get_message(ignore_subscribe_messages=True, timeout=sleep_time)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1690, in get_message
    response = self.parse_response(block=(timeout is None), timeout=timeout)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1542, in parse_response
Traceback (most recent call last):
    response = self._execute(conn, try_read)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1518, in _execute
    return conn.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 49, in call_with_retry
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 699, in connect
    fail(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1520, in <lambda>
    lambda error: self._disconnect_raise_connect(conn, error),
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1507, in _disconnect_raise_connect
    raise error
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1519, in <lambda>
    lambda: command(*args, **kwargs),
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1536, in try_read
    if not conn.can_read(timeout=timeout):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 861, in can_read
    sock = self.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 700, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1002, in _connect
    raise err
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 990, in _connect
    sock.connect(socket_address)
    return self._parser.can_read(timeout)ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib/python3.10/runpy.py", line 196, in _run_module_as_main

    return _run_code(code, main_globals, None,
  File "/usr/lib/python3.10/runpy.py", line 86, in _run_code
    exec(code, run_globals)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 114, in <module>
    main()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 20, in main
    click.Group(commands=commands)(prog_name="bench")  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 444, in can_read
    return self.read_from_socket(timeout=timeout, raise_on_timeout=False)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 455, in read_from_socket
    raise ConnectionError(SERVER_CLOSED_CONNECTION_ERROR)

redis.exceptions.ConnectionError:   File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1157, in __call__
Connection closed by server.
    return self.main(*args, **kwargs)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1078, in main
    rv = self.invoke(ctx)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 783, in invoke
    return __callback(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/commands/scheduler.py", line 205, in start_worker
    start_worker(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/background_jobs.py", line 318, in start_worker
    worker.work(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 456, in work
    self.teardown()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 1046, in teardown
    self.register_death()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 832, in register_death
    p.execute()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 2114, in execute
    conn = self.connection_pool.get_connection("MULTI", self.shard_hint)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1457, in get_connection
    connection.connect()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 705, in connect
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 111 connecting to 127.0.0.1:11000. Connection refused.
Exception in thread Thread-1:
Traceback (most recent call last):
  File "/usr/lib/python3.10/threading.py", line 1016, in _bootstrap_inner
    self.run()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1799, in run
    pubsub.get_message(ignore_subscribe_messages=True, timeout=sleep_time)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1690, in get_message
    response = self.parse_response(block=(timeout is None), timeout=timeout)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1542, in parse_response
    response = self._execute(conn, try_read)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1518, in _execute
    return conn.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 49, in call_with_retry
    fail(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1520, in <lambda>
    lambda error: self._disconnect_raise_connect(conn, error),
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1507, in _disconnect_raise_connect
    raise error
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1519, in <lambda>
    lambda: command(*args, **kwargs),
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1536, in try_read
    if not conn.can_read(timeout=timeout):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 861, in can_read
    return self._parser.can_read(timeout)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 444, in can_read
    return self.read_from_socket(timeout=timeout, raise_on_timeout=False)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 455, in read_from_socket
    raise ConnectionError(SERVER_CLOSED_CONNECTION_ERROR)
redis.exceptions.ConnectionError: Connection closed by server.
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 699, in connect
    sock = self.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 700, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1002, in _connect
    raise err
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 990, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib/python3.10/runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/lib/python3.10/runpy.py", line 86, in _run_code
    exec(code, run_globals)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 114, in <module>
    main()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 20, in main
    click.Group(commands=commands)(prog_name="bench")
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1078, in main
    rv = self.invoke(ctx)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 783, in invoke
    return __callback(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/commands/scheduler.py", line 205, in start_worker
    start_worker(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/background_jobs.py", line 318, in start_worker
    worker.work(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 456, in work
    self.teardown()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 1046, in teardown
    self.register_death()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 832, in register_death
    p.execute()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 2114, in execute
    conn = self.connection_pool.get_connection("MULTI", self.shard_hint)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1457, in get_connection
    connection.connect()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 705, in connect
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 111 connecting to 127.0.0.1:11000. Connection refused.
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:58: DeprecationWarning: The `push_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:72: DeprecationWarning: The `pop_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
Exception in thread Thread-1:
Traceback (most recent call last):
  File "/usr/lib/python3.10/threading.py", line 1016, in _bootstrap_inner
    self.run()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1799, in run
    pubsub.get_message(ignore_subscribe_messages=True, timeout=sleep_time)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1690, in get_message
    response = self.parse_response(block=(timeout is None), timeout=timeout)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1542, in parse_response
    response = self._execute(conn, try_read)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1518, in _execute
    return conn.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 49, in call_with_retry
    fail(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1520, in <lambda>
    lambda error: self._disconnect_raise_connect(conn, error),
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1507, in _disconnect_raise_connect
    raise error
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1519, in <lambda>
    lambda: command(*args, **kwargs),
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1536, in try_read
    if not conn.can_read(timeout=timeout):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 861, in can_read
    return self._parser.can_read(timeout)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 444, in can_read
    return self.read_from_socket(timeout=timeout, raise_on_timeout=False)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 455, in read_from_socket
    raise ConnectionError(SERVER_CLOSED_CONNECTION_ERROR)
redis.exceptions.ConnectionError: Connection closed by server.
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 699, in connect
    sock = self.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 700, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1002, in _connect
    raise err
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 990, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib/python3.10/runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/lib/python3.10/runpy.py", line 86, in _run_code
    exec(code, run_globals)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 114, in <module>
    main()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 20, in main
    click.Group(commands=commands)(prog_name="bench")
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1078, in main
    rv = self.invoke(ctx)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 783, in invoke
    return __callback(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/commands/scheduler.py", line 205, in start_worker
    start_worker(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/background_jobs.py", line 318, in start_worker
    worker.work(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 456, in work
    self.teardown()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 1046, in teardown
    self.register_death()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 832, in register_death
    p.execute()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 2114, in execute
    conn = self.connection_pool.get_connection("MULTI", self.shard_hint)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1457, in get_connection
    connection.connect()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 705, in connect
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 111 connecting to 127.0.0.1:11000. Connection refused.
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:58: DeprecationWarning: The `push_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:72: DeprecationWarning: The `pop_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
Exception in thread Thread-1:
Traceback (most recent call last):
  File "/usr/lib/python3.10/threading.py", line 1016, in _bootstrap_inner
    self.run()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1799, in run
    pubsub.get_message(ignore_subscribe_messages=True, timeout=sleep_time)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1690, in get_message
    response = self.parse_response(block=(timeout is None), timeout=timeout)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1542, in parse_response
    response = self._execute(conn, try_read)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1518, in _execute
    return conn.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 49, in call_with_retry
    fail(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1520, in <lambda>
    lambda error: self._disconnect_raise_connect(conn, error),
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1507, in _disconnect_raise_connect
    raise error
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1519, in <lambda>
    lambda: command(*args, **kwargs),
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1536, in try_read
    if not conn.can_read(timeout=timeout):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 861, in can_read
    return self._parser.can_read(timeout)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 444, in can_read
    return self.read_from_socket(timeout=timeout, raise_on_timeout=False)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 455, in read_from_socket
    raise ConnectionError(SERVER_CLOSED_CONNECTION_ERROR)
redis.exceptions.ConnectionError: Connection closed by server.
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 699, in connect
    sock = self.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 700, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1002, in _connect
    raise err
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 990, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib/python3.10/runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/lib/python3.10/runpy.py", line 86, in _run_code
    exec(code, run_globals)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 114, in <module>
    main()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 20, in main
    click.Group(commands=commands)(prog_name="bench")
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1078, in main
    rv = self.invoke(ctx)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 783, in invoke
    return __callback(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/commands/scheduler.py", line 205, in start_worker
    start_worker(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/background_jobs.py", line 318, in start_worker
    worker.work(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 456, in work
    self.teardown()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 1046, in teardown
    self.register_death()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 832, in register_death
    p.execute()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 2114, in execute
    conn = self.connection_pool.get_connection("MULTI", self.shard_hint)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1457, in get_connection
    connection.connect()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 705, in connect
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 111 connecting to 127.0.0.1:11000. Connection refused.
Exception in thread Thread-1:
Traceback (most recent call last):
  File "/usr/lib/python3.10/threading.py", line 1016, in _bootstrap_inner
    self.run()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1799, in run
    pubsub.get_message(ignore_subscribe_messages=True, timeout=sleep_time)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1690, in get_message
    response = self.parse_response(block=(timeout is None), timeout=timeout)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1542, in parse_response
    response = self._execute(conn, try_read)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1518, in _execute
    return conn.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 49, in call_with_retry
    fail(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1520, in <lambda>
    lambda error: self._disconnect_raise_connect(conn, error),
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1507, in _disconnect_raise_connect
    raise error
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1519, in <lambda>
    lambda: command(*args, **kwargs),
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1536, in try_read
    if not conn.can_read(timeout=timeout):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 861, in can_read
    return self._parser.can_read(timeout)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 444, in can_read
    return self.read_from_socket(timeout=timeout, raise_on_timeout=False)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 455, in read_from_socket
    raise ConnectionError(SERVER_CLOSED_CONNECTION_ERROR)
redis.exceptions.ConnectionError: Connection closed by server.
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 699, in connect
    sock = self.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 700, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1002, in _connect
    raise err
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 990, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib/python3.10/runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/lib/python3.10/runpy.py", line 86, in _run_code
    exec(code, run_globals)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 114, in <module>
    main()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 20, in main
    click.Group(commands=commands)(prog_name="bench")
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1078, in main
    rv = self.invoke(ctx)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 783, in invoke
    return __callback(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/commands/scheduler.py", line 205, in start_worker
    start_worker(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/background_jobs.py", line 318, in start_worker
    worker.work(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 456, in work
    self.teardown()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 1047, in teardown
    self.unsubscribe()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 624, in unsubscribe
    self.pubsub.unsubscribe()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1659, in unsubscribe
    return self.execute_command("UNSUBSCRIBE", *args)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1479, in execute_command
    self._execute(connection, connection.send_command, *args, **kwargs)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1518, in _execute
    return conn.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 49, in call_with_retry
    fail(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1520, in <lambda>
    lambda error: self._disconnect_raise_connect(conn, error),
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1507, in _disconnect_raise_connect
    raise error
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1519, in <lambda>
    lambda: command(*args, **kwargs),
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 847, in send_command
    self.send_packed_command(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 817, in send_packed_command
    self.connect()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 705, in connect
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 111 connecting to 127.0.0.1:11000. Connection refused.
Exception in thread Thread-1:
Traceback (most recent call last):
  File "/usr/lib/python3.10/threading.py", line 1016, in _bootstrap_inner
    self.run()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1799, in run
    pubsub.get_message(ignore_subscribe_messages=True, timeout=sleep_time)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1690, in get_message
    response = self.parse_response(block=(timeout is None), timeout=timeout)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1542, in parse_response
    response = self._execute(conn, try_read)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1518, in _execute
    return conn.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 49, in call_with_retry
    fail(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1520, in <lambda>
    lambda error: self._disconnect_raise_connect(conn, error),
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1507, in _disconnect_raise_connect
    raise error
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1519, in <lambda>
    lambda: command(*args, **kwargs),
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1536, in try_read
    if not conn.can_read(timeout=timeout):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 861, in can_read
    return self._parser.can_read(timeout)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 444, in can_read
    return self.read_from_socket(timeout=timeout, raise_on_timeout=False)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 455, in read_from_socket
    raise ConnectionError(SERVER_CLOSED_CONNECTION_ERROR)
redis.exceptions.ConnectionError: Connection closed by server.
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 699, in connect
    sock = self.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 700, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1002, in _connect
    raise err
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 990, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib/python3.10/runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/lib/python3.10/runpy.py", line 86, in _run_code
    exec(code, run_globals)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 114, in <module>
    main()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 20, in main
    click.Group(commands=commands)(prog_name="bench")
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1078, in main
    rv = self.invoke(ctx)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 783, in invoke
    return __callback(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/commands/scheduler.py", line 205, in start_worker
    start_worker(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/background_jobs.py", line 318, in start_worker
    worker.work(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 456, in work
    self.teardown()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 1046, in teardown
    self.register_death()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 832, in register_death
    p.execute()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 2114, in execute
    conn = self.connection_pool.get_connection("MULTI", self.shard_hint)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1457, in get_connection
    connection.connect()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 705, in connect
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 111 connecting to 127.0.0.1:11000. Connection refused.
2025-05-28 14:57:22,084 Could not connect to Redis instance: Connection closed by server. Retrying in 1 seconds...
Exception in thread Thread-1:
Traceback (most recent call last):
  File "/usr/lib/python3.10/threading.py", line 1016, in _bootstrap_inner
    self.run()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1799, in run
    pubsub.get_message(ignore_subscribe_messages=True, timeout=sleep_time)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1690, in get_message
    response = self.parse_response(block=(timeout is None), timeout=timeout)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1542, in parse_response
    response = self._execute(conn, try_read)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1518, in _execute
    return conn.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 49, in call_with_retry
    fail(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1520, in <lambda>
    lambda error: self._disconnect_raise_connect(conn, error),
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1507, in _disconnect_raise_connect
    raise error
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1519, in <lambda>
    lambda: command(*args, **kwargs),
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1536, in try_read
    if not conn.can_read(timeout=timeout):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 861, in can_read
    return self._parser.can_read(timeout)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 444, in can_read
    return self.read_from_socket(timeout=timeout, raise_on_timeout=False)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 455, in read_from_socket
    raise ConnectionError(SERVER_CLOSED_CONNECTION_ERROR)
redis.exceptions.ConnectionError: Connection closed by server.
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 699, in connect
    sock = self.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 700, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1002, in _connect
    raise err
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 990, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib/python3.10/runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/lib/python3.10/runpy.py", line 86, in _run_code
    exec(code, run_globals)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 114, in <module>
    main()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 20, in main
    click.Group(commands=commands)(prog_name="bench")
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1078, in main
    rv = self.invoke(ctx)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 783, in invoke
    return __callback(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/commands/scheduler.py", line 205, in start_worker
    start_worker(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/background_jobs.py", line 318, in start_worker
    worker.work(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 456, in work
    self.teardown()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 1046, in teardown
    self.register_death()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 832, in register_death
    p.execute()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 2114, in execute
    conn = self.connection_pool.get_connection("MULTI", self.shard_hint)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1457, in get_connection
    connection.connect()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 705, in connect
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 111 connecting to 127.0.0.1:11000. Connection refused.
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:58: DeprecationWarning: The `push_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:72: DeprecationWarning: The `pop_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
Exception in thread Thread-1:
Traceback (most recent call last):
  File "/usr/lib/python3.10/threading.py", line 1016, in _bootstrap_inner
    self.run()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1799, in run
Traceback (most recent call last):
    pubsub.get_message(ignore_subscribe_messages=True, timeout=sleep_time)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1690, in get_message
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 699, in connect
    response = self.parse_response(block=(timeout is None), timeout=timeout)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1542, in parse_response
    response = self._execute(conn, try_read)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1518, in _execute
    return conn.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 49, in call_with_retry
    fail(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1520, in <lambda>
    lambda error: self._disconnect_raise_connect(conn, error),
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1507, in _disconnect_raise_connect
    raise error
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1519, in <lambda>
        sock = self.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 700, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1002, in _connect
    raise err
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 990, in _connect
lambda: command(*args, **kwargs),    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib/python3.10/runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/lib/python3.10/runpy.py", line 86, in _run_code
    exec(code, run_globals)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 114, in <module>
    main()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 20, in main
    click.Group(commands=commands)(prog_name="bench")

  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1536, in try_read
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1157, in __call__
    if not conn.can_read(timeout=timeout):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 861, in can_read
    return self._parser.can_read(timeout)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 444, in can_read
    return self.read_from_socket(timeout=timeout, raise_on_timeout=False)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 455, in read_from_socket
    raise ConnectionError(SERVER_CLOSED_CONNECTION_ERROR)
redis.exceptions.ConnectionError: Connection closed by server.
    return self.main(*args, **kwargs)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1078, in main
    rv = self.invoke(ctx)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 783, in invoke
    return __callback(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/commands/scheduler.py", line 205, in start_worker
    start_worker(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/background_jobs.py", line 318, in start_worker
    worker.work(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 456, in work
    self.teardown()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 1046, in teardown
    self.register_death()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 832, in register_death
    p.execute()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 2114, in execute
    conn = self.connection_pool.get_connection("MULTI", self.shard_hint)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1457, in get_connection
    connection.connect()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 705, in connect
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 111 connecting to 127.0.0.1:11000. Connection refused.
Exception in thread Thread-1:
Traceback (most recent call last):
  File "/usr/lib/python3.10/threading.py", line 1016, in _bootstrap_inner
    self.run()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1799, in run
    pubsub.get_message(ignore_subscribe_messages=True, timeout=sleep_time)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1690, in get_message
    response = self.parse_response(block=(timeout is None), timeout=timeout)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1542, in parse_response
    response = self._execute(conn, try_read)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1518, in _execute
    return conn.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 49, in call_with_retry
    fail(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1520, in <lambda>
    lambda error: self._disconnect_raise_connect(conn, error),
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1507, in _disconnect_raise_connect
    raise error
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1519, in <lambda>
    lambda: command(*args, **kwargs),
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1536, in try_read
    if not conn.can_read(timeout=timeout):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 861, in can_read
    return self._parser.can_read(timeout)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 444, in can_read
    return self.read_from_socket(timeout=timeout, raise_on_timeout=False)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 455, in read_from_socket
    raise ConnectionError(SERVER_CLOSED_CONNECTION_ERROR)
redis.exceptions.ConnectionError: Connection closed by server.
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 699, in connect
    sock = self.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 700, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1002, in _connect
    raise err
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 990, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib/python3.10/runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/lib/python3.10/runpy.py", line 86, in _run_code
    exec(code, run_globals)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 114, in <module>
    main()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 20, in main
    click.Group(commands=commands)(prog_name="bench")
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1078, in main
    rv = self.invoke(ctx)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 783, in invoke
    return __callback(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/commands/scheduler.py", line 205, in start_worker
    start_worker(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/background_jobs.py", line 318, in start_worker
    worker.work(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 456, in work
    self.teardown()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 1046, in teardown
    self.register_death()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 832, in register_death
    p.execute()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 2114, in execute
    conn = self.connection_pool.get_connection("MULTI", self.shard_hint)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1457, in get_connection
    connection.connect()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 705, in connect
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 111 connecting to 127.0.0.1:11000. Connection refused.
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 699, in connect
    sock = self.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 700, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1002, in _connect
    raise err
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 990, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib/python3.10/runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/lib/python3.10/runpy.py", line 86, in _run_code
    exec(code, run_globals)Exception in thread Thread-1:
Traceback (most recent call last):
  File "/usr/lib/python3.10/threading.py", line 1016, in _bootstrap_inner
    self.run()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1799, in run

  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 114, in <module>
    main()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 20, in main
    click.Group(commands=commands)(prog_name="bench")
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1157, in __call__
    pubsub.get_message(ignore_subscribe_messages=True, timeout=sleep_time)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1690, in get_message
    response = self.parse_response(block=(timeout is None), timeout=timeout)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1542, in parse_response
    response = self._execute(conn, try_read)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1518, in _execute
    return conn.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 49, in call_with_retry
    fail(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1520, in <lambda>
    lambda error: self._disconnect_raise_connect(conn, error),
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1507, in _disconnect_raise_connect
    return self.main(*args, **kwargs)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1078, in main
    rv = self.invoke(ctx)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 783, in invoke
    return __callback(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/commands/scheduler.py", line 205, in start_worker
    start_worker(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/background_jobs.py", line 318, in start_worker
    raise error
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1519, in <lambda>
    lambda: command(*args, **kwargs),
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1536, in try_read
    if not conn.can_read(timeout=timeout):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 861, in can_read
    return self._parser.can_read(timeout)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 444, in can_read
    return self.read_from_socket(timeout=timeout, raise_on_timeout=False)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 455, in read_from_socket
    raise ConnectionError(SERVER_CLOSED_CONNECTION_ERROR)
redis.exceptions.ConnectionError: Connection closed by server.
    worker.work(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 456, in work
    self.teardown()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 1046, in teardown
    self.register_death()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 832, in register_death
    p.execute()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 2114, in execute
    conn = self.connection_pool.get_connection("MULTI", self.shard_hint)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1457, in get_connection
    connection.connect()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 705, in connect
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 111 connecting to 127.0.0.1:11000. Connection refused.
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 699, in connect
    sock = self.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 700, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1002, in _connect
    raise err
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 990, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib/python3.10/runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/lib/python3.10/runpy.py", line 86, in _run_code
    exec(code, run_globals)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 114, in <module>
    main()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 20, in main
    click.Group(commands=commands)(prog_name="bench")
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1078, in main
    rv = self.invoke(ctx)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 783, in invoke
    return __callback(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/commands/scheduler.py", line 205, in start_worker
    start_worker(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/background_jobs.py", line 318, in start_worker
Exception in thread Thread-1:
Traceback (most recent call last):
  File "/usr/lib/python3.10/threading.py", line 1016, in _bootstrap_inner
    worker.work(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 456, in work
    self.run()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1799, in run
    self.teardown()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 1046, in teardown
    self.register_death()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 832, in register_death
    p.execute()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 2114, in execute
    conn = self.connection_pool.get_connection("MULTI", self.shard_hint)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1457, in get_connection
    connection.connect()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 705, in connect
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 111 connecting to 127.0.0.1:11000. Connection refused.
Exception in thread Thread-1:
Traceback (most recent call last):
  File "/usr/lib/python3.10/threading.py", line 1016, in _bootstrap_inner
    self.run()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1799, in run
Traceback (most recent call last):
    pubsub.get_message(ignore_subscribe_messages=True, timeout=sleep_time)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1690, in get_message
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 699, in connect
    response = self.parse_response(block=(timeout is None), timeout=timeout)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1542, in parse_response
    response = self._execute(conn, try_read)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1518, in _execute
    return conn.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 49, in call_with_retry
    fail(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1520, in <lambda>
    lambda error: self._disconnect_raise_connect(conn, error),
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1507, in _disconnect_raise_connect
        raise error
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1519, in <lambda>
    lambda: command(*args, **kwargs),
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1536, in try_read
    if not conn.can_read(timeout=timeout):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 861, in can_read
    return self._parser.can_read(timeout)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 444, in can_read
    return self.read_from_socket(timeout=timeout, raise_on_timeout=False)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 455, in read_from_socket
    sock = self.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
raise ConnectionError(SERVER_CLOSED_CONNECTION_ERROR)    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 700, in <lambda>
    
lambda: self._connect(), lambda error: self.disconnect(error)
redis.exceptions  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1002, in _connect
.ConnectionError: Connection closed by server.
    raise err
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 990, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib/python3.10/runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/lib/python3.10/runpy.py", line 86, in _run_code
    exec(code, run_globals)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 114, in <module>
    main()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 20, in main
    click.Group(commands=commands)(prog_name="bench")
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1078, in main
    rv = self.invoke(ctx)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 783, in invoke
    return __callback(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/commands/scheduler.py", line 205, in start_worker
    start_worker(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/background_jobs.py", line 318, in start_worker
    worker.work(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 456, in work
    self.teardown()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 1046, in teardown
    self.register_death()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 832, in register_death
    p.execute()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 2114, in execute
    conn = self.connection_pool.get_connection("MULTI", self.shard_hint)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1457, in get_connection
    connection.connect()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 705, in connect
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 111 connecting to 127.0.0.1:11000. Connection refused.
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:58: DeprecationWarning: The `push_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/googleapiclient/model.py:30: DeprecationWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html
  import pkg_resources
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pkg_resources/__init__.py:2846: DeprecationWarning: Deprecated call to `pkg_resources.declare_namespace('google')`.
Implementing implicit namespace packages (as specified in PEP 420) is preferred to `pkg_resources.declare_namespace`. See https://setuptools.pypa.io/en/latest/references/keywords.html#keyword-namespace-packages
  declare_namespace(pkg)
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pkg_resources/__init__.py:2846: DeprecationWarning: Deprecated call to `pkg_resources.declare_namespace('google')`.
Implementing implicit namespace packages (as specified in PEP 420) is preferred to `pkg_resources.declare_namespace`. See https://setuptools.pypa.io/en/latest/references/keywords.html#keyword-namespace-packages
  declare_namespace(pkg)
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pkg_resources/__init__.py:2846: DeprecationWarning: Deprecated call to `pkg_resources.declare_namespace('google')`.
Implementing implicit namespace packages (as specified in PEP 420) is preferred to `pkg_resources.declare_namespace`. See https://setuptools.pypa.io/en/latest/references/keywords.html#keyword-namespace-packages
  declare_namespace(pkg)
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:72: DeprecationWarning: The `pop_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:58: DeprecationWarning: The `push_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:72: DeprecationWarning: The `pop_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:58: DeprecationWarning: The `push_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/googleapiclient/model.py:30: DeprecationWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html
  import pkg_resources
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pkg_resources/__init__.py:2846: DeprecationWarning: Deprecated call to `pkg_resources.declare_namespace('google')`.
Implementing implicit namespace packages (as specified in PEP 420) is preferred to `pkg_resources.declare_namespace`. See https://setuptools.pypa.io/en/latest/references/keywords.html#keyword-namespace-packages
  declare_namespace(pkg)
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pkg_resources/__init__.py:2846: DeprecationWarning: Deprecated call to `pkg_resources.declare_namespace('google')`.
Implementing implicit namespace packages (as specified in PEP 420) is preferred to `pkg_resources.declare_namespace`. See https://setuptools.pypa.io/en/latest/references/keywords.html#keyword-namespace-packages
  declare_namespace(pkg)
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pkg_resources/__init__.py:2846: DeprecationWarning: Deprecated call to `pkg_resources.declare_namespace('google')`.
Implementing implicit namespace packages (as specified in PEP 420) is preferred to `pkg_resources.declare_namespace`. See https://setuptools.pypa.io/en/latest/references/keywords.html#keyword-namespace-packages
  declare_namespace(pkg)
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:72: DeprecationWarning: The `pop_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:58: DeprecationWarning: The `push_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:72: DeprecationWarning: The `pop_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:58: DeprecationWarning: The `push_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/googleapiclient/model.py:30: DeprecationWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html
  import pkg_resources
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pkg_resources/__init__.py:2846: DeprecationWarning: Deprecated call to `pkg_resources.declare_namespace('google')`.
Implementing implicit namespace packages (as specified in PEP 420) is preferred to `pkg_resources.declare_namespace`. See https://setuptools.pypa.io/en/latest/references/keywords.html#keyword-namespace-packages
  declare_namespace(pkg)
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pkg_resources/__init__.py:2846: DeprecationWarning: Deprecated call to `pkg_resources.declare_namespace('google')`.
Implementing implicit namespace packages (as specified in PEP 420) is preferred to `pkg_resources.declare_namespace`. See https://setuptools.pypa.io/en/latest/references/keywords.html#keyword-namespace-packages
  declare_namespace(pkg)
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pkg_resources/__init__.py:2846: DeprecationWarning: Deprecated call to `pkg_resources.declare_namespace('google')`.
Implementing implicit namespace packages (as specified in PEP 420) is preferred to `pkg_resources.declare_namespace`. See https://setuptools.pypa.io/en/latest/references/keywords.html#keyword-namespace-packages
  declare_namespace(pkg)
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:72: DeprecationWarning: The `pop_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:58: DeprecationWarning: The `push_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/connections.py:72: DeprecationWarning: The `pop_connection` function is deprecated. Pass the `connection` explicitly instead.
  warnings.warn(
Exception in thread Thread-1:
Traceback (most recent call last):
  File "/usr/lib/python3.10/threading.py", line 1016, in _bootstrap_inner
    self.run()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1799, in run
    pubsub.get_message(ignore_subscribe_messages=True, timeout=sleep_time)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1690, in get_message
    response = self.parse_response(block=(timeout is None), timeout=timeout)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1542, in parse_response
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 699, in connect
    response = self._execute(conn, try_read)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1518, in _execute
    return conn.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 49, in call_with_retry
    fail(error)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1520, in <lambda>
    lambda error: self._disconnect_raise_connect(conn, error),
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1507, in _disconnect_raise_connect
    raise error
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1519, in <lambda>
    lambda: command(*args, **kwargs),
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 1536, in try_read
    if not conn.can_read(timeout=timeout):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 861, in can_read
    sock = self.retry.call_with_retry(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
    return self._parser.can_read(timeout)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 444, in can_read
    return self.read_from_socket(timeout=timeout, raise_on_timeout=False)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 455, in read_from_socket
    raise ConnectionError(SERVER_CLOSED_CONNECTION_ERROR)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 700, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)redis.exceptions.ConnectionError: Connection closed by server.

  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1002, in _connect
    raise err
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 990, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib/python3.10/runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/lib/python3.10/runpy.py", line 86, in _run_code
    exec(code, run_globals)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 114, in <module>
    main()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 20, in main
    click.Group(commands=commands)(prog_name="bench")
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1078, in main
    rv = self.invoke(ctx)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/click/core.py", line 783, in invoke
    return __callback(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/commands/scheduler.py", line 205, in start_worker
    start_worker(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/background_jobs.py", line 318, in start_worker
    worker.work(
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 456, in work
    self.teardown()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 1046, in teardown
    self.register_death()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/rq/worker.py", line 832, in register_death
    p.execute()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/client.py", line 2114, in execute
    conn = self.connection_pool.get_connection("MULTI", self.shard_hint)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 1457, in get_connection
    connection.connect()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/redis/connection.py", line 705, in connect
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 111 connecting to 127.0.0.1:11000. Connection refused.
