2025-05-20 20:45:28,236 Worker rq:worker:******************************** started with PID 3719, version 1.15.1
2025-05-20 20:45:28,236 Subscribing to channel rq:pubsub:********************************
2025-05-20 20:45:28,249 *** Listening on home-newsmart-frappe-bench:short, home-newsmart-frappe-bench:default, home-newsmart-frappe-bench:long...
2025-05-20 20:45:28,252 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-20 20:45:28,257 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-20 20:45:28,274 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-20 21:12:30,060 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-20 21:12:30,862 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-20 21:12:30,867 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-20 21:12:56,952 home-newsmart-frappe-bench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.search.website_search.build_index_for_all_routes', kwargs={}, method=<function build_index_for_all_routes at 0x7f3d74080d30>, site='site1.local', user='Administrator') (site1.local::96d82449-43b6-4d83-888e-f73a600f6572)

Retrieving Routes                   : [=                                       ] 2%
Retrieving Routes                   : [==                                      ] 5%
Retrieving Routes                   : [===                                     ] 8%
Retrieving Routes                   : [====                                    ] 10%
Retrieving Routes                   : [=====                                   ] 13%
Retrieving Routes                   : [======                                  ] 16%
Retrieving Routes                   : [=======                                 ] 18%
Retrieving Routes                   : [========                                ] 21%
Retrieving Routes                   : [=========                               ] 24%
Retrieving Routes                   : [==========                              ] 27%
Retrieving Routes                   : [===========                             ] 29%
Retrieving Routes                   : [============                            ] 32%
Retrieving Routes                   : [==============                          ] 35%
Retrieving Routes                   : [===============                         ] 37%
Retrieving Routes                   : [================                        ] 40%
Retrieving Routes                   : [=================                       ] 43%
Retrieving Routes                   : [==================                      ] 45%
Retrieving Routes                   : [===================                     ] 48%
Retrieving Routes                   : [====================                    ] 51%
Retrieving Routes                   : [=====================                   ] 54%
Retrieving Routes                   : [======================                  ] 56%
Retrieving Routes                   : [=======================                 ] 59%
Retrieving Routes                   : [========================                ] 62%
Retrieving Routes                   : [=========================               ] 64%
Retrieving Routes                   : [===========================             ] 67%
Retrieving Routes                   : [============================            ] 70%
Retrieving Routes                   : [=============================           ] 72%
Retrieving Routes                   : [==============================          ] 75%
Retrieving Routes                   : [===============================         ] 78%
Retrieving Routes                   : [================================        ] 81%
Retrieving Routes                   : [=================================       ] 83%
Retrieving Routes                   : [==================================      ] 86%
Retrieving Routes                   : [===================================     ] 89%
Retrieving Routes                   : [====================================    ] 91%
Retrieving Routes                   : [=====================================   ] 94%
Retrieving Routes                   : [======================================  ] 97%
Retrieving Routes                   : [========================================] 100%

Building Index                      : [=                                       ] 2%
Building Index                      : [==                                      ] 5%
Building Index                      : [===                                     ] 8%
Building Index                      : [====                                    ] 10%
Building Index                      : [=====                                   ] 13%
Building Index                      : [======                                  ] 16%
Building Index                      : [=======                                 ] 18%
Building Index                      : [========                                ] 21%
Building Index                      : [=========                               ] 24%
Building Index                      : [==========                              ] 27%
Building Index                      : [===========                             ] 29%
Building Index                      : [============                            ] 32%
Building Index                      : [==============                          ] 35%
Building Index                      : [===============                         ] 37%
Building Index                      : [================                        ] 40%
Building Index                      : [=================                       ] 43%
Building Index                      : [==================                      ] 45%
Building Index                      : [===================                     ] 48%
Building Index                      : [====================                    ] 51%
Building Index                      : [=====================                   ] 54%
Building Index                      : [======================                  ] 56%
Building Index                      : [=======================                 ] 59%
Building Index                      : [========================                ] 62%
Building Index                      : [=========================               ] 64%
Building Index                      : [===========================             ] 67%
Building Index                      : [============================            ] 70%
Building Index                      : [=============================           ] 72%
Building Index                      : [==============================          ] 75%
Building Index                      : [===============================         ] 78%
Building Index                      : [================================        ] 81%
Building Index                      : [=================================       ] 83%
Building Index                      : [==================================      ] 86%
Building Index                      : [===================================     ] 89%
Building Index                      : [====================================    ] 91%
Building Index                      : [=====================================   ] 94%
Building Index                      : [======================================  ] 97%
Building Index                      : [========================================] 100%2025-05-20 21:20:01,375 home-newsmart-frappe-bench:long: Job OK (site1.local::96d82449-43b6-4d83-888e-f73a600f6572)
2025-05-20 21:20:01,378 Result is kept for 600 seconds
2025-05-20 21:20:01,490 home-newsmart-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.model.delete_doc.delete_dynamic_links', kwargs={'doctype': 'Module Def', 'name': 'Whatsapp Integration'}, method='frappe.model.delete_doc.delete_dynamic_links', site='site1.local', user='Administrator') (site1.local::0042f8bb-bbc6-4269-a4f1-58a117ae0817)
2025-05-20 21:20:03,003 home-newsmart-frappe-bench:default: Job OK (site1.local::0042f8bb-bbc6-4269-a4f1-58a117ae0817)
2025-05-20 21:20:03,004 Result is kept for 600 seconds
2025-05-20 21:20:03,045 home-newsmart-frappe-bench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.search.website_search.build_index_for_all_routes', kwargs={}, method=<function build_index_for_all_routes at 0x7fcec457cd30>, site='site1.local', user='Administrator') (site1.local::d0c02ac7-a6ac-47bb-bddc-a2ea6005e269)

Retrieving Routes                   : [=                                       ] 2%
Retrieving Routes                   : [==                                      ] 5%
Retrieving Routes                   : [===                                     ] 8%
Retrieving Routes                   : [====                                    ] 10%
Retrieving Routes                   : [=====                                   ] 13%
Retrieving Routes                   : [======                                  ] 16%
Retrieving Routes                   : [=======                                 ] 18%
Retrieving Routes                   : [========                                ] 21%
Retrieving Routes                   : [=========                               ] 24%
Retrieving Routes                   : [==========                              ] 27%
Retrieving Routes                   : [===========                             ] 29%
Retrieving Routes                   : [============                            ] 32%
Retrieving Routes                   : [==============                          ] 35%
Retrieving Routes                   : [===============                         ] 37%
Retrieving Routes                   : [================                        ] 40%
Retrieving Routes                   : [=================                       ] 43%
Retrieving Routes                   : [==================                      ] 45%
Retrieving Routes                   : [===================                     ] 48%
Retrieving Routes                   : [====================                    ] 51%
Retrieving Routes                   : [=====================                   ] 54%
Retrieving Routes                   : [======================                  ] 56%
Retrieving Routes                   : [=======================                 ] 59%
Retrieving Routes                   : [========================                ] 62%
Retrieving Routes                   : [=========================               ] 64%
Retrieving Routes                   : [===========================             ] 67%
Retrieving Routes                   : [============================            ] 70%
Retrieving Routes                   : [=============================           ] 72%
Retrieving Routes                   : [==============================          ] 75%
Retrieving Routes                   : [===============================         ] 78%
Retrieving Routes                   : [================================        ] 81%
Retrieving Routes                   : [=================================       ] 83%
Retrieving Routes                   : [==================================      ] 86%
Retrieving Routes                   : [===================================     ] 89%
Retrieving Routes                   : [====================================    ] 91%
Retrieving Routes                   : [=====================================   ] 94%
Retrieving Routes                   : [======================================  ] 97%
Retrieving Routes                   : [========================================] 100%

Building Index                      : [=                                       ] 2%
Building Index                      : [==                                      ] 5%
Building Index                      : [===                                     ] 8%
Building Index                      : [====                                    ] 10%
Building Index                      : [=====                                   ] 13%
Building Index                      : [======                                  ] 16%
Building Index                      : [=======                                 ] 18%
Building Index                      : [========                                ] 21%
Building Index                      : [=========                               ] 24%
Building Index                      : [==========                              ] 27%
Building Index                      : [===========                             ] 29%
Building Index                      : [============                            ] 32%
Building Index                      : [==============                          ] 35%
Building Index                      : [===============                         ] 37%
Building Index                      : [================                        ] 40%
Building Index                      : [=================                       ] 43%
Building Index                      : [==================                      ] 45%
Building Index                      : [===================                     ] 48%
Building Index                      : [====================                    ] 51%
Building Index                      : [=====================                   ] 54%
Building Index                      : [======================                  ] 56%
Building Index                      : [=======================                 ] 59%
Building Index                      : [========================                ] 62%
Building Index                      : [=========================               ] 64%
Building Index                      : [===========================             ] 67%
Building Index                      : [============================            ] 70%
Building Index                      : [=============================           ] 72%
Building Index                      : [==============================          ] 75%
Building Index                      : [===============================         ] 78%
Building Index                      : [================================        ] 81%
Building Index                      : [=================================       ] 83%
Building Index                      : [==================================      ] 86%
Building Index                      : [===================================     ] 89%
Building Index                      : [====================================    ] 91%
Building Index                      : [=====================================   ] 94%
Building Index                      : [======================================  ] 97%
Building Index                      : [========================================] 100%2025-05-20 21:25:07,868 home-newsmart-frappe-bench:long: Job OK (site1.local::d0c02ac7-a6ac-47bb-bddc-a2ea6005e269)
2025-05-20 21:25:07,869 Result is kept for 600 seconds
2025-05-20 21:37:45,383 home-newsmart-frappe-bench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.search.website_search.build_index_for_all_routes', kwargs={}, method=<function build_index_for_all_routes at 0x7fb57f43cd30>, site='site1.local', user='Administrator') (site1.local::276fe4d8-18d4-4bb8-9706-b93bd446874d)

Retrieving Routes                   : [=                                       ] 2%
Retrieving Routes                   : [==                                      ] 5%
Retrieving Routes                   : [===                                     ] 8%
Retrieving Routes                   : [====                                    ] 10%
Retrieving Routes                   : [=====                                   ] 13%
Retrieving Routes                   : [======                                  ] 16%
Retrieving Routes                   : [=======                                 ] 18%
Retrieving Routes                   : [========                                ] 21%
Retrieving Routes                   : [=========                               ] 24%
Retrieving Routes                   : [==========                              ] 27%
Retrieving Routes                   : [===========                             ] 29%
Retrieving Routes                   : [============                            ] 32%
Retrieving Routes                   : [==============                          ] 35%
Retrieving Routes                   : [===============                         ] 37%
Retrieving Routes                   : [================                        ] 40%
Retrieving Routes                   : [=================                       ] 43%
Retrieving Routes                   : [==================                      ] 45%
Retrieving Routes                   : [===================                     ] 48%
Retrieving Routes                   : [====================                    ] 51%
Retrieving Routes                   : [=====================                   ] 54%
Retrieving Routes                   : [======================                  ] 56%
Retrieving Routes                   : [=======================                 ] 59%
Retrieving Routes                   : [========================                ] 62%
Retrieving Routes                   : [=========================               ] 64%
Retrieving Routes                   : [===========================             ] 67%
Retrieving Routes                   : [============================            ] 70%
Retrieving Routes                   : [=============================           ] 72%
Retrieving Routes                   : [==============================          ] 75%
Retrieving Routes                   : [===============================         ] 78%
Retrieving Routes                   : [================================        ] 81%
Retrieving Routes                   : [=================================       ] 83%
Retrieving Routes                   : [==================================      ] 86%
Retrieving Routes                   : [===================================     ] 89%
Retrieving Routes                   : [====================================    ] 91%
Retrieving Routes                   : [=====================================   ] 94%
Retrieving Routes                   : [======================================  ] 97%
Retrieving Routes                   : [========================================] 100%

Building Index                      : [=                                       ] 2%
Building Index                      : [==                                      ] 5%
Building Index                      : [===                                     ] 8%
Building Index                      : [====                                    ] 10%
Building Index                      : [=====                                   ] 13%
Building Index                      : [======                                  ] 16%
Building Index                      : [=======                                 ] 18%
Building Index                      : [========                                ] 21%
Building Index                      : [=========                               ] 24%
Building Index                      : [==========                              ] 27%
Building Index                      : [===========                             ] 29%
Building Index                      : [============                            ] 32%
Building Index                      : [==============                          ] 35%
Building Index                      : [===============                         ] 37%
Building Index                      : [================                        ] 40%
Building Index                      : [=================                       ] 43%
Building Index                      : [==================                      ] 45%
Building Index                      : [===================                     ] 48%
Building Index                      : [====================                    ] 51%
Building Index                      : [=====================                   ] 54%
Building Index                      : [======================                  ] 56%
Building Index                      : [=======================                 ] 59%
Building Index                      : [========================                ] 62%
Building Index                      : [=========================               ] 64%
Building Index                      : [===========================             ] 67%
Building Index                      : [============================            ] 70%
Building Index                      : [=============================           ] 72%
Building Index                      : [==============================          ] 75%
Building Index                      : [===============================         ] 78%
Building Index                      : [================================        ] 81%
Building Index                      : [=================================       ] 83%
Building Index                      : [==================================      ] 86%
Building Index                      : [===================================     ] 89%
Building Index                      : [====================================    ] 91%
Building Index                      : [=====================================   ] 94%
Building Index                      : [======================================  ] 97%
Building Index                      : [========================================] 100%2025-05-20 21:45:23,477 home-newsmart-frappe-bench:long: Job OK (site1.local::276fe4d8-18d4-4bb8-9706-b93bd446874d)
2025-05-20 21:45:23,478 Result is kept for 600 seconds
2025-05-20 21:45:23,604 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-20 21:45:23,614 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-20 21:45:23,663 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-20 21:50:25,952 home-newsmart-frappe-bench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.search.website_search.build_index_for_all_routes', kwargs={}, method=<function build_index_for_all_routes at 0x7f1f5db84d30>, site='site1.local', user='Administrator') (site1.local::849cbdfd-95d5-4706-9f6e-7101a75ea60f)

2025-05-20 21:50:38,487 home-newsmart-frappe-bench:long: Job OK (site1.local::849cbdfd-95d5-4706-9f6e-7101a75ea60f)
2025-05-20 21:50:38,496 Result is kept for 600 seconds
2025-05-20 22:04:23,166 home-newsmart-frappe-bench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.search.website_search.build_index_for_all_routes', kwargs={}, method=<function build_index_for_all_routes at 0x7fa514680d30>, site='site1.local', user='Administrator') (site1.local::000994bf-b282-4e23-93ac-6d9f0c31c0f1)

2025-05-20 22:04:24,695 home-newsmart-frappe-bench:long: Job OK (site1.local::000994bf-b282-4e23-93ac-6d9f0c31c0f1)
2025-05-20 22:04:24,696 Result is kept for 600 seconds
2025-05-20 22:09:20,814 Worker ******************************** [PID 3719]: warm shut down requested
2025-05-20 22:09:20,823 Unsubscribing from channel rq:pubsub:********************************
2025-05-20 22:11:53,330 Worker rq:worker:******************************** started with PID 129690, version 1.15.1
2025-05-20 22:11:53,330 Subscribing to channel rq:pubsub:********************************
2025-05-20 22:11:53,332 *** Listening on home-newsmart-frappe-bench:short, home-newsmart-frappe-bench:default, home-newsmart-frappe-bench:long...
2025-05-20 22:11:53,333 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-20 22:11:53,336 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-20 22:11:53,339 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-20 22:17:10,113 home-newsmart-frappe-bench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.search.website_search.build_index_for_all_routes', kwargs={}, method=<function build_index_for_all_routes at 0x7fe32ab78d30>, site='site1.local', user='Administrator') (site1.local::210b5eb4-0357-4c69-bb47-11c22aee3694)

Retrieving Routes                   : [=                                       ] 3%
Retrieving Routes                   : [==                                      ] 6%
Retrieving Routes                   : [===                                     ] 9%
Retrieving Routes                   : [====                                    ] 12%
Retrieving Routes                   : [======                                  ] 15%
Retrieving Routes                   : [=======                                 ] 18%
Retrieving Routes                   : [========                                ] 21%
Retrieving Routes                   : [=========                               ] 24%
Retrieving Routes                   : [==========                              ] 27%
Retrieving Routes                   : [============                            ] 30%
Retrieving Routes                   : [=============                           ] 33%
Retrieving Routes                   : [==============                          ] 36%
Retrieving Routes                   : [===============                         ] 39%
Retrieving Routes                   : [================                        ] 42%
Retrieving Routes                   : [==================                      ] 45%
Retrieving Routes                   : [===================                     ] 48%
Retrieving Routes                   : [====================                    ] 51%
Retrieving Routes                   : [=====================                   ] 54%
Retrieving Routes                   : [=======================                 ] 57%
Retrieving Routes                   : [========================                ] 60%
Retrieving Routes                   : [=========================               ] 63%
Retrieving Routes                   : [==========================              ] 66%
Retrieving Routes                   : [===========================             ] 69%
Retrieving Routes                   : [=============================           ] 72%
Retrieving Routes                   : [==============================          ] 75%
Retrieving Routes                   : [===============================         ] 78%
Retrieving Routes                   : [================================        ] 81%
Retrieving Routes                   : [=================================       ] 84%
Retrieving Routes                   : [===================================     ] 87%
Retrieving Routes                   : [====================================    ] 90%
Retrieving Routes                   : [=====================================   ] 93%
Retrieving Routes                   : [======================================  ] 96%
Retrieving Routes                   : [========================================] 100%

Building Index                      : [=                                       ] 3%
Building Index                      : [==                                      ] 6%
Building Index                      : [===                                     ] 9%
Building Index                      : [====                                    ] 12%
Building Index                      : [======                                  ] 15%
Building Index                      : [=======                                 ] 18%
Building Index                      : [========                                ] 21%
Building Index                      : [=========                               ] 24%
Building Index                      : [==========                              ] 27%
Building Index                      : [============                            ] 30%
Building Index                      : [=============                           ] 33%
Building Index                      : [==============                          ] 36%
Building Index                      : [===============                         ] 39%
Building Index                      : [================                        ] 42%
Building Index                      : [==================                      ] 45%
Building Index                      : [===================                     ] 48%
Building Index                      : [====================                    ] 51%
Building Index                      : [=====================                   ] 54%
Building Index                      : [=======================                 ] 57%
Building Index                      : [========================                ] 60%
Building Index                      : [=========================               ] 63%
Building Index                      : [==========================              ] 66%
Building Index                      : [===========================             ] 69%
Building Index                      : [=============================           ] 72%
Building Index                      : [==============================          ] 75%
Building Index                      : [===============================         ] 78%
Building Index                      : [================================        ] 81%
Building Index                      : [=================================       ] 84%
Building Index                      : [===================================     ] 87%
Building Index                      : [====================================    ] 90%
Building Index                      : [=====================================   ] 93%
Building Index                      : [======================================  ] 96%
Building Index                      : [========================================] 100%2025-05-20 22:17:51,637 home-newsmart-frappe-bench:long: Job OK (site1.local::210b5eb4-0357-4c69-bb47-11c22aee3694)
2025-05-20 22:17:51,638 Result is kept for 600 seconds
2025-05-20 22:38:06,968 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-20 22:38:07,010 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-20 22:38:07,013 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-21 12:52:11,218 Worker rq:worker:******************************** started with PID 4039, version 1.15.1
2025-05-21 12:52:11,230 Subscribing to channel rq:pubsub:********************************
2025-05-21 12:52:11,232 *** Listening on home-newsmart-frappe-bench:short, home-newsmart-frappe-bench:default, home-newsmart-frappe-bench:long...
2025-05-21 12:52:11,233 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-21 12:52:11,234 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-21 12:52:11,238 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-21 13:19:12,303 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-21 13:19:13,688 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-21 13:19:13,694 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-21 13:42:41,846 Worker ******************************** [PID 4039]: warm shut down requested
2025-05-21 13:43:11,628 Worker rq:worker:******************************** started with PID 81694, version 1.15.1
2025-05-21 13:43:11,628 Subscribing to channel rq:pubsub:********************************
2025-05-21 13:43:11,631 *** Listening on home-newsmart-frappe-bench:short, home-newsmart-frappe-bench:default, home-newsmart-frappe-bench:long...
2025-05-21 13:43:11,633 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-21 13:43:11,638 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-21 13:43:11,642 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-21 14:10:12,310 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-21 14:10:12,626 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-21 14:10:12,629 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-21 14:37:12,890 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-21 14:37:12,891 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-21 14:37:12,895 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-21 14:53:56,674 home-newsmart-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.model.delete_doc.delete_dynamic_links', kwargs={'doctype': 'WhatsApp Sender Account', 'name': 'منير'}, method='frappe.model.delete_doc.delete_dynamic_links', site='site1.local', user='Administrator') (site1.local::88c2100a-9cd9-479e-af59-55af7de57911)
2025-05-21 14:54:01,570 home-newsmart-frappe-bench:default: Job OK (site1.local::88c2100a-9cd9-479e-af59-55af7de57911)
2025-05-21 14:54:01,583 Result is kept for 600 seconds
2025-05-21 14:54:01,669 home-newsmart-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.model.delete_doc.delete_dynamic_links', kwargs={'doctype': 'WhatsApp Sender Account', 'name': 'حساب منير'}, method='frappe.model.delete_doc.delete_dynamic_links', site='site1.local', user='Administrator') (site1.local::1d4c33cc-7af7-4a4a-9014-aeb087362bf2)
2025-05-21 14:54:02,179 home-newsmart-frappe-bench:default: Job OK (site1.local::1d4c33cc-7af7-4a4a-9014-aeb087362bf2)
2025-05-21 14:54:02,180 Result is kept for 600 seconds
2025-05-21 15:00:13,583 home-newsmart-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.model.delete_doc.delete_dynamic_links', kwargs={'doctype': 'WhatsApp Sender Account', 'name': 'selling'}, method='frappe.model.delete_doc.delete_dynamic_links', site='site1.local', user='Administrator') (site1.local::c873134a-0f39-4e77-adc3-a4d64a58a61b)
2025-05-21 15:00:13,832 home-newsmart-frappe-bench:default: Job OK (site1.local::c873134a-0f39-4e77-adc3-a4d64a58a61b)
2025-05-21 15:00:13,832 Result is kept for 600 seconds
2025-05-21 15:06:58,941 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-21 15:06:58,954 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-21 15:06:58,968 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-21 15:09:24,032 home-newsmart-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.model.delete_doc.delete_dynamic_links', kwargs={'doctype': 'WhatsApp Sender Account', 'name': 'send'}, method='frappe.model.delete_doc.delete_dynamic_links', site='site1.local', user='Administrator') (site1.local::8be811c4-8485-4e24-95c1-46ac6d7737b6)
2025-05-21 15:09:24,287 home-newsmart-frappe-bench:default: Job OK (site1.local::8be811c4-8485-4e24-95c1-46ac6d7737b6)
2025-05-21 15:09:24,287 Result is kept for 600 seconds
2025-05-21 15:22:54,406 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-21 15:22:54,426 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-21 15:22:54,434 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-21 15:44:29,786 home-newsmart-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.model.delete_doc.delete_dynamic_links', kwargs={'doctype': 'WhatsApp Sender Account', 'name': 'selling'}, method='frappe.model.delete_doc.delete_dynamic_links', site='site1.local', user='Administrator') (site1.local::3b5202d3-ce9f-4c2b-885d-c25d22f5137a)
2025-05-21 15:44:30,817 home-newsmart-frappe-bench:default: Job OK (site1.local::3b5202d3-ce9f-4c2b-885d-c25d22f5137a)
2025-05-21 15:44:30,820 Result is kept for 600 seconds
2025-05-21 15:44:30,830 home-newsmart-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.model.delete_doc.delete_dynamic_links', kwargs={'doctype': 'WhatsApp Sender Account', 'name': 'buy'}, method='frappe.model.delete_doc.delete_dynamic_links', site='site1.local', user='Administrator') (site1.local::edd04738-2d60-4833-a5f2-8594d8308630)
2025-05-21 15:44:31,261 home-newsmart-frappe-bench:default: Job OK (site1.local::edd04738-2d60-4833-a5f2-8594d8308630)
2025-05-21 15:44:31,263 Result is kept for 600 seconds
2025-05-21 15:51:16,307 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-21 15:51:16,311 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-21 15:51:16,313 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-21 16:02:49,761 home-newsmart-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.model.delete_doc.delete_dynamic_links', kwargs={'doctype': 'WhatsApp Sender Account', 'name': 'silling'}, method='frappe.model.delete_doc.delete_dynamic_links', site='site1.local', user='Administrator') (site1.local::b9b43f7e-40ce-4874-a02b-9bdaca44dab0)
2025-05-21 16:02:50,146 home-newsmart-frappe-bench:default: Job OK (site1.local::b9b43f7e-40ce-4874-a02b-9bdaca44dab0)
2025-05-21 16:02:50,146 Result is kept for 600 seconds
2025-05-21 16:02:50,188 home-newsmart-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.model.delete_doc.delete_dynamic_links', kwargs={'doctype': 'WhatsApp Sender Account', 'name': 'buy'}, method='frappe.model.delete_doc.delete_dynamic_links', site='site1.local', user='Administrator') (site1.local::688bc9df-4b47-407d-8864-fe09d93ef9cd)
2025-05-21 16:02:50,475 home-newsmart-frappe-bench:default: Job OK (site1.local::688bc9df-4b47-407d-8864-fe09d93ef9cd)
2025-05-21 16:02:50,476 Result is kept for 600 seconds
2025-05-21 16:16:20,610 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-21 16:16:20,618 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-21 16:16:20,623 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-21 16:43:20,919 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-21 16:43:20,928 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-21 16:43:20,931 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-24 12:32:09,777 Worker rq:worker:******************************** started with PID 3784, version 1.15.1
2025-05-24 12:32:09,787 Subscribing to channel rq:pubsub:********************************
2025-05-24 12:32:09,804 *** Listening on home-newsmart-frappe-bench:short, home-newsmart-frappe-bench:default, home-newsmart-frappe-bench:long...
2025-05-24 12:32:09,804 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-24 12:32:09,806 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-24 12:32:09,807 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-24 12:42:18,790 Worker ******************************** [PID 3784]: warm shut down requested
2025-05-24 12:42:28,666 Worker rq:worker:******************************** started with PID 18084, version 1.15.1
2025-05-24 12:42:28,666 Subscribing to channel rq:pubsub:********************************
2025-05-24 12:42:28,671 *** Listening on home-newsmart-frappe-bench:short, home-newsmart-frappe-bench:default, home-newsmart-frappe-bench:long...
2025-05-24 12:42:28,676 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-24 12:42:28,729 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-24 12:42:28,770 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-24 12:42:30,571 Worker ******************************** [PID 18084]: warm shut down requested
2025-05-24 12:42:30,627 Unsubscribing from channel rq:pubsub:********************************
2025-05-24 12:42:55,946 Worker rq:worker:******************************** started with PID 18644, version 1.15.1
2025-05-24 12:42:55,947 Subscribing to channel rq:pubsub:********************************
2025-05-24 12:42:55,951 *** Listening on home-newsmart-frappe-bench:short, home-newsmart-frappe-bench:default, home-newsmart-frappe-bench:long...
2025-05-24 12:42:55,958 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-24 12:42:55,982 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-24 12:42:55,990 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-24 12:42:58,481 Worker ******************************** [PID 18644]: warm shut down requested
2025-05-24 12:42:58,490 Unsubscribing from channel rq:pubsub:********************************
2025-05-24 13:02:47,886 Worker rq:worker:******************************** started with PID 3455, version 1.15.1
2025-05-24 13:02:47,899 Subscribing to channel rq:pubsub:********************************
2025-05-24 13:02:47,908 *** Listening on home-newsmart-frappe-bench:short, home-newsmart-frappe-bench:default, home-newsmart-frappe-bench:long...
2025-05-24 13:02:47,911 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-24 13:02:47,920 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-24 13:02:47,924 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-24 13:02:50,130 Worker ******************************** [PID 3455]: warm shut down requested
2025-05-24 13:02:50,132 Unsubscribing from channel rq:pubsub:********************************
2025-05-24 13:11:39,235 Worker rq:worker:******************************** started with PID 16783, version 1.15.1
2025-05-24 13:11:39,236 Subscribing to channel rq:pubsub:********************************
2025-05-24 13:11:39,287 *** Listening on home-newsmart-frappe-bench:short, home-newsmart-frappe-bench:default, home-newsmart-frappe-bench:long...
2025-05-24 13:11:39,300 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-24 13:11:39,304 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-24 13:11:39,307 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-24 13:34:19,968 Worker ******************************** [PID 16783]: warm shut down requested
2025-05-24 13:35:27,412 Worker rq:worker:******************************** started with PID 53151, version 1.15.1
2025-05-24 13:35:27,412 Subscribing to channel rq:pubsub:********************************
2025-05-24 13:35:27,422 *** Listening on home-newsmart-frappe-bench:short, home-newsmart-frappe-bench:default, home-newsmart-frappe-bench:long...
2025-05-24 13:35:27,429 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-24 13:35:27,455 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-24 13:35:27,466 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-24 13:44:53,234 Worker ******************************** [PID 53151]: warm shut down requested
2025-05-24 13:45:24,220 Worker rq:worker:******************************** started with PID 65018, version 1.15.1
2025-05-24 13:45:24,221 Subscribing to channel rq:pubsub:********************************
2025-05-24 13:45:24,224 *** Listening on home-newsmart-frappe-bench:short, home-newsmart-frappe-bench:default, home-newsmart-frappe-bench:long...
2025-05-24 13:45:24,228 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-24 13:45:24,229 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-24 13:45:24,231 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-24 13:48:38,029 home-newsmart-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.model.delete_doc.delete_dynamic_links', kwargs={'doctype': 'WhatsApp Sender Account', 'name': 'buy'}, method='frappe.model.delete_doc.delete_dynamic_links', site='site1.local', user='Administrator') (site1.local::8c832ecf-fb1c-4700-a678-1a8f0411101f)
2025-05-24 13:48:40,551 home-newsmart-frappe-bench:default: Job OK (site1.local::8c832ecf-fb1c-4700-a678-1a8f0411101f)
2025-05-24 13:48:40,551 Result is kept for 600 seconds
2025-05-24 14:01:23,348 home-newsmart-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.model.delete_doc.delete_dynamic_links', kwargs={'doctype': 'WhatsApp Sender Account', 'name': 'invois'}, method='frappe.model.delete_doc.delete_dynamic_links', site='site1.local', user='Administrator') (site1.local::19b7e66b-dc66-4b7d-b2f7-55d2ecb018cc)
2025-05-24 14:01:30,208 home-newsmart-frappe-bench:default: Job OK (site1.local::19b7e66b-dc66-4b7d-b2f7-55d2ecb018cc)
2025-05-24 14:01:30,211 Result is kept for 600 seconds
2025-05-24 14:08:15,441 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-24 14:08:15,467 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-24 14:08:15,486 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-24 14:14:39,382 home-newsmart-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.model.delete_doc.delete_dynamic_links', kwargs={'doctype': 'Scheduled Job Type', 'name': 'email_account.notify_unreplied'}, method='frappe.model.delete_doc.delete_dynamic_links', site='newsmart.local', user='Administrator') (newsmart.local::********-16c0-492c-bb14-5c5bd8bb8794)
2025-05-24 14:14:42,069 home-newsmart-frappe-bench:default: Job OK (newsmart.local::********-16c0-492c-bb14-5c5bd8bb8794)
2025-05-24 14:14:42,070 Result is kept for 600 seconds
2025-05-24 14:14:42,084 home-newsmart-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.model.delete_doc.delete_dynamic_links', kwargs={'doctype': 'Scheduled Job Type', 'name': 'global_search.sync_global_search..., method='frappe.model.delete_doc.delete_dynamic_links', site='newsmart.local', user='Administrator') (newsmart.local::2c631cbb-81be-45fa-b681-51addf46da9e)
2025-05-24 14:14:43,200 home-newsmart-frappe-bench:default: Job OK (newsmart.local::2c631cbb-81be-45fa-b681-51addf46da9e)
2025-05-24 14:14:43,201 Result is kept for 600 seconds
2025-05-24 14:14:43,234 home-newsmart-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.model.delete_doc.delete_dynamic_links', kwargs={'doctype': 'Scheduled Job Type', 'name': 'deferred_insert.save_to_db'}, method='frappe.model.delete_doc.delete_dynamic_links', site='newsmart.local', user='Administrator') (newsmart.local::3bd07125-efdc-4c52-8b2b-88b7c99ba4f2)
2025-05-24 14:14:44,182 home-newsmart-frappe-bench:default: Job OK (newsmart.local::3bd07125-efdc-4c52-8b2b-88b7c99ba4f2)
2025-05-24 14:14:44,182 Result is kept for 600 seconds
2025-05-24 14:14:44,216 home-newsmart-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.model.delete_doc.delete_dynamic_links', kwargs={'doctype': 'Scheduled Job Type', 'name': 'reminder.send_reminders'}, method='frappe.model.delete_doc.delete_dynamic_links', site='newsmart.local', user='Administrator') (newsmart.local::9b778d69-52d1-4964-a72f-1002bd82f954)
2025-05-24 14:14:44,789 home-newsmart-frappe-bench:default: Job OK (newsmart.local::9b778d69-52d1-4964-a72f-1002bd82f954)
2025-05-24 14:14:44,790 Result is kept for 600 seconds
2025-05-24 14:14:44,856 home-newsmart-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.model.delete_doc.delete_dynamic_links', kwargs={'doctype': 'Scheduled Job Type', 'name': 'change_log.check_for_update'}, method='frappe.model.delete_doc.delete_dynamic_links', site='newsmart.local', user='Administrator') (newsmart.local::f5472e63-b298-435c-bec2-37fe24315a12)
2025-05-24 14:14:48,131 home-newsmart-frappe-bench:default: Job OK (newsmart.local::f5472e63-b298-435c-bec2-37fe24315a12)
2025-05-24 14:14:48,132 Result is kept for 600 seconds
2025-05-24 14:14:48,153 home-newsmart-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.model.delete_doc.delete_dynamic_links', kwargs={'doctype': 'Scheduled Job Type', 'name': 'auto_repeat.set_auto_repeat_as_c..., method='frappe.model.delete_doc.delete_dynamic_links', site='newsmart.local', user='Administrator') (newsmart.local::01409b6a-b318-41d4-a039-f55a379fec80)
2025-05-24 14:14:48,801 home-newsmart-frappe-bench:default: Job OK (newsmart.local::01409b6a-b318-41d4-a039-f55a379fec80)
2025-05-24 14:14:48,805 Result is kept for 600 seconds
2025-05-24 14:20:35,082 home-newsmart-frappe-bench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.search.website_search.build_index_for_all_routes', kwargs={}, method=<function build_index_for_all_routes at 0x7f9734678d30>, site='site1.local', user='Administrator') (site1.local::bafb9d44-9717-4644-8b04-37ced52502cc)

Retrieving Routes                   : [=                                       ] 2%
Retrieving Routes                   : [==                                      ] 5%
Retrieving Routes                   : [===                                     ] 8%
Retrieving Routes                   : [====                                    ] 10%
Retrieving Routes                   : [=====                                   ] 13%
Retrieving Routes                   : [======                                  ] 16%
Retrieving Routes                   : [=======                                 ] 18%
Retrieving Routes                   : [========                                ] 21%
Retrieving Routes                   : [=========                               ] 24%
Retrieving Routes                   : [==========                              ] 27%
Retrieving Routes                   : [===========                             ] 29%
Retrieving Routes                   : [============                            ] 32%
Retrieving Routes                   : [==============                          ] 35%
Retrieving Routes                   : [===============                         ] 37%
Retrieving Routes                   : [================                        ] 40%
Retrieving Routes                   : [=================                       ] 43%
Retrieving Routes                   : [==================                      ] 45%
Retrieving Routes                   : [===================                     ] 48%
Retrieving Routes                   : [====================                    ] 51%
Retrieving Routes                   : [=====================                   ] 54%
Retrieving Routes                   : [======================                  ] 56%
Retrieving Routes                   : [=======================                 ] 59%
Retrieving Routes                   : [========================                ] 62%
Retrieving Routes                   : [=========================               ] 64%
Retrieving Routes                   : [===========================             ] 67%
Retrieving Routes                   : [============================            ] 70%
Retrieving Routes                   : [=============================           ] 72%
Retrieving Routes                   : [==============================          ] 75%
Retrieving Routes                   : [===============================         ] 78%
Retrieving Routes                   : [================================        ] 81%
Retrieving Routes                   : [=================================       ] 83%
Retrieving Routes                   : [==================================      ] 86%
Retrieving Routes                   : [===================================     ] 89%
Retrieving Routes                   : [====================================    ] 91%
Retrieving Routes                   : [=====================================   ] 94%
Retrieving Routes                   : [======================================  ] 97%
Retrieving Routes                   : [========================================] 100%

Building Index                      : [=                                       ] 2%
Building Index                      : [==                                      ] 5%
Building Index                      : [===                                     ] 8%
Building Index                      : [====                                    ] 10%
Building Index                      : [=====                                   ] 13%
Building Index                      : [======                                  ] 16%
Building Index                      : [=======                                 ] 18%
Building Index                      : [========                                ] 21%
Building Index                      : [=========                               ] 24%
Building Index                      : [==========                              ] 27%
Building Index                      : [===========                             ] 29%
Building Index                      : [============                            ] 32%
Building Index                      : [==============                          ] 35%
Building Index                      : [===============                         ] 37%
Building Index                      : [================                        ] 40%
Building Index                      : [=================                       ] 43%
Building Index                      : [==================                      ] 45%
Building Index                      : [===================                     ] 48%
Building Index                      : [====================                    ] 51%
Building Index                      : [=====================                   ] 54%
Building Index                      : [======================                  ] 56%
Building Index                      : [=======================                 ] 59%
Building Index                      : [========================                ] 62%
Building Index                      : [=========================               ] 64%
Building Index                      : [===========================             ] 67%
Building Index                      : [============================            ] 70%
Building Index                      : [=============================           ] 72%
Building Index                      : [==============================          ] 75%
Building Index                      : [===============================         ] 78%
Building Index                      : [================================        ] 81%
Building Index                      : [=================================       ] 83%
Building Index                      : [==================================      ] 86%
Building Index                      : [===================================     ] 89%
Building Index                      : [====================================    ] 91%
Building Index                      : [=====================================   ] 94%
Building Index                      : [======================================  ] 97%
Building Index                      : [========================================] 100%2025-05-24 14:22:47,169 home-newsmart-frappe-bench:long: Job OK (site1.local::bafb9d44-9717-4644-8b04-37ced52502cc)
2025-05-24 14:22:47,169 Result is kept for 600 seconds
2025-05-24 14:25:50,162 home-newsmart-frappe-bench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.search.website_search.build_index_for_all_routes', kwargs={}, method=<function build_index_for_all_routes at 0x7fdcc8578d30>, site='site1.local', user='Administrator') (site1.local::0c103317-9f29-4f46-8f52-f2aae2b69d45)

Retrieving Routes                   : [=                                       ] 2%
Retrieving Routes                   : [==                                      ] 5%
Retrieving Routes                   : [===                                     ] 8%
Retrieving Routes                   : [====                                    ] 10%
Retrieving Routes                   : [=====                                   ] 13%
Retrieving Routes                   : [======                                  ] 16%
Retrieving Routes                   : [=======                                 ] 18%
Retrieving Routes                   : [========                                ] 21%
Retrieving Routes                   : [=========                               ] 24%
Retrieving Routes                   : [==========                              ] 27%
Retrieving Routes                   : [===========                             ] 29%
Retrieving Routes                   : [============                            ] 32%
Retrieving Routes                   : [==============                          ] 35%
Retrieving Routes                   : [===============                         ] 37%
Retrieving Routes                   : [================                        ] 40%
Retrieving Routes                   : [=================                       ] 43%
Retrieving Routes                   : [==================                      ] 45%
Retrieving Routes                   : [===================                     ] 48%
Retrieving Routes                   : [====================                    ] 51%
Retrieving Routes                   : [=====================                   ] 54%
Retrieving Routes                   : [======================                  ] 56%
Retrieving Routes                   : [=======================                 ] 59%
Retrieving Routes                   : [========================                ] 62%
Retrieving Routes                   : [=========================               ] 64%
Retrieving Routes                   : [===========================             ] 67%
Retrieving Routes                   : [============================            ] 70%
Retrieving Routes                   : [=============================           ] 72%
Retrieving Routes                   : [==============================          ] 75%
Retrieving Routes                   : [===============================         ] 78%
Retrieving Routes                   : [================================        ] 81%
Retrieving Routes                   : [=================================       ] 83%
Retrieving Routes                   : [==================================      ] 86%
Retrieving Routes                   : [===================================     ] 89%
Retrieving Routes                   : [====================================    ] 91%
Retrieving Routes                   : [=====================================   ] 94%
Retrieving Routes                   : [======================================  ] 97%
Retrieving Routes                   : [========================================] 100%

Building Index                      : [=                                       ] 2%
Building Index                      : [==                                      ] 5%
Building Index                      : [===                                     ] 8%
Building Index                      : [====                                    ] 10%
Building Index                      : [=====                                   ] 13%
Building Index                      : [======                                  ] 16%
Building Index                      : [=======                                 ] 18%
Building Index                      : [========                                ] 21%
Building Index                      : [=========                               ] 24%
Building Index                      : [==========                              ] 27%
Building Index                      : [===========                             ] 29%
Building Index                      : [============                            ] 32%
Building Index                      : [==============                          ] 35%
Building Index                      : [===============                         ] 37%
Building Index                      : [================                        ] 40%
Building Index                      : [=================                       ] 43%
Building Index                      : [==================                      ] 45%
Building Index                      : [===================                     ] 48%
Building Index                      : [====================                    ] 51%
Building Index                      : [=====================                   ] 54%
Building Index                      : [======================                  ] 56%
Building Index                      : [=======================                 ] 59%
Building Index                      : [========================                ] 62%
Building Index                      : [=========================               ] 64%
Building Index                      : [===========================             ] 67%
Building Index                      : [============================            ] 70%
Building Index                      : [=============================           ] 72%
Building Index                      : [==============================          ] 75%
Building Index                      : [===============================         ] 78%
Building Index                      : [================================        ] 81%
Building Index                      : [=================================       ] 83%
Building Index                      : [==================================      ] 86%
Building Index                      : [===================================     ] 89%
Building Index                      : [====================================    ] 91%
Building Index                      : [=====================================   ] 94%
Building Index                      : [======================================  ] 97%
Building Index                      : [========================================] 100%2025-05-24 14:27:55,281 home-newsmart-frappe-bench:long: Job OK (site1.local::0c103317-9f29-4f46-8f52-f2aae2b69d45)
2025-05-24 14:27:55,282 Result is kept for 600 seconds
2025-05-24 14:28:49,283 home-newsmart-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.model.delete_doc.delete_dynamic_links', kwargs={'doctype': 'WhatsApp Sender Account', 'name': 'seling'}, method='frappe.model.delete_doc.delete_dynamic_links', site='site1.local', user='Administrator') (site1.local::b4800fc3-eb05-4fe1-a0a7-23ef6fdcb869)
2025-05-24 14:28:49,757 home-newsmart-frappe-bench:default: Job OK (site1.local::b4800fc3-eb05-4fe1-a0a7-23ef6fdcb869)
2025-05-24 14:28:49,758 Result is kept for 600 seconds
2025-05-24 14:35:34,778 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-24 14:35:34,787 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-24 14:35:34,796 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-24 15:02:35,055 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-24 15:02:35,060 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-24 15:02:35,062 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-24 15:29:35,217 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-24 15:29:35,315 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-24 15:29:35,320 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-24 15:47:33,094 home-newsmart-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.model.delete_doc.delete_dynamic_links', kwargs={'doctype': 'WhatsApp Sender Account', 'name': 'seling'}, method='frappe.model.delete_doc.delete_dynamic_links', site='site1.local', user='Administrator') (site1.local::9a88acd6-0b14-4f58-9d8d-c3cf769981dd)
2025-05-24 15:47:34,813 home-newsmart-frappe-bench:default: Job OK (site1.local::9a88acd6-0b14-4f58-9d8d-c3cf769981dd)
2025-05-24 15:47:34,814 Result is kept for 600 seconds
2025-05-24 15:54:19,927 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-24 15:54:19,930 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-24 15:54:19,931 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-24 16:21:20,146 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-24 16:21:20,148 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-24 16:21:20,149 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-25 13:10:31,620 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-25 13:10:31,627 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-25 13:10:31,628 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-25 13:30:11,067 home-newsmart-frappe-bench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.search.website_search.build_index_for_all_routes', kwargs={}, method=<function build_index_for_all_routes at 0x7f3c99d8ac20>, site='site1.local', user='Administrator') (site1.local::917daffb-242f-498a-8a76-078aa9721b5e)

Retrieving Routes                   : [=                                       ] 2%
Retrieving Routes                   : [==                                      ] 5%
Retrieving Routes                   : [===                                     ] 8%
Retrieving Routes                   : [====                                    ] 10%
Retrieving Routes                   : [=====                                   ] 13%
Retrieving Routes                   : [======                                  ] 16%
Retrieving Routes                   : [=======                                 ] 18%
Retrieving Routes                   : [========                                ] 21%
Retrieving Routes                   : [=========                               ] 24%
Retrieving Routes                   : [==========                              ] 27%
Retrieving Routes                   : [===========                             ] 29%
Retrieving Routes                   : [============                            ] 32%
Retrieving Routes                   : [==============                          ] 35%
Retrieving Routes                   : [===============                         ] 37%
Retrieving Routes                   : [================                        ] 40%
Retrieving Routes                   : [=================                       ] 43%
Retrieving Routes                   : [==================                      ] 45%
Retrieving Routes                   : [===================                     ] 48%
Retrieving Routes                   : [====================                    ] 51%
Retrieving Routes                   : [=====================                   ] 54%
Retrieving Routes                   : [======================                  ] 56%
Retrieving Routes                   : [=======================                 ] 59%
Retrieving Routes                   : [========================                ] 62%
Retrieving Routes                   : [=========================               ] 64%
Retrieving Routes                   : [===========================             ] 67%
Retrieving Routes                   : [============================            ] 70%
Retrieving Routes                   : [=============================           ] 72%
Retrieving Routes                   : [==============================          ] 75%
Retrieving Routes                   : [===============================         ] 78%
Retrieving Routes                   : [================================        ] 81%
Retrieving Routes                   : [=================================       ] 83%
Retrieving Routes                   : [==================================      ] 86%
Retrieving Routes                   : [===================================     ] 89%
Retrieving Routes                   : [====================================    ] 91%
Retrieving Routes                   : [=====================================   ] 94%
Retrieving Routes                   : [======================================  ] 97%
Retrieving Routes                   : [========================================] 100%

Building Index                      : [=                                       ] 2%
Building Index                      : [==                                      ] 5%
Building Index                      : [===                                     ] 8%
Building Index                      : [====                                    ] 10%
Building Index                      : [=====                                   ] 13%
Building Index                      : [======                                  ] 16%
Building Index                      : [=======                                 ] 18%
Building Index                      : [========                                ] 21%
Building Index                      : [=========                               ] 24%
Building Index                      : [==========                              ] 27%
Building Index                      : [===========                             ] 29%
Building Index                      : [============                            ] 32%
Building Index                      : [==============                          ] 35%
Building Index                      : [===============                         ] 37%
Building Index                      : [================                        ] 40%
Building Index                      : [=================                       ] 43%
Building Index                      : [==================                      ] 45%
Building Index                      : [===================                     ] 48%
Building Index                      : [====================                    ] 51%
Building Index                      : [=====================                   ] 54%
Building Index                      : [======================                  ] 56%
Building Index                      : [=======================                 ] 59%
Building Index                      : [========================                ] 62%
Building Index                      : [=========================               ] 64%
Building Index                      : [===========================             ] 67%
Building Index                      : [============================            ] 70%
Building Index                      : [=============================           ] 72%
Building Index                      : [==============================          ] 75%
Building Index                      : [===============================         ] 78%
Building Index                      : [================================        ] 81%
Building Index                      : [=================================       ] 83%
Building Index                      : [==================================      ] 86%
Building Index                      : [===================================     ] 89%
Building Index                      : [====================================    ] 91%
Building Index                      : [=====================================   ] 94%
Building Index                      : [======================================  ] 97%
Building Index                      : [========================================] 100%2025-05-25 13:33:12,806 home-newsmart-frappe-bench:long: Job OK (site1.local::917daffb-242f-498a-8a76-078aa9721b5e)
2025-05-25 13:33:12,809 Result is kept for 600 seconds
2025-05-25 13:39:58,060 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-25 13:39:58,113 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-25 13:39:58,121 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-25 14:06:58,971 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-25 14:06:59,363 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-25 14:06:59,366 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-25 14:11:03,486 Worker ******************************** [PID 65018]: warm shut down requested
2025-05-25 14:11:20,905 Worker rq:worker:******************************** started with PID 412244, version 1.15.1
2025-05-25 14:11:20,905 Subscribing to channel rq:pubsub:********************************
2025-05-25 14:11:20,909 *** Listening on home-newsmart-frappe-bench:short, home-newsmart-frappe-bench:default, home-newsmart-frappe-bench:long...
2025-05-25 14:11:20,911 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-25 14:11:20,916 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-25 14:11:20,917 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-25 14:38:21,914 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-25 14:38:22,596 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-25 14:38:22,611 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-25 14:38:29,527 Worker ******************************** [PID 412244]: warm shut down requested
2025-05-25 14:38:45,280 Worker rq:worker:******************************** started with PID 447078, version 1.15.1
2025-05-25 14:38:45,281 Subscribing to channel rq:pubsub:********************************
2025-05-25 14:38:45,285 *** Listening on home-newsmart-frappe-bench:short, home-newsmart-frappe-bench:default, home-newsmart-frappe-bench:long...
2025-05-25 14:38:45,287 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-25 14:38:45,295 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-25 14:38:45,305 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-25 14:43:25,070 Worker ******************************** [PID 447078]: warm shut down requested
2025-05-25 14:43:37,058 Worker rq:worker:******************************** started with PID 453098, version 1.15.1
2025-05-25 14:43:37,058 Subscribing to channel rq:pubsub:********************************
2025-05-25 14:43:37,069 *** Listening on home-newsmart-frappe-bench:short, home-newsmart-frappe-bench:default, home-newsmart-frappe-bench:long...
2025-05-25 14:43:37,071 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-25 14:43:37,073 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-25 14:43:37,076 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-25 14:45:14,592 Worker ******************************** [PID 453098]: warm shut down requested
2025-05-25 14:46:17,544 Worker rq:worker:******************************** started with PID 456264, version 1.15.1
2025-05-25 14:46:17,545 Subscribing to channel rq:pubsub:********************************
2025-05-25 14:46:17,552 *** Listening on home-newsmart-frappe-bench:short, home-newsmart-frappe-bench:default, home-newsmart-frappe-bench:long...
2025-05-25 14:46:17,556 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-25 14:46:17,558 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-25 14:46:17,559 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-25 14:49:09,352 Worker ******************************** [PID 456264]: warm shut down requested
2025-05-25 14:51:33,814 Worker rq:worker:******************************** started with PID 463403, version 1.15.1
2025-05-25 14:51:33,814 Subscribing to channel rq:pubsub:********************************
2025-05-25 14:51:33,819 *** Listening on home-newsmart-frappe-bench:short, home-newsmart-frappe-bench:default, home-newsmart-frappe-bench:long...
2025-05-25 14:51:33,819 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-25 14:51:33,820 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-25 14:51:33,821 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-25 14:52:10,990 Worker ******************************** [PID 463403]: warm shut down requested
2025-05-25 14:56:12,760 Worker rq:worker:******************************** started with PID 4919, version 1.15.1
2025-05-25 14:56:12,769 Subscribing to channel rq:pubsub:********************************
2025-05-25 14:56:12,777 *** Listening on home-newsmart-frappe-bench:short, home-newsmart-frappe-bench:default, home-newsmart-frappe-bench:long...
2025-05-25 14:56:12,784 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-25 14:56:12,800 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-25 14:56:12,824 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-25 15:23:14,800 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-25 15:23:15,217 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-25 15:23:15,223 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-25 15:29:00,765 Worker ******************************** [PID 4919]: warm shut down requested
2025-05-25 15:29:17,606 Worker rq:worker:******************************** started with PID 54398, version 1.15.1
2025-05-25 15:29:17,606 Subscribing to channel rq:pubsub:********************************
2025-05-25 15:29:17,614 *** Listening on home-newsmart-frappe-bench:short, home-newsmart-frappe-bench:default, home-newsmart-frappe-bench:long...
2025-05-25 15:29:17,623 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-25 15:29:17,759 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-25 15:29:17,824 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-25 15:33:15,834 home-newsmart-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.model.delete_doc.delete_dynamic_links', kwargs={'doctype': 'WhatsApp Sender Account', 'name': 'seling'}, method='frappe.model.delete_doc.delete_dynamic_links', site='site1.local', user='Administrator') (site1.local::983f7c17-845a-4848-97b5-8582135e2063)
2025-05-25 15:33:17,389 home-newsmart-frappe-bench:default: Job OK (site1.local::983f7c17-845a-4848-97b5-8582135e2063)
2025-05-25 15:33:17,390 Result is kept for 600 seconds
2025-05-25 15:53:32,693 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-25 15:53:32,731 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-25 15:53:32,735 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-25 16:10:14,682 home-newsmart-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='erpnext.utilities.bulk_transaction.job', kwargs={'deserialized_data': [{'name': 'ACC-SINV-2025-00001', 'owner': 'Administra..., method=<function job at 0x7f4e9f50caf0>, site='site1.local', user='Administrator') (site1.local::09b38212-7fcb-4fd4-9624-86b2f6fb49c7)
2025-05-25 16:10:24,187 home-newsmart-frappe-bench:default: Job OK (site1.local::09b38212-7fcb-4fd4-9624-86b2f6fb49c7)
2025-05-25 16:10:24,187 Result is kept for 600 seconds
2025-05-25 16:17:09,295 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-25 16:17:09,330 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-25 16:17:09,331 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-25 16:44:09,704 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-25 16:44:09,735 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-25 16:44:09,816 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-26 12:59:41,818 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-26 12:59:41,846 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-26 12:59:41,857 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-26 13:01:01,509 Worker ******************************** [PID 54398]: warm shut down requested
2025-05-26 13:01:22,796 Worker rq:worker:******************************** started with PID 185013, version 1.15.1
2025-05-26 13:01:22,796 Subscribing to channel rq:pubsub:********************************
2025-05-26 13:01:22,798 *** Listening on home-newsmart-frappe-bench:short, home-newsmart-frappe-bench:default, home-newsmart-frappe-bench:long...
2025-05-26 13:01:22,798 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-26 13:01:22,800 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-26 13:01:22,800 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-26 13:12:33,574 Worker ******************************** [PID 185013]: warm shut down requested
2025-05-26 13:20:51,158 Worker rq:worker:******************************** started with PID 216646, version 1.15.1
2025-05-26 13:20:51,158 Subscribing to channel rq:pubsub:********************************
2025-05-26 13:20:51,168 *** Listening on home-newsmart-frappe-bench:short, home-newsmart-frappe-bench:default, home-newsmart-frappe-bench:long...
2025-05-26 13:20:51,174 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-26 13:20:51,213 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-26 13:20:51,220 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-26 13:22:46,943 home-newsmart-frappe-bench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.search.website_search.build_index_for_all_routes', kwargs={}, method=<function build_index_for_all_routes at 0x7fad690c2dd0>, site='site1.local', user='Administrator') (site1.local::ac021bbc-4d74-4c8f-8170-6df60548fe8b)

Retrieving Routes                   : [=                                       ] 2%
Retrieving Routes                   : [==                                      ] 5%
Retrieving Routes                   : [===                                     ] 8%
Retrieving Routes                   : [====                                    ] 10%
Retrieving Routes                   : [=====                                   ] 13%
Retrieving Routes                   : [======                                  ] 16%
Retrieving Routes                   : [=======                                 ] 18%
Retrieving Routes                   : [========                                ] 21%
Retrieving Routes                   : [=========                               ] 24%
Retrieving Routes                   : [==========                              ] 27%
Retrieving Routes                   : [===========                             ] 29%
Retrieving Routes                   : [============                            ] 32%
Retrieving Routes                   : [==============                          ] 35%
Retrieving Routes                   : [===============                         ] 37%
Retrieving Routes                   : [================                        ] 40%
Retrieving Routes                   : [=================                       ] 43%
Retrieving Routes                   : [==================                      ] 45%
Retrieving Routes                   : [===================                     ] 48%
Retrieving Routes                   : [====================                    ] 51%
Retrieving Routes                   : [=====================                   ] 54%
Retrieving Routes                   : [======================                  ] 56%
Retrieving Routes                   : [=======================                 ] 59%
Retrieving Routes                   : [========================                ] 62%
Retrieving Routes                   : [=========================               ] 64%
Retrieving Routes                   : [===========================             ] 67%
Retrieving Routes                   : [============================            ] 70%
Retrieving Routes                   : [=============================           ] 72%
Retrieving Routes                   : [==============================          ] 75%
Retrieving Routes                   : [===============================         ] 78%
Retrieving Routes                   : [================================        ] 81%
Retrieving Routes                   : [=================================       ] 83%
Retrieving Routes                   : [==================================      ] 86%
Retrieving Routes                   : [===================================     ] 89%
Retrieving Routes                   : [====================================    ] 91%
Retrieving Routes                   : [=====================================   ] 94%
Retrieving Routes                   : [======================================  ] 97%
Retrieving Routes                   : [========================================] 100%

Building Index                      : [=                                       ] 2%
Building Index                      : [==                                      ] 5%
Building Index                      : [===                                     ] 8%
Building Index                      : [====                                    ] 10%
Building Index                      : [=====                                   ] 13%
Building Index                      : [======                                  ] 16%
Building Index                      : [=======                                 ] 18%
Building Index                      : [========                                ] 21%
Building Index                      : [=========                               ] 24%
Building Index                      : [==========                              ] 27%
Building Index                      : [===========                             ] 29%
Building Index                      : [============                            ] 32%
Building Index                      : [==============                          ] 35%
Building Index                      : [===============                         ] 37%
Building Index                      : [================                        ] 40%
Building Index                      : [=================                       ] 43%
Building Index                      : [==================                      ] 45%
Building Index                      : [===================                     ] 48%
Building Index                      : [====================                    ] 51%
Building Index                      : [=====================                   ] 54%
Building Index                      : [======================                  ] 56%
Building Index                      : [=======================                 ] 59%
Building Index                      : [========================                ] 62%
Building Index                      : [=========================               ] 64%
Building Index                      : [===========================             ] 67%
Building Index                      : [============================            ] 70%
Building Index                      : [=============================           ] 72%
Building Index                      : [==============================          ] 75%
Building Index                      : [===============================         ] 78%
Building Index                      : [================================        ] 81%
Building Index                      : [=================================       ] 83%
Building Index                      : [==================================      ] 86%
Building Index                      : [===================================     ] 89%
Building Index                      : [====================================    ] 91%
Building Index                      : [=====================================   ] 94%
Building Index                      : [======================================  ] 97%
Building Index                      : [========================================] 100%2025-05-26 13:25:30,909 home-newsmart-frappe-bench:long: Job OK (site1.local::ac021bbc-4d74-4c8f-8170-6df60548fe8b)
2025-05-26 13:25:30,910 Result is kept for 600 seconds
2025-05-26 13:45:46,173 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-26 13:45:46,338 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-26 13:45:46,343 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-26 14:12:46,597 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-26 14:12:46,603 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-26 14:12:46,608 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-26 14:39:46,918 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-26 14:39:46,926 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-26 14:39:46,931 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-26 14:51:40,929 Worker rq:worker:******************************** started with PID 4005, version 1.15.1
2025-05-26 14:51:40,951 Subscribing to channel rq:pubsub:********************************
2025-05-26 14:51:40,961 *** Listening on home-newsmart-frappe-bench:short, home-newsmart-frappe-bench:default, home-newsmart-frappe-bench:long...
2025-05-26 14:51:40,964 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-26 14:51:40,980 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-26 14:51:40,986 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-26 15:18:42,879 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-26 15:18:43,362 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-26 15:18:43,371 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-26 15:45:43,529 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-26 15:45:43,548 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-26 15:45:43,557 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-26 16:33:32,595 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-26 16:33:32,612 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-26 16:33:32,619 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-26 17:00:32,774 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-26 17:00:32,786 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-26 17:00:32,791 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-26 17:01:49,956 Worker ******************************** [PID 4005]: warm shut down requested
2025-05-26 17:02:05,189 Worker rq:worker:******************************** started with PID 142091, version 1.15.1
2025-05-26 17:02:05,189 Subscribing to channel rq:pubsub:********************************
2025-05-26 17:02:05,213 *** Listening on home-newsmart-frappe-bench:short, home-newsmart-frappe-bench:default, home-newsmart-frappe-bench:long...
2025-05-26 17:02:05,227 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-26 17:02:05,230 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-26 17:02:05,233 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-26 17:29:05,787 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-26 17:29:06,150 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-26 17:29:06,164 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-26 17:29:23,941 home-newsmart-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.model.delete_doc.delete_dynamic_links', kwargs={'doctype': 'WhatsApp Sender Account', 'name': 'buy'}, method='frappe.model.delete_doc.delete_dynamic_links', site='site1.local', user='Administrator') (site1.local::1728fa6a-2c85-4806-bd66-43e5fc1d5299)
2025-05-26 17:29:29,792 home-newsmart-frappe-bench:default: Job OK (site1.local::1728fa6a-2c85-4806-bd66-43e5fc1d5299)
2025-05-26 17:29:29,794 Result is kept for 600 seconds
2025-05-27 12:45:19,704 Worker rq:worker:******************************** started with PID 4406, version 1.15.1
2025-05-27 12:45:19,711 Subscribing to channel rq:pubsub:********************************
2025-05-27 12:45:19,714 *** Listening on home-newsmart-frappe-bench:short, home-newsmart-frappe-bench:default, home-newsmart-frappe-bench:long...
2025-05-27 12:45:19,715 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-27 12:45:19,717 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-27 12:45:19,719 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-27 13:12:23,470 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-27 13:12:24,833 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-27 13:12:24,837 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-27 13:39:25,176 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-27 13:39:25,180 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-27 13:39:25,185 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-27 14:06:25,523 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-27 14:06:25,536 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-27 14:06:25,544 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-27 14:33:25,833 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-27 14:33:25,838 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-27 14:33:25,844 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-27 15:00:26,048 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-27 15:00:26,051 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-27 15:00:26,054 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-27 15:27:26,177 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-27 15:27:26,182 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-27 15:27:26,183 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-27 15:54:26,465 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-27 15:54:26,550 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-27 15:54:26,568 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-27 16:44:00,297 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-27 16:44:00,343 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-27 16:44:00,496 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-27 16:51:02,235 Worker ******************************** [PID 4406]: warm shut down requested
2025-05-27 16:51:15,196 Worker rq:worker:******************************** started with PID 285196, version 1.15.1
2025-05-27 16:51:15,196 Subscribing to channel rq:pubsub:********************************
2025-05-27 16:51:15,220 *** Listening on home-newsmart-frappe-bench:short, home-newsmart-frappe-bench:default, home-newsmart-frappe-bench:long...
2025-05-27 16:51:15,221 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-27 16:51:15,224 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-27 16:51:15,227 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-28 14:22:19,636 Worker rq:worker:******************************** started with PID 3547, version 1.15.1
2025-05-28 14:22:19,646 Subscribing to channel rq:pubsub:********************************
2025-05-28 14:22:19,653 *** Listening on home-newsmart-frappe-bench:short, home-newsmart-frappe-bench:default, home-newsmart-frappe-bench:long...
2025-05-28 14:22:19,656 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-28 14:22:19,660 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-28 14:22:19,665 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-28 14:31:05,167 Worker ******************************** [PID 3547]: warm shut down requested
2025-05-28 14:31:05,179 Unsubscribing from channel rq:pubsub:********************************
2025-05-28 14:31:10,645 Worker rq:worker:******************************** started with PID 16277, version 1.15.1
2025-05-28 14:31:10,646 Subscribing to channel rq:pubsub:********************************
2025-05-28 14:31:10,656 *** Listening on home-newsmart-frappe-bench:short, home-newsmart-frappe-bench:default, home-newsmart-frappe-bench:long...
2025-05-28 14:31:10,684 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-28 14:31:10,715 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-28 14:31:10,729 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-28 14:54:59,383 Worker ******************************** [PID 16277]: warm shut down requested
2025-05-28 14:55:14,189 Worker rq:worker:******************************** started with PID 51061, version 1.15.1
2025-05-28 14:55:14,189 Subscribing to channel rq:pubsub:********************************
2025-05-28 14:55:14,191 *** Listening on home-newsmart-frappe-bench:short, home-newsmart-frappe-bench:default, home-newsmart-frappe-bench:long...
2025-05-28 14:55:14,192 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-28 14:55:14,194 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-28 14:55:14,195 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-28 14:57:22,123 Worker ******************************** [PID 51061]: warm shut down requested
2025-05-28 14:57:26,655 Worker rq:worker:******************************** started with PID 53845, version 1.15.1
2025-05-28 14:57:26,659 Subscribing to channel rq:pubsub:********************************
2025-05-28 14:57:26,662 *** Listening on home-newsmart-frappe-bench:short, home-newsmart-frappe-bench:default, home-newsmart-frappe-bench:long...
2025-05-28 14:57:26,663 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-28 14:57:26,664 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-28 14:57:26,666 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-28 15:24:27,029 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-28 15:24:27,620 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-28 15:24:27,657 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-28 15:51:28,289 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-28 15:51:28,307 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-28 15:51:28,313 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-28 16:30:22,829 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-28 16:30:23,056 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-28 16:30:23,262 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-31 13:18:47,471 Worker rq:worker:******************************** started with PID 3402, version 1.15.1
2025-05-31 13:18:47,476 Subscribing to channel rq:pubsub:********************************
2025-05-31 13:18:47,484 *** Listening on home-newsmart-frappe-bench:short, home-newsmart-frappe-bench:default, home-newsmart-frappe-bench:long...
2025-05-31 13:18:47,493 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-31 13:18:47,499 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-31 13:18:47,504 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-31 13:37:40,531 Worker rq:worker:******************************** started with PID 2067, version 1.15.1
2025-05-31 13:37:40,534 Subscribing to channel rq:pubsub:********************************
2025-05-31 13:37:40,551 *** Listening on home-newsmart-frappe-bench:short, home-newsmart-frappe-bench:default, home-newsmart-frappe-bench:long...
2025-05-31 13:37:40,582 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-31 13:37:40,595 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-31 13:37:40,602 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-31 14:04:41,578 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-31 14:04:42,751 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-31 14:04:42,779 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-31 14:04:59,452 home-newsmart-frappe-bench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.search.website_search.build_index_for_all_routes', kwargs={}, method=<function build_index_for_all_routes at 0x7f6af6b2add0>, site='site1.local', user='Administrator') (site1.local::3ea4acbf-dbaf-4f48-831b-a5970101ef62)

Retrieving Routes                   : [=                                       ] 2%
Retrieving Routes                   : [==                                      ] 5%
Retrieving Routes                   : [===                                     ] 8%
Retrieving Routes                   : [====                                    ] 10%
Retrieving Routes                   : [=====                                   ] 13%
Retrieving Routes                   : [======                                  ] 16%
Retrieving Routes                   : [=======                                 ] 18%
Retrieving Routes                   : [========                                ] 21%
Retrieving Routes                   : [=========                               ] 24%
Retrieving Routes                   : [==========                              ] 27%
Retrieving Routes                   : [===========                             ] 29%
Retrieving Routes                   : [============                            ] 32%
Retrieving Routes                   : [==============                          ] 35%
Retrieving Routes                   : [===============                         ] 37%
Retrieving Routes                   : [================                        ] 40%
Retrieving Routes                   : [=================                       ] 43%
Retrieving Routes                   : [==================                      ] 45%
Retrieving Routes                   : [===================                     ] 48%
Retrieving Routes                   : [====================                    ] 51%
Retrieving Routes                   : [=====================                   ] 54%
Retrieving Routes                   : [======================                  ] 56%
Retrieving Routes                   : [=======================                 ] 59%
Retrieving Routes                   : [========================                ] 62%
Retrieving Routes                   : [=========================               ] 64%
Retrieving Routes                   : [===========================             ] 67%
Retrieving Routes                   : [============================            ] 70%
Retrieving Routes                   : [=============================           ] 72%
Retrieving Routes                   : [==============================          ] 75%
Retrieving Routes                   : [===============================         ] 78%
Retrieving Routes                   : [================================        ] 81%
Retrieving Routes                   : [=================================       ] 83%
Retrieving Routes                   : [==================================      ] 86%
Retrieving Routes                   : [===================================     ] 89%
Retrieving Routes                   : [====================================    ] 91%
Retrieving Routes                   : [=====================================   ] 94%
Retrieving Routes                   : [======================================  ] 97%
Retrieving Routes                   : [========================================] 100%

Building Index                      : [=                                       ] 2%
Building Index                      : [==                                      ] 5%
Building Index                      : [===                                     ] 8%
Building Index                      : [====                                    ] 10%
Building Index                      : [=====                                   ] 13%
Building Index                      : [======                                  ] 16%
Building Index                      : [=======                                 ] 18%
Building Index                      : [========                                ] 21%
Building Index                      : [=========                               ] 24%
Building Index                      : [==========                              ] 27%
Building Index                      : [===========                             ] 29%
Building Index                      : [============                            ] 32%
Building Index                      : [==============                          ] 35%
Building Index                      : [===============                         ] 37%
Building Index                      : [================                        ] 40%
Building Index                      : [=================                       ] 43%
Building Index                      : [==================                      ] 45%
Building Index                      : [===================                     ] 48%
Building Index                      : [====================                    ] 51%
Building Index                      : [=====================                   ] 54%
Building Index                      : [======================                  ] 56%
Building Index                      : [=======================                 ] 59%
Building Index                      : [========================                ] 62%
Building Index                      : [=========================               ] 64%
Building Index                      : [===========================             ] 67%
Building Index                      : [============================            ] 70%
Building Index                      : [=============================           ] 72%
Building Index                      : [==============================          ] 75%
Building Index                      : [===============================         ] 78%
Building Index                      : [================================        ] 81%
Building Index                      : [=================================       ] 83%
Building Index                      : [==================================      ] 86%
Building Index                      : [===================================     ] 89%
Building Index                      : [====================================    ] 91%
Building Index                      : [=====================================   ] 94%
Building Index                      : [======================================  ] 97%
Building Index                      : [========================================] 100%2025-05-31 14:08:24,642 home-newsmart-frappe-bench:long: Job OK (site1.local::3ea4acbf-dbaf-4f48-831b-a5970101ef62)
2025-05-31 14:08:24,642 Result is kept for 600 seconds
2025-05-31 14:28:40,092 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-31 14:28:40,172 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-31 14:28:40,183 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-31 14:35:33,744 Worker ******************************** [PID 2067]: warm shut down requested
2025-05-31 14:35:49,302 Worker rq:worker:******************************** started with PID 79335, version 1.15.1
2025-05-31 14:35:49,302 Subscribing to channel rq:pubsub:********************************
2025-05-31 14:35:49,307 *** Listening on home-newsmart-frappe-bench:short, home-newsmart-frappe-bench:default, home-newsmart-frappe-bench:long...
2025-05-31 14:35:49,309 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-31 14:35:49,317 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-31 14:35:49,320 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-31 14:38:07,016 Worker ******************************** [PID 79335]: warm shut down requested
2025-05-31 14:38:36,692 Worker rq:worker:******************************** started with PID 82572, version 1.15.1
2025-05-31 14:38:36,692 Subscribing to channel rq:pubsub:********************************
2025-05-31 14:38:36,694 *** Listening on home-newsmart-frappe-bench:short, home-newsmart-frappe-bench:default, home-newsmart-frappe-bench:long...
2025-05-31 14:38:36,695 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-31 14:38:36,696 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-31 14:38:36,697 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-31 14:40:03,520 Worker ******************************** [PID 82572]: warm shut down requested
2025-05-31 14:41:16,245 Worker rq:worker:******************************** started with PID 86817, version 1.15.1
2025-05-31 14:41:16,246 Subscribing to channel rq:pubsub:********************************
2025-05-31 14:41:16,259 *** Listening on home-newsmart-frappe-bench:short, home-newsmart-frappe-bench:default, home-newsmart-frappe-bench:long...
2025-05-31 14:41:16,261 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-31 14:41:16,265 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-31 14:41:16,272 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-31 14:46:24,704 Worker ******************************** [PID 86817]: warm shut down requested
2025-05-31 14:49:24,043 Worker rq:worker:******************************** started with PID 97824, version 1.15.1
2025-05-31 14:49:24,045 Subscribing to channel rq:pubsub:********************************
2025-05-31 14:49:24,058 *** Listening on home-newsmart-frappe-bench:short, home-newsmart-frappe-bench:default, home-newsmart-frappe-bench:long...
2025-05-31 14:49:24,063 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-31 14:49:24,090 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-31 14:49:24,113 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-31 14:52:55,182 Worker rq:worker:******************************** started with PID 3118, version 1.15.1
2025-05-31 14:52:55,189 Subscribing to channel rq:pubsub:********************************
2025-05-31 14:52:55,239 *** Listening on home-newsmart-frappe-bench:short, home-newsmart-frappe-bench:default, home-newsmart-frappe-bench:long...
2025-05-31 14:52:55,243 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-31 14:52:55,302 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-31 14:52:55,327 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-31 15:19:56,434 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-31 15:19:56,927 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-31 15:19:56,933 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-31 15:46:57,397 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-31 15:46:57,455 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-31 15:46:57,457 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-31 16:34:16,055 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-31 16:34:16,079 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-31 16:34:16,102 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-05-31 16:59:26,606 Worker ******************************** [PID 3118]: warm shut down requested
2025-05-31 16:59:39,928 Worker rq:worker:******************************** started with PID 142615, version 1.15.1
2025-05-31 16:59:39,928 Subscribing to channel rq:pubsub:********************************
2025-05-31 16:59:39,931 *** Listening on home-newsmart-frappe-bench:short, home-newsmart-frappe-bench:default, home-newsmart-frappe-bench:long...
2025-05-31 16:59:39,932 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-05-31 16:59:39,934 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-05-31 16:59:39,935 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-06-01 12:37:59,726 Worker rq:worker:******************************** started with PID 3490, version 1.15.1
2025-06-01 12:37:59,737 Subscribing to channel rq:pubsub:********************************
2025-06-01 12:37:59,755 *** Listening on home-newsmart-frappe-bench:short, home-newsmart-frappe-bench:default, home-newsmart-frappe-bench:long...
2025-06-01 12:37:59,773 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-06-01 12:37:59,818 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-06-01 12:37:59,880 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-06-01 13:05:00,344 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-06-01 13:05:01,467 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-06-01 13:05:01,483 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-06-01 13:32:01,847 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-06-01 13:32:01,853 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-06-01 13:32:01,855 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-06-01 13:59:02,499 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-06-01 13:59:02,734 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-06-01 13:59:02,741 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-06-01 14:22:45,398 home-newsmart-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.user.user.create_contact', kwargs={'user': <User: <EMAIL>>, 'ignore_mandatory': True}, method='frappe.core.doctype.user.user.create_contact', site='site1.local', user='Administrator') (site1.local::69ef06bd-52c6-4542-b318-58a27e3d5b14)
2025-06-01 14:22:55,642 home-newsmart-frappe-bench:default: Job OK (site1.local::69ef06bd-52c6-4542-b318-58a27e3d5b14)
2025-06-01 14:22:55,642 Result is kept for 600 seconds
2025-06-01 14:22:55,664 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-06-01 14:22:55,671 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-06-01 14:22:55,676 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-06-01 14:22:55,685 home-newsmart-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.user.user.update_gravatar', kwargs={'name': '<EMAIL>'}, method='frappe.core.doctype.user.user.update_gravatar', site='site1.local', user='Administrator') (site1.local::398c39b4-fb3e-434c-932c-999d5e67ef41)
2025-06-01 14:22:56,959 home-newsmart-frappe-bench:default: Job OK (site1.local::398c39b4-fb3e-434c-932c-999d5e67ef41)
2025-06-01 14:22:56,959 Result is kept for 600 seconds
2025-06-01 14:23:01,751 home-newsmart-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.user.user.create_contact', kwargs={'user': <User: <EMAIL>>, 'ignore_mandatory': True}, method='frappe.core.doctype.user.user.create_contact', site='site1.local', user='Administrator') (site1.local::38a102c9-4cef-4688-911d-5d208638939e)
2025-06-01 14:23:04,010 home-newsmart-frappe-bench:default: Job OK (site1.local::38a102c9-4cef-4688-911d-5d208638939e)
2025-06-01 14:23:04,010 Result is kept for 600 seconds
2025-06-01 14:23:04,042 home-newsmart-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.user.user.update_gravatar', kwargs={'name': '<EMAIL>'}, method='frappe.core.doctype.user.user.update_gravatar', site='site1.local', user='Administrator') (site1.local::dd93f467-9da4-45bb-ae29-94894a1ab923)
2025-06-01 14:23:05,328 home-newsmart-frappe-bench:default: Job OK (site1.local::dd93f467-9da4-45bb-ae29-94894a1ab923)
2025-06-01 14:23:05,330 Result is kept for 600 seconds
2025-06-01 14:23:13,060 home-newsmart-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.user.user.create_contact', kwargs={'user': <User: <EMAIL>>, 'ignore_mandatory': True}, method='frappe.core.doctype.user.user.create_contact', site='site1.local', user='Administrator') (site1.local::d7d086e0-2849-4179-bd59-a174689c1fb2)
2025-06-01 14:23:16,796 home-newsmart-frappe-bench:default: Job OK (site1.local::d7d086e0-2849-4179-bd59-a174689c1fb2)
2025-06-01 14:23:16,796 Result is kept for 600 seconds
2025-06-01 14:23:16,820 home-newsmart-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.user.user.update_gravatar', kwargs={'name': '<EMAIL>'}, method='frappe.core.doctype.user.user.update_gravatar', site='site1.local', user='Administrator') (site1.local::3eedaa4a-bdff-4b30-b3bb-3306077bde0e)
2025-06-01 14:23:18,350 home-newsmart-frappe-bench:default: Job OK (site1.local::3eedaa4a-bdff-4b30-b3bb-3306077bde0e)
2025-06-01 14:23:18,351 Result is kept for 600 seconds
2025-06-01 14:50:18,614 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-06-01 14:50:18,618 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-06-01 14:50:18,622 Cleaning registries for queue: home-newsmart-frappe-bench:long
2025-06-01 15:17:18,822 Cleaning registries for queue: home-newsmart-frappe-bench:short
2025-06-01 15:17:18,837 Cleaning registries for queue: home-newsmart-frappe-bench:default
2025-06-01 15:17:18,841 Cleaning registries for queue: home-newsmart-frappe-bench:long
