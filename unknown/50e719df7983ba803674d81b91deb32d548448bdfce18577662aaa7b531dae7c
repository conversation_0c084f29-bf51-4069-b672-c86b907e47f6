{"actions": [], "allow_rename": 1, "autoname": "field:group_name", "creation": "2023-05-21 14:25:00.000000", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["group_name", "description", "sender_account", "group_id", "members_section", "members", "sync_section", "last_synced", "sync_group"], "fields": [{"fieldname": "group_name", "fieldtype": "Data", "in_list_view": 1, "label": "Group Name", "reqd": 1, "unique": 1}, {"fieldname": "description", "fieldtype": "Small Text", "label": "Description"}, {"fieldname": "sender_account", "fieldtype": "Link", "in_list_view": 1, "label": "Sender Account", "options": "WhatsA<PERSON> Sender Account", "reqd": 1}, {"fieldname": "group_id", "fieldtype": "Data", "label": "Group ID", "read_only": 1}, {"fieldname": "members_section", "fieldtype": "Section Break", "label": "Members"}, {"fieldname": "members", "fieldtype": "Table", "label": "Members", "options": "WhatsApp Group Member"}, {"fieldname": "sync_section", "fieldtype": "Section Break", "label": "Synchronization"}, {"fieldname": "last_synced", "fieldtype": "Datetime", "label": "Last Synced", "read_only": 1}, {"fieldname": "sync_group", "fieldtype": "<PERSON><PERSON>", "label": "Sync Group"}], "index_web_pages_for_search": 1, "links": [], "modified": "2023-05-21 14:25:00.000000", "modified_by": "Administrator", "module": "WhatsApp", "name": "WhatsApp Group", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "All", "share": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}