/**
 * Test WhatsApp Integration
 */

console.log('🚀 تحميل اختبار WhatsApp...');

// اختبار بسيط لفاتورة المبيعات
frappe.ui.form.on('Sales Invoice', {
    refresh: function(frm) {
        console.log('🔄 تحديث فاتورة المبيعات:', frm.doc.name);
        
        if (frm.doc.name && !frm.doc.__islocal) {
            console.log('✅ إضافة زر WhatsApp للفاتورة');
            
            // إضافة زر بسيط
            frm.add_custom_button('📱 WhatsApp', function() {
                frappe.msgprint('زر WhatsApp يعمل! 🎉');
                console.log('✅ زر WhatsApp تم النقر عليه');
            }).addClass('btn-success');
            
            // إضافة زر إرسال سريع
            frm.add_custom_button('📤 إرسال سريع', function() {
                test_send_whatsapp(frm);
            }).addClass('btn-primary');
        }
    }
});

function test_send_whatsapp(frm) {
    console.log('🧪 اختبار إرسال WhatsApp...');
    
    frappe.call({
        method: 'whatsapp.whatsapp.api.get_default_sender_account',
        callback: function(r) {
            console.log('📱 حساب WhatsApp الافتراضي:', r.message);
            
            if (r.message) {
                frappe.show_alert({
                    message: `حساب WhatsApp الافتراضي: ${r.message}`,
                    indicator: 'green'
                });
            } else {
                frappe.show_alert({
                    message: 'لم يتم العثور على حساب WhatsApp',
                    indicator: 'orange'
                });
            }
        }
    });
}

// اختبار عام لجميع المستندات
$(document).ready(function() {
    console.log('📄 تحميل اختبار WhatsApp للمستندات...');
    
    // إضافة زر اختبار في الشريط العلوي
    setTimeout(function() {
        if (window.cur_frm && cur_frm.doc.name && !cur_frm.doc.__islocal) {
            console.log('🔧 إضافة زر اختبار WhatsApp');
            
            cur_frm.add_custom_button('🧪 اختبار WhatsApp', function() {
                frappe.msgprint({
                    title: 'اختبار WhatsApp',
                    message: `
                        <div style="text-align: center;">
                            <h4>✅ WhatsApp يعمل بنجاح!</h4>
                            <p>المستند: ${cur_frm.doctype}</p>
                            <p>الرقم: ${cur_frm.doc.name}</p>
                        </div>
                    `,
                    indicator: 'green'
                });
            }).addClass('btn-warning');
        }
    }, 2000);
});

console.log('✅ تم تحميل اختبار WhatsApp بنجاح');
