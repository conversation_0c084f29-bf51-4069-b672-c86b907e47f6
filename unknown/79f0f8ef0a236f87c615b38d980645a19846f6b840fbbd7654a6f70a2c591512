/**
 * WhatsApp Document Sender
 * إضافة أزرار إرسال المستندات عبر WhatsApp للفواتير والمستندات المختلفة
 */

console.log('🚀 تحميل WhatsApp Document Sender...');

// قائمة المستندات المدعومة
const SUPPORTED_DOCTYPES = [
    'Sales Invoice',
    'Purchase Invoice',
    'Quotation',
    'Sales Order',
    'Purchase Order',
    'Delivery Note',
    'Purchase Receipt',
    'Payment Entry',
    'Journal Entry'
];

// إضافة أزرار WhatsApp لجميع المستندات المدعومة
SUPPORTED_DOCTYPES.forEach(doctype => {
    console.log(`📱 إضافة أزرار WhatsApp لـ ${doctype}`);
    frappe.ui.form.on(doctype, {
        refresh: function(frm) {
            console.log(`🔄 تحديث ${doctype}: ${frm.doc.name}`);
            add_whatsapp_buttons(frm);
        }
    });
});

function add_whatsapp_buttons(frm) {
    // التأكد من أن المستند محفوظ
    if (!frm.doc.name || frm.doc.__islocal) {
        console.log('⚠️ المستند غير محفوظ، لن يتم إضافة أزرار WhatsApp');
        return;
    }

    console.log(`✅ إضافة أزرار WhatsApp للمستند: ${frm.doc.name}`);

    // إضافة زر إرسال عبر WhatsApp في المكان الصحيح
    frm.add_custom_button(__('📱 إرسال عبر WhatsApp'), function() {
        show_whatsapp_dialog(frm);
    }).addClass('btn-primary');

    // إضافة زر إرسال PDF سريع
    frm.add_custom_button(__('📄 إرسال PDF'), function() {
        send_document_pdf_quick(frm);
    });

    // إضافة زر إرسال رابط
    frm.add_custom_button(__('🔗 إرسال رابط'), function() {
        send_document_link_quick(frm);
    });
}

function show_whatsapp_dialog(frm) {
    // إنشاء نافذة حوار لاختيار تفاصيل الإرسال
    let dialog = new frappe.ui.Dialog({
        title: __('إرسال {0} عبر WhatsApp', [__(frm.doctype)]),
        fields: [
            {
                fieldtype: 'Section Break',
                label: __('تفاصيل المرسل')
            },
            {
                fieldname: 'sender_account',
                fieldtype: 'Link',
                label: __('حساب WhatsApp'),
                options: 'WhatsApp Sender Account',
                reqd: 1,
                get_query: function() {
                    return {
                        filters: {
                            'status': 'Connected'
                        }
                    };
                }
            },
            {
                fieldtype: 'Section Break',
                label: __('تفاصيل المستقبل')
            },
            {
                fieldname: 'recipient_type',
                fieldtype: 'Select',
                label: __('نوع المستقبل'),
                options: 'رقم هاتف\nعميل\nمورد\nجهة اتصال',
                default: 'رقم هاتف',
                reqd: 1,
                onchange: function() {
                    update_recipient_field(dialog);
                }
            },
            {
                fieldname: 'phone_number',
                fieldtype: 'Data',
                label: __('رقم الهاتف'),
                reqd: 1,
                description: __('مثال: +967xxxxxxxxx')
            },
            {
                fieldname: 'customer',
                fieldtype: 'Link',
                options: 'Customer',
                label: __('العميل'),
                hidden: 1
            },
            {
                fieldname: 'supplier',
                fieldtype: 'Link',
                options: 'Supplier',
                label: __('المورد'),
                hidden: 1
            },
            {
                fieldname: 'contact',
                fieldtype: 'Link',
                options: 'Contact',
                label: __('جهة الاتصال'),
                hidden: 1
            },
            {
                fieldtype: 'Section Break',
                label: __('تفاصيل الرسالة')
            },
            {
                fieldname: 'send_type',
                fieldtype: 'Select',
                label: __('نوع الإرسال'),
                options: 'PDF + رسالة\nPDF فقط\nرسالة فقط\nرابط المستند',
                default: 'PDF + رسالة',
                reqd: 1
            },
            {
                fieldname: 'message',
                fieldtype: 'Text',
                label: __('الرسالة'),
                default: get_default_message(frm)
            },
            {
                fieldname: 'print_format',
                fieldtype: 'Link',
                options: 'Print Format',
                label: __('تنسيق الطباعة'),
                get_query: function() {
                    return {
                        filters: {
                            'doc_type': frm.doctype
                        }
                    };
                }
            }
        ],
        primary_action_label: __('إرسال'),
        primary_action: function(values) {
            send_whatsapp_document(frm, values);
            dialog.hide();
        }
    });

    // تحديد القيم الافتراضية بناءً على المستند
    set_default_values(frm, dialog);

    dialog.show();
}

function update_recipient_field(dialog) {
    let recipient_type = dialog.get_value('recipient_type');

    // إخفاء جميع الحقول
    dialog.set_df_property('phone_number', 'hidden', 1);
    dialog.set_df_property('customer', 'hidden', 1);
    dialog.set_df_property('supplier', 'hidden', 1);
    dialog.set_df_property('contact', 'hidden', 1);

    // إظهار الحقل المناسب
    switch(recipient_type) {
        case 'رقم هاتف':
            dialog.set_df_property('phone_number', 'hidden', 0);
            break;
        case 'عميل':
            dialog.set_df_property('customer', 'hidden', 0);
            break;
        case 'مورد':
            dialog.set_df_property('supplier', 'hidden', 0);
            break;
        case 'جهة اتصال':
            dialog.set_df_property('contact', 'hidden', 0);
            break;
    }
}

function set_default_values(frm, dialog) {
    // تحديد حساب WhatsApp الافتراضي
    frappe.call({
        method: 'whatsapp.whatsapp.api.get_default_sender_account',
        callback: function(r) {
            if (r.message) {
                dialog.set_value('sender_account', r.message);
            }
        }
    });

    // تحديد المستقبل بناءً على نوع المستند
    if (frm.doc.customer) {
        dialog.set_value('recipient_type', 'عميل');
        dialog.set_value('customer', frm.doc.customer);
        update_recipient_field(dialog);
    } else if (frm.doc.supplier) {
        dialog.set_value('recipient_type', 'مورد');
        dialog.set_value('supplier', frm.doc.supplier);
        update_recipient_field(dialog);
    }
}

function get_default_message(frm) {
    let doctype_ar = {
        'Sales Invoice': 'فاتورة مبيعات',
        'Purchase Invoice': 'فاتورة مشتريات',
        'Quotation': 'عرض سعر',
        'Sales Order': 'أمر مبيعات',
        'Purchase Order': 'أمر شراء',
        'Delivery Note': 'إشعار تسليم',
        'Purchase Receipt': 'إيصال شراء',
        'Payment Entry': 'قيد دفع',
        'Journal Entry': 'قيد يومية'
    };

    let doc_name_ar = doctype_ar[frm.doctype] || frm.doctype;

    return `السلام عليكم ورحمة الله وبركاته

نرسل لكم ${doc_name_ar} رقم: ${frm.doc.name}

${frm.doc.customer ? 'العميل: ' + frm.doc.customer : ''}
${frm.doc.supplier ? 'المورد: ' + frm.doc.supplier : ''}
${frm.doc.grand_total ? 'المبلغ الإجمالي: ' + format_currency(frm.doc.grand_total, frm.doc.currency) : ''}

شكراً لكم
فريق ${frappe.boot.sitename}`;
}

function send_whatsapp_document(frm, values) {
    frappe.show_progress(__('جاري الإرسال...'), 0, 100, __('جاري تحضير المستند'));

    frappe.call({
        method: 'whatsapp.whatsapp.api.send_document_via_whatsapp',
        args: {
            doctype: frm.doctype,
            docname: frm.doc.name,
            sender_account: values.sender_account,
            recipient_type: values.recipient_type,
            phone_number: values.phone_number,
            customer: values.customer,
            supplier: values.supplier,
            contact: values.contact,
            send_type: values.send_type,
            message: values.message,
            print_format: values.print_format
        },
        callback: function(r) {
            frappe.hide_progress();
            if (r.message && r.message.success) {
                frappe.show_alert({
                    message: __('تم إرسال المستند بنجاح عبر WhatsApp'),
                    indicator: 'green'
                });

                // إضافة تعليق في المستند
                frm.add_comment('Comment', __('تم إرسال المستند عبر WhatsApp إلى {0}', [r.message.recipient]));
            } else {
                frappe.msgprint({
                    title: __('خطأ في الإرسال'),
                    message: r.message ? r.message.error : __('حدث خطأ غير متوقع'),
                    indicator: 'red'
                });
            }
        },
        error: function(r) {
            frappe.hide_progress();
            frappe.msgprint({
                title: __('خطأ في الإرسال'),
                message: __('فشل في إرسال المستند. يرجى المحاولة مرة أخرى.'),
                indicator: 'red'
            });
        }
    });
}

function send_document_pdf_quick(frm) {
    // إرسال PDF مباشرة بدون حوار
    frappe.show_progress(__('جاري إرسال PDF...'), 50, 100);

    frappe.call({
        method: 'whatsapp.whatsapp.api.send_document_via_whatsapp',
        args: {
            doctype: frm.doctype,
            docname: frm.doc.name,
            sender_account: null, // سيتم اختيار الافتراضي
            recipient_type: 'عميل',
            customer: frm.doc.customer,
            supplier: frm.doc.supplier,
            send_type: 'PDF فقط',
            message: `📄 ${frm.doctype} رقم: ${frm.doc.name}`
        },
        callback: function(r) {
            frappe.hide_progress();
            if (r.message && r.message.success) {
                frappe.show_alert({
                    message: __('تم إرسال PDF بنجاح إلى {0}', [r.message.recipient]),
                    indicator: 'green'
                });
                frm.add_comment('Comment', __('تم إرسال PDF عبر WhatsApp'));
            } else {
                frappe.msgprint({
                    title: __('خطأ في الإرسال'),
                    message: r.message ? r.message.error : __('فشل في إرسال PDF'),
                    indicator: 'red'
                });
            }
        },
        error: function() {
            frappe.hide_progress();
            frappe.msgprint(__('حدث خطأ في الإرسال'));
        }
    });
}

function send_document_link_quick(frm) {
    // إرسال رابط المستند
    frappe.show_progress(__('جاري إرسال الرابط...'), 50, 100);

    let doc_url = window.location.origin + '/app/' + frm.doctype.toLowerCase().replace(' ', '-') + '/' + frm.doc.name;
    let message = `🔗 رابط ${frm.doctype} رقم: ${frm.doc.name}\n\n${doc_url}`;

    frappe.call({
        method: 'whatsapp.whatsapp.api.send_document_via_whatsapp',
        args: {
            doctype: frm.doctype,
            docname: frm.doc.name,
            sender_account: null, // سيتم اختيار الافتراضي
            recipient_type: 'عميل',
            customer: frm.doc.customer,
            supplier: frm.doc.supplier,
            send_type: 'رسالة فقط',
            message: message
        },
        callback: function(r) {
            frappe.hide_progress();
            if (r.message && r.message.success) {
                frappe.show_alert({
                    message: __('تم إرسال الرابط بنجاح إلى {0}', [r.message.recipient]),
                    indicator: 'green'
                });
                frm.add_comment('Comment', __('تم إرسال رابط المستند عبر WhatsApp'));
            } else {
                frappe.msgprint({
                    title: __('خطأ في الإرسال'),
                    message: r.message ? r.message.error : __('فشل في إرسال الرابط'),
                    indicator: 'red'
                });
            }
        },
        error: function() {
            frappe.hide_progress();
            frappe.msgprint(__('حدث خطأ في الإرسال'));
        }
    });
}
