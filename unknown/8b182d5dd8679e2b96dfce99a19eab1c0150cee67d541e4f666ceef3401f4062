# Copyright (c) 2023, moneer and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document
import json
from frappe import _

class WhatsAppTriggerSettings(Document):
    def validate(self):
        # Validate condition
        if self.condition:
            try:
                # Try to compile the condition to check for syntax errors
                compile(self.condition, '<string>', 'eval')
            except Exception as e:
                frappe.throw(_("Invalid condition: {0}").format(str(e)))
        
        # Validate recipient field
        if self.recipient_field:
            # Check if the field exists in the doctype
            meta = frappe.get_meta(self.reference_doctype)
            if not meta.has_field(self.recipient_field):
                frappe.throw(_("Field {0} does not exist in {1}").format(
                    self.recipient_field, self.reference_doctype))
        
        # Validate attachment field if attachments are included
        if self.include_attachments and self.attachment_field:
            # Check if the field exists in the doctype
            meta = frappe.get_meta(self.reference_doctype)
            if not meta.has_field(self.attachment_field):
                frappe.throw(_("Field {0} does not exist in {1}").format(
                    self.attachment_field, self.reference_doctype))
    
    def process_document(self, doc):
        """Process a document and send WhatsApp message if conditions are met"""
        if not self.enabled:
            return
            
        # Check condition
        if self.condition:
            try:
                condition_met = eval(self.condition, {'doc': doc, 'frappe': frappe})
                if not condition_met:
                    return
            except Exception as e:
                frappe.log_error(_("Error evaluating condition for WhatsApp trigger {0}: {1}").format(
                    self.name, str(e)), "WhatsApp Trigger Error")
                return
        
        # Get recipient
        recipient = None
        if self.recipient_field:
            recipient = getattr(doc, self.recipient_field, None)
            if not recipient:
                frappe.log_error(_("Recipient field {0} is empty in document {1}").format(
                    self.recipient_field, doc.name), "WhatsApp Trigger Error")
                return
        
        # Get sender account
        sender_account = frappe.get_doc("WhatsApp Sender Account", self.sender_account)
        
        # Get message template
        template = frappe.get_doc("WhatsApp Message Template", self.message_template)
        
        # Send message
        try:
            log_name = template.send_message(doc, sender_account, recipient_field=self.recipient_field, recipient=recipient)
            
            # Add attachments if needed
            if self.include_attachments and log_name:
                log = frappe.get_doc("WhatsApp Message Log", log_name)
                
                # Handle attachments based on type
                if self.attachment_type == "PDF":
                    # Generate PDF and attach
                    self.attach_pdf(doc, log)
                elif self.attachment_field:
                    # Attach file from field
                    attachment = getattr(doc, self.attachment_field, None)
                    if attachment:
                        log.add_attachment(attachment)
                        
                log.save()
                
            return log_name
        except Exception as e:
            frappe.log_error(_("Error sending WhatsApp message for trigger {0}: {1}").format(
                self.name, str(e)), "WhatsApp Trigger Error")
            return None
    
    def attach_pdf(self, doc, log):
        """Generate PDF for document and attach to message log"""
        from frappe.utils.pdf import get_pdf
        from frappe.utils import get_url
        
        try:
            # Generate PDF
            html = frappe.get_print(doc.doctype, doc.name)
            pdf_data = get_pdf(html)
            
            # Save PDF as attachment
            filename = "{0}.pdf".format(doc.name.replace(" ", "-").replace("/", "-"))
            file_url = save_file(filename, pdf_data, doc.doctype, doc.name)
            
            # Add attachment to message log
            log.add_attachment(file_url, filename, "application/pdf")
            
        except Exception as e:
            frappe.log_error(_("Error generating PDF for document {0}: {1}").format(
                doc.name, str(e)), "WhatsApp PDF Error")


def save_file(fname, content, dt, dn):
    """Save file to disk and create File document"""
    from frappe.utils.file_manager import save_file
    return save_file(fname, content, dt, dn, is_private=1).file_url
