{"actions": [], "allow_rename": 1, "autoname": "field:account_name", "creation": "2023-05-21 14:00:00.000000", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["account_name", "phone_number", "department", "status_section", "status", "qr_code_html", "last_connected", "settings_section", "auto_reply_message", "default_template"], "fields": [{"fieldname": "account_name", "fieldtype": "Data", "in_list_view": 1, "label": "Account Name", "reqd": 1, "unique": 1}, {"fieldname": "phone_number", "fieldtype": "Data", "in_list_view": 1, "label": "Phone Number", "reqd": 1}, {"fieldname": "department", "fieldtype": "Link", "label": "Department", "options": "Department"}, {"fieldname": "status_section", "fieldtype": "Section Break", "label": "Status"}, {"default": "Disconnected", "fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "label": "Status", "options": "Connected\nDisconnected\nPending", "read_only": 1}, {"fieldname": "qr_code_html", "fieldtype": "HTML", "label": "QR Code"}, {"fieldname": "last_connected", "fieldtype": "Datetime", "label": "Last Connected", "read_only": 1}, {"fieldname": "settings_section", "fieldtype": "Section Break", "label": "Settings"}, {"fieldname": "auto_reply_message", "fieldtype": "Small Text", "label": "Auto Reply Message"}, {"fieldname": "default_template", "fieldtype": "Link", "label": "<PERSON><PERSON><PERSON>", "options": "WhatsApp Message Template"}], "index_web_pages_for_search": 1, "links": [], "modified": "2023-05-21 14:00:00.000000", "modified_by": "Administrator", "module": "WhatsApp", "name": "WhatsA<PERSON> Sender Account", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "All", "share": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}