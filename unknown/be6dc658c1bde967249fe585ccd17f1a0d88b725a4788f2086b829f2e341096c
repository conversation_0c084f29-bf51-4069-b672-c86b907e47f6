/**
 * WhatsApp Integration for Sales Invoice
 */

frappe.ui.form.on('Sales Invoice', {
    refresh: function(frm) {
        console.log('🔄 Sales Invoice تحديث:', frm.doc.name);
        
        // التأكد من أن المستند محفوظ
        if (!frm.doc.name || frm.doc.__islocal) {
            console.log('⚠️ فاتورة المبيعات غير محفوظة');
            return;
        }

        console.log('✅ إضافة أزرار WhatsApp لفاتورة المبيعات');

        // إضافة زر إرسال عبر WhatsApp
        frm.add_custom_button(__('📱 إرسال عبر WhatsApp'), function() {
            show_whatsapp_dialog_sales_invoice(frm);
        }).addClass('btn-primary');

        // إضافة زر إرسال PDF سريع
        frm.add_custom_button(__('📄 إرسال PDF'), function() {
            send_sales_invoice_pdf(frm);
        });

        // إضافة زر إرسال رابط
        frm.add_custom_button(__('🔗 إرسال رابط'), function() {
            send_sales_invoice_link(frm);
        });
    }
});

function show_whatsapp_dialog_sales_invoice(frm) {
    let dialog = new frappe.ui.Dialog({
        title: __('إرسال فاتورة المبيعات عبر WhatsApp'),
        fields: [
            {
                fieldtype: 'Section Break',
                label: __('تفاصيل المرسل')
            },
            {
                fieldname: 'sender_account',
                fieldtype: 'Link',
                label: __('حساب WhatsApp'),
                options: 'WhatsApp Sender Account',
                reqd: 1,
                get_query: function() {
                    return {
                        filters: {
                            'status': 'Connected'
                        }
                    };
                }
            },
            {
                fieldtype: 'Section Break',
                label: __('تفاصيل المستقبل')
            },
            {
                fieldname: 'phone_number',
                fieldtype: 'Data',
                label: __('رقم الهاتف'),
                reqd: 1,
                description: __('مثال: +967xxxxxxxxx'),
                default: frm.doc.customer ? get_customer_phone(frm.doc.customer) : ''
            },
            {
                fieldtype: 'Section Break',
                label: __('تفاصيل الرسالة')
            },
            {
                fieldname: 'send_type',
                fieldtype: 'Select',
                label: __('نوع الإرسال'),
                options: 'PDF + رسالة\nPDF فقط\nرسالة فقط\nرابط المستند',
                default: 'PDF + رسالة',
                reqd: 1
            },
            {
                fieldname: 'message',
                fieldtype: 'Text',
                label: __('الرسالة'),
                default: get_sales_invoice_message(frm)
            }
        ],
        primary_action_label: __('إرسال'),
        primary_action: function(values) {
            send_sales_invoice_whatsapp(frm, values);
            dialog.hide();
        }
    });

    // تحديد حساب WhatsApp الافتراضي
    frappe.call({
        method: 'whatsapp.whatsapp.api.get_default_sender_account',
        callback: function(r) {
            if (r.message) {
                dialog.set_value('sender_account', r.message);
            }
        }
    });

    dialog.show();
}

function get_customer_phone(customer) {
    // محاولة الحصول على رقم هاتف العميل
    if (!customer) return '';
    
    frappe.call({
        method: 'frappe.client.get_value',
        args: {
            doctype: 'Customer',
            filters: {'name': customer},
            fieldname: 'mobile_no'
        },
        callback: function(r) {
            if (r.message && r.message.mobile_no) {
                return r.message.mobile_no;
            }
        }
    });
    return '';
}

function get_sales_invoice_message(frm) {
    return `السلام عليكم ورحمة الله وبركاته

نرسل لكم فاتورة مبيعات رقم: ${frm.doc.name}

العميل: ${frm.doc.customer || ''}
التاريخ: ${frm.doc.posting_date || ''}
المبلغ الإجمالي: ${format_currency(frm.doc.grand_total || 0, frm.doc.currency || 'YER')}

شكراً لكم
${frappe.boot.sitename || 'فريق المبيعات'}`;
}

function send_sales_invoice_whatsapp(frm, values) {
    frappe.show_progress(__('جاري الإرسال...'), 0, 100);

    frappe.call({
        method: 'whatsapp.whatsapp.api.send_document_via_whatsapp',
        args: {
            doctype: 'Sales Invoice',
            docname: frm.doc.name,
            sender_account: values.sender_account,
            recipient_type: 'رقم هاتف',
            phone_number: values.phone_number,
            send_type: values.send_type,
            message: values.message
        },
        callback: function(r) {
            frappe.hide_progress();
            if (r.message && r.message.success) {
                frappe.show_alert({
                    message: __('تم إرسال الفاتورة بنجاح عبر WhatsApp'),
                    indicator: 'green'
                });
                frm.add_comment('Comment', __('تم إرسال الفاتورة عبر WhatsApp إلى {0}', [values.phone_number]));
            } else {
                frappe.msgprint({
                    title: __('خطأ في الإرسال'),
                    message: r.message ? r.message.error : __('حدث خطأ غير متوقع'),
                    indicator: 'red'
                });
            }
        },
        error: function() {
            frappe.hide_progress();
            frappe.msgprint(__('فشل في إرسال الفاتورة'));
        }
    });
}

function send_sales_invoice_pdf(frm) {
    frappe.show_progress(__('جاري إرسال PDF...'), 50, 100);
    
    frappe.call({
        method: 'whatsapp.whatsapp.api.send_document_via_whatsapp',
        args: {
            doctype: 'Sales Invoice',
            docname: frm.doc.name,
            sender_account: null,
            recipient_type: 'عميل',
            customer: frm.doc.customer,
            send_type: 'PDF فقط',
            message: `📄 فاتورة مبيعات رقم: ${frm.doc.name}`
        },
        callback: function(r) {
            frappe.hide_progress();
            if (r.message && r.message.success) {
                frappe.show_alert({
                    message: __('تم إرسال PDF بنجاح'),
                    indicator: 'green'
                });
            } else {
                frappe.msgprint(__('فشل في إرسال PDF: {0}', [r.message ? r.message.error : 'خطأ غير معروف']));
            }
        }
    });
}

function send_sales_invoice_link(frm) {
    frappe.show_progress(__('جاري إرسال الرابط...'), 50, 100);
    
    let doc_url = window.location.origin + '/app/sales-invoice/' + frm.doc.name;
    let message = `🔗 رابط فاتورة المبيعات رقم: ${frm.doc.name}\n\n${doc_url}`;
    
    frappe.call({
        method: 'whatsapp.whatsapp.api.send_document_via_whatsapp',
        args: {
            doctype: 'Sales Invoice',
            docname: frm.doc.name,
            sender_account: null,
            recipient_type: 'عميل',
            customer: frm.doc.customer,
            send_type: 'رسالة فقط',
            message: message
        },
        callback: function(r) {
            frappe.hide_progress();
            if (r.message && r.message.success) {
                frappe.show_alert({
                    message: __('تم إرسال الرابط بنجاح'),
                    indicator: 'green'
                });
            } else {
                frappe.msgprint(__('فشل في إرسال الرابط: {0}', [r.message ? r.message.error : 'خطأ غير معروف']));
            }
        }
    });
}
