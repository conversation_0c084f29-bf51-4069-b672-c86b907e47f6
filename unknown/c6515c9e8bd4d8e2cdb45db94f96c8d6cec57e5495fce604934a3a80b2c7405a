/* WhatsApp Integration Styles */

/* زر WhatsApp الرئيسي */
.btn-whatsapp-main {
    background: #25D366 !important;
    border-color: #25D366 !important;
    color: white !important;
    font-weight: 500 !important;
    border-radius: 6px !important;
    padding: 8px 16px !important;
    margin-left: 10px !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 4px rgba(37, 211, 102, 0.2) !important;
    position: relative;
    overflow: hidden;
}

.btn-whatsapp-main:hover {
    background: #128C7E !important;
    border-color: #128C7E !important;
    color: white !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(37, 211, 102, 0.3) !important;
}

.btn-whatsapp-main:active {
    transform: translateY(0) !important;
    box-shadow: 0 2px 4px rgba(37, 211, 102, 0.2) !important;
}

.btn-whatsapp-main:focus {
    box-shadow: 0 0 0 3px rgba(37, 211, 102, 0.25) !important;
    outline: none !important;
}

.btn-whatsapp-main .fa-whatsapp {
    margin-left: 5px;
    font-size: 16px;
}

/* تأثير الموجة عند النقر */
.btn-whatsapp-main::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.btn-whatsapp-main:active::before {
    width: 300px;
    height: 300px;
}

/* حاوي زر WhatsApp */
.whatsapp-btn-container {
    margin-bottom: 15px;
    text-align: right;
    padding: 10px 0;
    border-bottom: 1px solid #e9ecef;
}

/* أزرار WhatsApp في القوائم */
.whatsapp-btn-added {
    position: relative;
    overflow: hidden;
}

.whatsapp-btn-added::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.whatsapp-btn-added:hover::before {
    left: 100%;
}

/* أيقونة WhatsApp في شريط التنقل */
.whatsapp-icon {
    position: relative;
    display: inline-block;
}

.whatsapp-icon::after {
    content: '';
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    background: #25D366;
    border-radius: 50%;
    animation: whatsapp-pulse 2s infinite;
}

@keyframes whatsapp-pulse {
    0% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.7);
    }

    70% {
        transform: scale(1);
        box-shadow: 0 0 0 10px rgba(37, 211, 102, 0);
    }

    100% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(37, 211, 102, 0);
    }
}

/* تحسين مظهر نافذة الحوار */
.modal-dialog .whatsapp-dialog {
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

/* تحسين القائمة السريعة لـ WhatsApp */
.whatsapp-quick-menu {
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.whatsapp-quick-menu .modal-content {
    border-radius: 12px;
    border: none;
}

.whatsapp-quick-menu .modal-header {
    background: linear-gradient(135deg, #25D366, #128C7E);
    color: white;
    border-radius: 12px 12px 0 0;
    border: none;
}

.whatsapp-quick-menu .modal-body {
    padding: 0;
}

/* أزرار القائمة السريعة */
.whatsapp-quick-menu .btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.whatsapp-quick-menu .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.whatsapp-quick-menu .quick-detailed:hover {
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
}

.whatsapp-quick-menu .quick-pdf:hover {
    box-shadow: 0 4px 15px rgba(23, 162, 184, 0.4);
}

.whatsapp-quick-menu .quick-link:hover {
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.4);
}

.whatsapp-quick-menu .quick-message:hover {
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.4);
}

.modal-header {
    background: linear-gradient(135deg, #25D366, #128C7E);
    color: white;
    border-radius: 15px 15px 0 0;
}

/* أزرار مخصصة لـ WhatsApp */
.btn-whatsapp {
    background: #25D366;
    border-color: #25D366;
    color: white;
    transition: all 0.3s ease;
}

.btn-whatsapp:hover {
    background: #128C7E;
    border-color: #128C7E;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(37, 211, 102, 0.4);
}

.btn-whatsapp-pdf {
    background: #007bff;
    border-color: #007bff;
    color: white;
}

.btn-whatsapp-pdf:hover {
    background: #0056b3;
    border-color: #0056b3;
    color: white;
    transform: translateY(-2px);
}

.btn-whatsapp-link {
    background: #ffc107;
    border-color: #ffc107;
    color: #212529;
}

.btn-whatsapp-link:hover {
    background: #e0a800;
    border-color: #d39e00;
    color: #212529;
    transform: translateY(-2px);
}

.btn-whatsapp-message {
    background: #6f42c1;
    border-color: #6f42c1;
    color: white;
}

.btn-whatsapp-message:hover {
    background: #5a32a3;
    border-color: #5a32a3;
    color: white;
    transform: translateY(-2px);
}

/* تحسين مظهر الحقول في النافذة */
.frappe-control[data-fieldname="phone_number"] input {
    direction: ltr;
    text-align: left;
}

.frappe-control[data-fieldname="message"] textarea {
    min-height: 120px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* رسائل التنبيه المخصصة */
.alert-whatsapp {
    background-color: #d4edda;
    border-color: #25D366;
    color: #155724;
    border-left: 4px solid #25D366;
}

.alert-whatsapp .alert-icon {
    color: #25D366;
}

/* تحسين مظهر شريط التقدم */
.progress-whatsapp {
    background-color: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
}

.progress-whatsapp .progress-bar {
    background: linear-gradient(90deg, #25D366, #128C7E);
    transition: width 0.6s ease;
}

/* تحسين مظهر التعليقات */
.comment-whatsapp {
    border-left: 3px solid #25D366;
    background-color: #f8f9fa;
    padding: 10px;
    margin: 5px 0;
    border-radius: 0 5px 5px 0;
}

.comment-whatsapp::before {
    content: "📱";
    margin-right: 5px;
}

/* تحسين مظهر القوائم المنسدلة */
.dropdown-menu .dropdown-item:hover {
    background-color: #25D366;
    color: white;
}

/* تحسين مظهر الأزرار في الأجهزة المحمولة */
@media (max-width: 768px) {
    .whatsapp-btn-added {
        font-size: 12px;
        padding: 5px 10px;
    }

    .modal-dialog {
        margin: 10px;
    }

    .frappe-control[data-fieldname="message"] textarea {
        min-height: 80px;
    }
}

/* تحسين مظهر الإشعارات */
.desk-alert.alert-success {
    border-left: 4px solid #25D366;
}

.desk-alert.alert-success .alert-icon {
    color: #25D366;
}

/* تحسين مظهر الأزرار المجمعة */
.btn-group .btn-whatsapp {
    border-radius: 0;
}

.btn-group .btn-whatsapp:first-child {
    border-radius: 5px 0 0 5px;
}

.btn-group .btn-whatsapp:last-child {
    border-radius: 0 5px 5px 0;
}

/* تأثيرات الحركة */
.whatsapp-btn-added {
    transition: all 0.3s ease;
}

.whatsapp-btn-added:active {
    transform: scale(0.95);
}

/* تحسين مظهر الحقول المطلوبة */
.frappe-control.has-error .control-label {
    color: #dc3545;
}

.frappe-control.has-error input,
.frappe-control.has-error select,
.frappe-control.has-error textarea {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

/* تحسين مظهر الروابط */
.whatsapp-link {
    color: #25D366;
    text-decoration: none;
}

.whatsapp-link:hover {
    color: #128C7E;
    text-decoration: underline;
}

/* تحسين مظهر الأيقونات */
.fa-whatsapp {
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

/* تحسين مظهر الجداول */
.table-whatsapp {
    border: 1px solid #dee2e6;
    border-radius: 10px;
    overflow: hidden;
}

.table-whatsapp th {
    background-color: #25D366;
    color: white;
    border: none;
}

.table-whatsapp td {
    border-color: #dee2e6;
}

/* تحسين مظهر البطاقات */
.card-whatsapp {
    border: 1px solid #25D366;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(37, 211, 102, 0.1);
}

.card-whatsapp .card-header {
    background-color: #25D366;
    color: white;
    border-radius: 10px 10px 0 0;
}

/* تحسين مظهر الشارات */
.badge-whatsapp {
    background-color: #25D366;
    color: white;
}

.badge-whatsapp-secondary {
    background-color: #128C7E;
    color: white;
}
