/**
 * WhatsApp Global Integration
 * إضافة أزرار WhatsApp لجميع المستندات
 */

console.log('🚀 تحميل WhatsApp Global Integration...');

// قائمة المستندات المدعومة
const WHATSAPP_SUPPORTED_DOCTYPES = [
    'Sales Invoice',
    'Purchase Invoice',
    'Quotation',
    'Sales Order',
    'Purchase Order',
    'Delivery Note',
    'Purchase Receipt',
    'Payment Entry',
    'Journal Entry',
    'Material Request',
    'Stock Entry',
    'Customer',
    'Supplier',
    'Item',
    'Lead',
    'Opportunity',
    'Project',
    'Task',
    'Issue',
    'Timesheet',
    'Expense Claim',
    'Leave Application',
    'Salary Slip',
    'Employee',
    'Asset',
    'Maintenance Schedule',
    'Work Order',
    'Job Card',
    'BOM',
    'Production Plan'
];

// Add WhatsApp icon to the Navbar
frappe.ui.toolbar.WhatsAppIcon = class WhatsAppIcon {
    constructor() {
        this.setup();
    }

    setup() {
        // Create the WhatsApp icon in the navbar
        this.$icon = $(`
            <div class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#"
                   data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <span class="whatsapp-icon">
                        <i class="fa fa-whatsapp" style="color: #25D366; font-size: 1.2em;"></i>
                    </span>
                </a>
                <div class="dropdown-menu dropdown-menu-right" aria-labelledby="whatsapp-icon">
                    <a class="dropdown-item" href="#List/WhatsApp Sender Account">
                        ${__("Sender Accounts")}
                    </a>
                    <a class="dropdown-item" href="#List/WhatsApp Message Template">
                        ${__("Message Templates")}
                    </a>
                    <a class="dropdown-item" href="#List/WhatsApp Group">
                        ${__("Groups")}
                    </a>
                    <a class="dropdown-item" href="#List/WhatsApp Message Log">
                        ${__("Message Logs")}
                    </a>
                    <div class="dropdown-divider"></div>
                    <a class="dropdown-item" href="#Form/WhatsApp Settings">
                        ${__("Settings")}
                    </a>
                </div>
            </div>
        `).appendTo($('.navbar-right'));

        // Handle click events
        this.$icon.find('a.dropdown-item').on('click', (e) => {
            e.preventDefault();
            const route = $(e.currentTarget).attr('href').substring(1);
            frappe.set_route(route);
        });
    }
};

// Initialize the WhatsApp icon when the desk is ready
$(document).ready(function() {
    frappe.ui.toolbar.whatsapp_icon = new frappe.ui.toolbar.WhatsAppIcon();

    console.log('📄 تحميل WhatsApp للمستندات...');

    // مراقبة تغييرات الصفحة
    setTimeout(function() {
        addWhatsAppButtonsGlobal();
    }, 1000);

    // مراقبة تحديثات الصفحة
    setInterval(function() {
        addWhatsAppButtonsGlobal();
        addTopBarButtonGlobal(); // إضافة زر الشريط العلوي
        forceAddWhatsAppButton(); // إضافة فورية للزر
    }, 3000);

    // إضافة فورية للزر عند تحميل الصفحة
    setTimeout(function() {
        forceAddWhatsAppButton();
    }, 2000);
});

// إضافة الأزرار عند تحميل أي نموذج
frappe.ui.form.on('*', {
    refresh: function(frm) {
        // التحقق من أن المستند مدعوم
        if (WHATSAPP_SUPPORTED_DOCTYPES && WHATSAPP_SUPPORTED_DOCTYPES.includes(frm.doctype)) {
            console.log(`🔄 تحديث النموذج: ${frm.doctype}`);

            // إضافة الزر البسيط مع تأخير قصير
            setTimeout(() => {
                addDirectWhatsAppButton(frm);
            }, 500);
        }
    }
});

// دالة مباشرة لإضافة زر WhatsApp
function addDirectWhatsAppButton(frm) {
    // التحقق من عدم وجود الزر مسبقاً
    if ($('.btn-whatsapp-direct').length > 0) {
        return;
    }

    console.log('🚀 إضافة زر WhatsApp المباشر...');

    // إضافة الزر باستخدام add_custom_button
    const whatsappBtn = frm.add_custom_button(__('📱 WhatsApp'), function() {
        showWhatsAppDialog(frm);
    });

    // تخصيص مظهر الزر
    whatsappBtn.addClass('btn-whatsapp-direct')
               .removeClass('btn-default')
               .addClass('btn-success')
               .css({
                   'background': '#25D366',
                   'border-color': '#25D366',
                   'color': 'white'
               });

    console.log('✅ تم إضافة زر WhatsApp المباشر بنجاح');

    // إضافة زر في الشريط العلوي بجانب زر "إنشاء"
    addTopBarWhatsAppButton(frm);
}

// دالة لإضافة زر WhatsApp في الشريط العلوي بجانب زر "إنشاء"
function addTopBarWhatsAppButton(frm) {
    // التحقق من عدم وجود الزر مسبقاً
    if ($('.btn-whatsapp-topbar').length > 0) {
        return;
    }

    console.log('🔝 إضافة زر WhatsApp في الشريط العلوي...');

    // إنشاء زر WhatsApp للشريط العلوي
    const topWhatsappBtn = $(`
        <button class="btn btn-success btn-whatsapp-topbar"
                style="background: #25D366 !important; border-color: #25D366 !important; color: white !important; margin-left: 8px; border-radius: 4px; font-weight: 500;">
            <i class="fa fa-whatsapp" style="margin-left: 5px;"></i>
            WhatsApp
        </button>
    `);

    // إضافة حدث النقر
    topWhatsappBtn.on('click', function(e) {
        e.preventDefault();
        showWhatsAppDialog(frm);
    });

    // إضافة تأثيرات hover
    topWhatsappBtn.hover(
        function() {
            $(this).css('background', '#128C7E');
        },
        function() {
            $(this).css('background', '#25D366');
        }
    );

    // البحث عن زر "إنشاء" وإضافة زر WhatsApp بجانبه
    let added = false;

    // الطريقة 1: البحث عن زر "إنشاء" أو "Create" في الشريط العلوي
    const createSelectors = [
        'button:contains("إنشاء")',
        'button:contains("Create")',
        '.btn:contains("إنشاء")',
        '.btn:contains("Create")',
        '[data-label="إنشاء"]',
        '[data-label="Create"]',
        '.page-actions button:first',
        '.page-head button:first'
    ];

    for (let selector of createSelectors) {
        const createBtn = $(selector).first();
        if (createBtn.length > 0 && createBtn.is(':visible')) {
            createBtn.after(topWhatsappBtn);
            added = true;
            console.log(`✅ تم إضافة الزر بجانب زر "إنشاء" باستخدام: ${selector}`);
            break;
        }
    }

    // الطريقة 2: البحث في الشريط العلوي
    if (!added) {
        const topBar = $('.page-head .container .row, .page-title .title-area, .page-actions').first();
        if (topBar.length > 0) {
            topBar.append(topWhatsappBtn);
            added = true;
            console.log('✅ تم إضافة الزر في الشريط العلوي');
        }
    }

    // الطريقة 3: في منطقة العنوان
    if (!added) {
        const titleArea = $('.page-title, .title-text, h1').first().parent();
        if (titleArea.length > 0) {
            titleArea.append($('<div style="display: inline-block; margin-left: 15px;"></div>').append(topWhatsappBtn));
            added = true;
            console.log('✅ تم إضافة الزر في منطقة العنوان');
        }
    }

    // الطريقة 4: في أي مكان في الصفحة العلوية
    if (!added) {
        const pageHead = $('.page-head, .page-header, .navbar').first();
        if (pageHead.length > 0) {
            pageHead.append($('<div style="position: absolute; top: 10px; right: 200px;"></div>').append(topWhatsappBtn));
            added = true;
            console.log('✅ تم إضافة الزر في رأس الصفحة');
        }
    }

    if (added) {
        console.log('🎉 تم إضافة زر WhatsApp في الشريط العلوي بنجاح');
    } else {
        console.log('❌ فشل في إضافة زر WhatsApp في الشريط العلوي');
    }
}

// دالة عامة لإضافة زر في الشريط العلوي
function addTopBarButtonGlobal() {
    // التحقق من عدم وجود الزر مسبقاً
    if ($('.btn-whatsapp-global-top').length > 0) {
        return;
    }

    // التحقق من وجود نموذج مفتوح ومدعوم
    if (!window.cur_frm || !cur_frm.doc || !cur_frm.doc.name || cur_frm.doc.__islocal) {
        return;
    }

    if (!WHATSAPP_SUPPORTED_DOCTYPES || !WHATSAPP_SUPPORTED_DOCTYPES.includes(cur_frm.doctype)) {
        return;
    }

    console.log('🌐 إضافة زر WhatsApp العام في الشريط العلوي...');

    // إنشاء زر WhatsApp للشريط العلوي
    const globalTopBtn = $(`
        <button class="btn btn-success btn-whatsapp-global-top"
                style="background: #25D366 !important; border-color: #25D366 !important; color: white !important; margin-left: 8px; border-radius: 4px; font-weight: 500; font-size: 13px;">
            <i class="fa fa-whatsapp" style="margin-left: 5px;"></i>
            إرسال WhatsApp
        </button>
    `);

    // إضافة حدث النقر
    globalTopBtn.on('click', function(e) {
        e.preventDefault();
        if (typeof showWhatsAppDialog === 'function') {
            showWhatsAppDialog(cur_frm);
        } else {
            alert('نافذة WhatsApp غير متوفرة');
        }
    });

    // إضافة تأثيرات hover
    globalTopBtn.hover(
        function() {
            $(this).css('background', '#128C7E');
        },
        function() {
            $(this).css('background', '#25D366');
        }
    );

    // البحث عن أماكن مختلفة لإضافة الزر
    let added = false;

    // قائمة أماكن محتملة للزر
    const locations = [
        // بجانب زر إنشاء
        'button:contains("إنشاء"):visible',
        'button:contains("Create"):visible',
        '.btn:contains("إنشاء"):visible',
        '.btn:contains("Create"):visible',

        // في الشريط العلوي
        '.page-actions .btn-group:first',
        '.page-head .btn-group:first',
        '.page-title .btn-group:first',

        // في منطقة العنوان
        '.page-title:first',
        '.title-area:first',
        'h1:first'
    ];

    for (let location of locations) {
        const target = $(location).first();
        if (target.length > 0 && target.is(':visible')) {
            if (location.includes('contains')) {
                // إضافة بجانب الزر
                target.after(globalTopBtn);
            } else {
                // إضافة داخل العنصر
                target.append(globalTopBtn);
            }
            added = true;
            console.log(`✅ تم إضافة الزر في: ${location}`);
            break;
        }
    }

    // إذا لم ينجح أي مكان، أضف كزر عائم
    if (!added) {
        $('body').append($('<div style="position: fixed; top: 80px; right: 20px; z-index: 1000;"></div>').append(globalTopBtn));
        added = true;
        console.log('✅ تم إضافة الزر كزر عائم في الأعلى');
    }

    if (added) {
        console.log('🎉 تم إضافة زر WhatsApp العام في الشريط العلوي بنجاح');
    }
}

// دالة لإضافة زر WhatsApp بقوة في أي مكان
function forceAddWhatsAppButton() {
    // التحقق من عدم وجود الزر مسبقاً
    if ($('.btn-whatsapp-force').length > 0) {
        return;
    }

    console.log('💪 محاولة إضافة زر WhatsApp بقوة...');

    // إنشاء الزر
    const forceBtn = $(`
        <button class="btn btn-success btn-whatsapp-force"
                style="background: #25D366 !important;
                       border-color: #25D366 !important;
                       color: white !important;
                       margin: 5px;
                       border-radius: 4px;
                       font-weight: 500;
                       position: relative;
                       z-index: 999;">
            <i class="fa fa-whatsapp" style="margin-left: 5px;"></i>
            WhatsApp
        </button>
    `);

    // إضافة حدث النقر
    forceBtn.on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        if (window.cur_frm && cur_frm.doc) {
            // إذا كانت دالة WhatsApp متوفرة، استخدمها
            if (typeof showWhatsAppDialog === 'function') {
                showWhatsAppDialog(cur_frm);
            } else {
                // نافذة بسيطة للاختبار
                const dialog = new frappe.ui.Dialog({
                    title: 'إرسال عبر WhatsApp',
                    fields: [
                        {
                            fieldtype: 'Data',
                            fieldname: 'phone',
                            label: 'رقم الهاتف',
                            reqd: 1
                        },
                        {
                            fieldtype: 'Small Text',
                            fieldname: 'message',
                            label: 'الرسالة',
                            default: `مرحباً، إليك ${cur_frm.doctype}: ${cur_frm.doc.name || 'جديد'}`
                        }
                    ],
                    primary_action_label: 'إرسال',
                    primary_action: function(values) {
                        frappe.msgprint(`سيتم إرسال رسالة إلى: ${values.phone}<br>الرسالة: ${values.message}`);
                        dialog.hide();
                    }
                });
                dialog.show();
            }
        } else {
            frappe.msgprint('🎉 زر WhatsApp يعمل!<br>لكن لا يوجد مستند مفتوح حالياً.');
        }
    });

    // إضافة تأثيرات hover
    forceBtn.hover(
        function() { $(this).css('background', '#128C7E !important'); },
        function() { $(this).css('background', '#25D366 !important'); }
    );

    // قائمة شاملة لأماكن إضافة الزر
    const targetLocations = [
        // بجانب أزرار محددة
        '.primary-action:visible',
        '.btn-primary:visible',
        'button[data-label="Save"]:visible',
        'button:contains("Save"):visible',
        'button:contains("حفظ"):visible',
        'button:contains("إنشاء"):visible',
        'button:contains("Create"):visible',

        // في مجموعات الأزرار
        '.form-actions .btn-group',
        '.page-actions .btn-group',
        '.actions-btn-group',

        // في مناطق عامة
        '.form-actions',
        '.page-actions',
        '.page-head',
        '.page-title',
        '.navbar'
    ];

    let added = false;

    for (let selector of targetLocations) {
        const target = $(selector).first();
        if (target.length > 0 && target.is(':visible')) {
            if (selector.includes('btn-group') || selector.includes('actions')) {
                target.append(forceBtn);
            } else {
                target.after(forceBtn);
            }
            added = true;
            console.log(`✅ تم إضافة الزر القوي في: ${selector}`);
            break;
        }
    }

    // إذا لم ينجح، أضف في أعلى النموذج
    if (!added) {
        const formLayout = $('.layout-main-section, .form-layout').first();
        if (formLayout.length > 0) {
            formLayout.prepend($(`
                <div class="whatsapp-force-container"
                     style="margin-bottom: 15px;
                            text-align: center;
                            padding: 10px;
                            background: linear-gradient(135deg, #25D366, #128C7E);
                            color: white;
                            border-radius: 8px;
                            box-shadow: 0 2px 10px rgba(37, 211, 102, 0.3);">
                    <strong>📱 إرسال المستند عبر WhatsApp</strong>
                </div>
            `).append(forceBtn));
            added = true;
            console.log('✅ تم إضافة الزر القوي في أعلى النموذج');
        }
    }

    // إذا لم ينجح، أضف كزر عائم مميز
    if (!added) {
        $('body').append($(`
            <div class="whatsapp-floating-force"
                 style="position: fixed;
                        top: 100px;
                        right: 20px;
                        z-index: 1000;
                        background: linear-gradient(135deg, #25D366, #128C7E);
                        padding: 15px;
                        border-radius: 12px;
                        box-shadow: 0 8px 25px rgba(37, 211, 102, 0.4);
                        border: 2px solid white;
                        animation: pulse 2s infinite;">
                <div style="margin-bottom: 8px; font-size: 12px; color: white; text-align: center; font-weight: bold;">📱 إرسال المستند</div>
            </div>
        `).append(forceBtn));

        // إضافة CSS للأنيميشن
        if (!$('#whatsapp-animation-css').length) {
            $('head').append(`
                <style id="whatsapp-animation-css">
                    @keyframes pulse {
                        0% { transform: scale(1); }
                        50% { transform: scale(1.05); }
                        100% { transform: scale(1); }
                    }
                </style>
            `);
        }

        added = true;
        console.log('✅ تم إضافة الزر القوي كزر عائم مميز');
    }

    if (added) {
        console.log('🎉 تم إضافة زر WhatsApp القوي بنجاح!');
    } else {
        console.log('❌ فشل في إضافة زر WhatsApp القوي');
    }
}

function addWhatsAppButtonsGlobal() {
    // التحقق من وجود نموذج مفتوح
    if (window.cur_frm && cur_frm.doc && cur_frm.doc.name && !cur_frm.doc.__islocal) {
        const doctype = cur_frm.doctype;
        const docname = cur_frm.doc.name;

        // التحقق من أن المستند مدعوم
        if (WHATSAPP_SUPPORTED_DOCTYPES.includes(doctype)) {
            console.log(`📱 إضافة أزرار WhatsApp لـ ${doctype}: ${docname}`);

            // التحقق من عدم وجود الأزرار مسبقاً
            if (!$('.whatsapp-btn-added').length) {
                addWhatsAppButtons(cur_frm);
            }

            // إضافة الزر البسيط إذا لم يكن موجوداً
            if (!$('.btn-whatsapp-simple').length) {
                addSimpleWhatsAppButton(cur_frm);
            }
        }
    }
}

// دالة بسيطة لإضافة زر WhatsApp مباشرة
function addSimpleWhatsAppButton(frm) {
    console.log('🔧 إضافة زر WhatsApp البسيط...');

    // إنشاء الزر
    const whatsappBtn = $(`
        <button class="btn btn-success btn-whatsapp-simple"
                style="margin-left: 10px; background: #25D366 !important; border-color: #25D366 !important; color: white !important;">
            <i class="fa fa-whatsapp"></i> WhatsApp
        </button>
    `);

    // إضافة حدث النقر
    whatsappBtn.on('click', function(e) {
        e.preventDefault();
        showWhatsAppDialog(frm);
    });

    // البحث عن مكان لإضافة الزر
    let added = false;

    // الطريقة 1: بجانب زر Save
    const saveBtn = $('.primary-action, .btn-primary').filter(':contains("Save"), :contains("حفظ")').first();
    if (saveBtn.length > 0 && !added) {
        saveBtn.after(whatsappBtn);
        added = true;
        console.log('✅ تم إضافة الزر بجانب زر Save');
    }

    // الطريقة 2: في منطقة الأزرار العلوية
    if (!added) {
        const btnGroup = $('.form-actions .btn-group, .page-actions .btn-group, .actions-btn-group').first();
        if (btnGroup.length > 0) {
            btnGroup.append(whatsappBtn);
            added = true;
            console.log('✅ تم إضافة الزر في مجموعة الأزرار');
        }
    }

    // الطريقة 3: في منطقة الأزرار
    if (!added) {
        const formActions = $('.form-actions, .page-actions, .form-toolbar').first();
        if (formActions.length > 0) {
            formActions.append(whatsappBtn);
            added = true;
            console.log('✅ تم إضافة الزر في منطقة الأزرار');
        }
    }

    // الطريقة 4: في أعلى النموذج
    if (!added) {
        const formLayout = $('.form-layout, .layout-main-section').first();
        if (formLayout.length > 0) {
            formLayout.prepend($('<div class="whatsapp-btn-wrapper" style="margin-bottom: 15px; text-align: right; padding: 10px; border-bottom: 1px solid #ddd;"></div>').append(whatsappBtn));
            added = true;
            console.log('✅ تم إضافة الزر في أعلى النموذج');
        }
    }

    // الطريقة 5: زر عائم
    if (!added) {
        $('body').append($('<div class="whatsapp-floating-btn" style="position: fixed; top: 120px; right: 20px; z-index: 1000;"></div>').append(whatsappBtn));
        added = true;
        console.log('✅ تم إضافة الزر كزر عائم');
    }

    if (added) {
        console.log('🎉 تم إضافة زر WhatsApp البسيط بنجاح');
    } else {
        console.log('❌ فشل في إضافة زر WhatsApp البسيط');
    }
}

function addWhatsAppButtons(frm) {
    console.log(`✅ إضافة أزرار WhatsApp للمستند: ${frm.doctype} - ${frm.doc.name}`);

    // إضافة زر WhatsApp الرئيسي في شريط الأزرار العلوي
    addMainWhatsAppButton(frm);

    // إضافة مجموعة أزرار WhatsApp في قائمة Actions
    const whatsappGroup = frm.add_custom_button(__('WhatsApp'), null, __('Actions'));

    // إضافة زر إرسال عبر WhatsApp (مفصل)
    frm.add_custom_button(__('📱 إرسال مفصل'), function() {
        showWhatsAppDialog(frm);
    }, __('WhatsApp')).addClass('btn-success whatsapp-btn-added');

    // إضافة زر إرسال PDF سريع
    frm.add_custom_button(__('📄 إرسال PDF'), function() {
        sendDocumentQuick(frm, 'PDF');
    }, __('WhatsApp')).addClass('btn-info whatsapp-btn-added');

    // إضافة زر إرسال رابط سريع
    frm.add_custom_button(__('🔗 إرسال رابط'), function() {
        sendDocumentQuick(frm, 'رابط');
    }, __('WhatsApp')).addClass('btn-warning whatsapp-btn-added');

    // إضافة زر إرسال رسالة فقط
    frm.add_custom_button(__('💬 رسالة فقط'), function() {
        sendMessageOnly(frm);
    }, __('WhatsApp')).addClass('btn-primary whatsapp-btn-added');

    console.log('✅ تم إضافة أزرار WhatsApp بنجاح');
}

function addMainWhatsAppButton(frm) {
    // التحقق من عدم وجود الزر مسبقاً
    if ($('.btn-whatsapp-main').length > 0) {
        return;
    }

    // إنشاء زر WhatsApp الرئيسي
    const whatsappBtn = $(`
        <button class="btn btn-whatsapp-main"
                style="background: #25D366; color: white; border: 1px solid #25D366; margin-left: 10px; border-radius: 6px; padding: 8px 16px; font-weight: 500; transition: all 0.3s ease;"
                title="إرسال عبر WhatsApp">
            <i class="fa fa-whatsapp" style="margin-left: 5px;"></i>
            WhatsApp
        </button>
    `);

    // إضافة تأثيرات hover
    whatsappBtn.hover(
        function() {
            $(this).css({
                'background': '#128C7E',
                'border-color': '#128C7E',
                'transform': 'translateY(-1px)',
                'box-shadow': '0 4px 12px rgba(37, 211, 102, 0.3)'
            });
        },
        function() {
            $(this).css({
                'background': '#25D366',
                'border-color': '#25D366',
                'transform': 'translateY(0)',
                'box-shadow': 'none'
            });
        }
    );

    // إضافة حدث النقر
    whatsappBtn.on('click', function(e) {
        e.preventDefault();
        showWhatsAppQuickMenu(frm);
    });

    // إضافة الزر في شريط الأزرار العلوي
    // البحث عن أفضل مكان لإضافة الزر

    // الطريقة الأولى: في شريط الأزرار الرئيسي
    const pageActions = $('.page-actions .btn-group:first');
    if (pageActions.length > 0) {
        pageActions.append(whatsappBtn);
        console.log('✅ تم إضافة الزر في شريط الأزرار الرئيسي');
        return;
    }

    // الطريقة الثانية: في منطقة العنوان
    const pageTitle = $('.page-title .title-area');
    if (pageTitle.length > 0) {
        pageTitle.append($('<div class="whatsapp-btn-wrapper" style="display: inline-block; margin-left: 15px;"></div>').append(whatsappBtn));
        console.log('✅ تم إضافة الزر في منطقة العنوان');
        return;
    }

    // الطريقة الثالثة: في شريط الأدوات العلوي
    const toolbar = $('.form-toolbar, .page-head .container .row');
    if (toolbar.length > 0) {
        toolbar.first().append($('<div class="whatsapp-btn-wrapper" style="display: inline-block; float: right; margin-left: 10px;"></div>').append(whatsappBtn));
        console.log('✅ تم إضافة الزر في شريط الأدوات');
        return;
    }

    // الطريقة الرابعة: في أعلى النموذج (الطريقة الافتراضية)
    const layoutMain = $('.layout-main-section').first();
    if (layoutMain.length > 0) {
        layoutMain.prepend($('<div class="whatsapp-btn-container"></div>').append(whatsappBtn));
        console.log('✅ تم إضافة الزر في أعلى النموذج');
        return;
    }

    // الطريقة الخامسة: في أي مكان متاح (الطريقة الأخيرة)
    $('body').append($('<div class="whatsapp-floating-btn" style="position: fixed; top: 100px; right: 20px; z-index: 1000;"></div>').append(whatsappBtn));
    console.log('✅ تم إضافة الزر كزر عائم');

    console.log('✅ تم إضافة زر WhatsApp الرئيسي');
}

function showWhatsAppQuickMenu(frm) {
    // إنشاء قائمة سريعة لخيارات WhatsApp
    const quickMenu = new frappe.ui.Dialog({
        title: __('📱 إرسال عبر WhatsApp'),
        fields: [
            {
                fieldtype: 'HTML',
                fieldname: 'quick_options',
                options: `
                    <div style="text-align: center; padding: 25px;">
                        <div style="margin-bottom: 25px;">
                            <h4 style="color: #25D366; margin-bottom: 10px; font-weight: 600;">اختر طريقة الإرسال</h4>
                            <p style="color: #666; font-size: 14px; margin: 0;">المستند: ${frm.doctype} - ${frm.doc.name}</p>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; max-width: 400px; margin: 0 auto;">
                            <button class="btn btn-success btn-block quick-detailed" style="padding: 20px; font-size: 14px; border-radius: 12px; font-weight: 500; transition: all 0.3s ease;">
                                <i class="fa fa-whatsapp" style="margin-left: 5px; font-size: 18px;"></i><br>
                                <span style="margin-top: 8px; display: block;">إرسال مفصل</span>
                            </button>
                            <button class="btn btn-info btn-block quick-pdf" style="padding: 20px; font-size: 14px; border-radius: 12px; font-weight: 500; transition: all 0.3s ease;">
                                <i class="fa fa-file-pdf-o" style="margin-left: 5px; font-size: 18px;"></i><br>
                                <span style="margin-top: 8px; display: block;">إرسال PDF</span>
                            </button>
                            <button class="btn btn-warning btn-block quick-link" style="padding: 20px; font-size: 14px; border-radius: 12px; font-weight: 500; transition: all 0.3s ease;">
                                <i class="fa fa-link" style="margin-left: 5px; font-size: 18px;"></i><br>
                                <span style="margin-top: 8px; display: block;">إرسال رابط</span>
                            </button>
                            <button class="btn btn-primary btn-block quick-message" style="padding: 20px; font-size: 14px; border-radius: 12px; font-weight: 500; transition: all 0.3s ease;">
                                <i class="fa fa-comment" style="margin-left: 5px; font-size: 18px;"></i><br>
                                <span style="margin-top: 8px; display: block;">رسالة فقط</span>
                            </button>
                        </div>
                        <div style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #eee;">
                            <small style="color: #888;">💡 نصيحة: استخدم "إرسال مفصل" لخيارات أكثر</small>
                        </div>
                    </div>
                `
            }
        ],
        primary_action_label: __('إغلاق'),
        primary_action: function() {
            quickMenu.hide();
        }
    });

    // إضافة class للحوار لتطبيق CSS المخصص
    quickMenu.$wrapper.addClass('whatsapp-quick-menu');

    // إضافة أحداث النقر للأزرار
    quickMenu.$wrapper.on('click', '.quick-detailed', function() {
        quickMenu.hide();
        showWhatsAppDialog(frm);
    });

    quickMenu.$wrapper.on('click', '.quick-pdf', function() {
        quickMenu.hide();
        sendDocumentQuick(frm, 'PDF');
    });

    quickMenu.$wrapper.on('click', '.quick-link', function() {
        quickMenu.hide();
        sendDocumentQuick(frm, 'رابط');
    });

    quickMenu.$wrapper.on('click', '.quick-message', function() {
        quickMenu.hide();
        sendMessageOnly(frm);
    });

    quickMenu.show();
}

function showWhatsAppDialog(frm) {
    console.log('📱 فتح نافذة WhatsApp...');

    const dialog = new frappe.ui.Dialog({
        title: __('إرسال {0} عبر WhatsApp', [__(frm.doctype)]),
        fields: [
            {
                fieldtype: 'Section Break',
                label: __('حساب المرسل')
            },
            {
                fieldname: 'sender_account',
                fieldtype: 'Link',
                label: __('حساب WhatsApp'),
                options: 'WhatsApp Sender Account',
                get_query: function() {
                    return {
                        filters: {
                            'status': 'Connected'
                        }
                    };
                }
            },
            {
                fieldtype: 'Section Break',
                label: __('تفاصيل المستقبل')
            },
            {
                fieldname: 'recipient_type',
                fieldtype: 'Select',
                label: __('نوع المستقبل'),
                options: 'رقم هاتف\nعميل\nمورد\nجهة اتصال',
                default: getDefaultRecipientType(frm),
                reqd: 1,
                onchange: function() {
                    updateRecipientFields(dialog, frm);
                }
            },
            {
                fieldname: 'phone_number',
                fieldtype: 'Data',
                label: __('رقم الهاتف'),
                description: __('مثال: +967xxxxxxxxx أو 967xxxxxxxxx'),
                default: getDefaultPhone(frm)
            },
            {
                fieldname: 'customer',
                fieldtype: 'Link',
                options: 'Customer',
                label: __('العميل'),
                hidden: 1
            },
            {
                fieldname: 'supplier',
                fieldtype: 'Link',
                options: 'Supplier',
                label: __('المورد'),
                hidden: 1
            },
            {
                fieldname: 'contact',
                fieldtype: 'Link',
                options: 'Contact',
                label: __('جهة الاتصال'),
                hidden: 1
            },
            {
                fieldtype: 'Section Break',
                label: __('تفاصيل الإرسال')
            },
            {
                fieldname: 'send_type',
                fieldtype: 'Select',
                label: __('نوع الإرسال'),
                options: 'PDF + رسالة\nPDF فقط\nرسالة فقط\nرابط المستند',
                default: 'PDF + رسالة',
                reqd: 1
            },
            {
                fieldname: 'message',
                fieldtype: 'Text',
                label: __('الرسالة'),
                default: getDefaultMessage(frm)
            },
            {
                fieldname: 'print_format',
                fieldtype: 'Link',
                options: 'Print Format',
                label: __('تنسيق الطباعة'),
                get_query: function() {
                    return {
                        filters: {
                            'doc_type': frm.doctype
                        }
                    };
                }
            }
        ],
        primary_action_label: __('إرسال'),
        primary_action: function(values) {
            sendWhatsAppDocument(frm, values);
            dialog.hide();
        }
    });

    // تحديد القيم الافتراضية
    setDefaultValues(dialog, frm);

    dialog.show();
}

function getDefaultRecipientType(frm) {
    if (frm.doc.customer) return 'عميل';
    if (frm.doc.supplier) return 'مورد';
    return 'رقم هاتف';
}

function getDefaultPhone(frm) {
    // محاولة الحصول على رقم هاتف من المستند
    if (frm.doc.mobile_no) return frm.doc.mobile_no;
    if (frm.doc.contact_mobile) return frm.doc.contact_mobile;
    if (frm.doc.phone) return frm.doc.phone;
    return '+967'; // رقم افتراضي لليمن
}

function updateRecipientFields(dialog, frm) {
    const recipient_type = dialog.get_value('recipient_type');

    // إخفاء جميع الحقول
    dialog.set_df_property('phone_number', 'hidden', 1);
    dialog.set_df_property('customer', 'hidden', 1);
    dialog.set_df_property('supplier', 'hidden', 1);
    dialog.set_df_property('contact', 'hidden', 1);

    // إظهار الحقل المناسب وتحديد القيمة الافتراضية
    switch(recipient_type) {
        case 'رقم هاتف':
            dialog.set_df_property('phone_number', 'hidden', 0);
            dialog.set_df_property('phone_number', 'reqd', 1);
            break;
        case 'عميل':
            dialog.set_df_property('customer', 'hidden', 0);
            dialog.set_df_property('customer', 'reqd', 1);
            if (frm.doc.customer) {
                dialog.set_value('customer', frm.doc.customer);
            }
            break;
        case 'مورد':
            dialog.set_df_property('supplier', 'hidden', 0);
            dialog.set_df_property('supplier', 'reqd', 1);
            if (frm.doc.supplier) {
                dialog.set_value('supplier', frm.doc.supplier);
            }
            break;
        case 'جهة اتصال':
            dialog.set_df_property('contact', 'hidden', 0);
            dialog.set_df_property('contact', 'reqd', 1);
            break;
    }
}

function setDefaultValues(dialog, frm) {
    // تحديد حساب WhatsApp الافتراضي
    frappe.call({
        method: 'whatsapp.whatsapp.api.get_default_sender_account',
        callback: function(r) {
            if (r.message) {
                dialog.set_value('sender_account', r.message);
            }
        }
    });

    // تحديث حقول المستقبل
    setTimeout(() => {
        updateRecipientFields(dialog, frm);
    }, 100);
}

function getDefaultMessage(frm) {
    const doctype_ar = {
        'Sales Invoice': 'فاتورة مبيعات',
        'Purchase Invoice': 'فاتورة مشتريات',
        'Quotation': 'عرض سعر',
        'Sales Order': 'أمر مبيعات',
        'Purchase Order': 'أمر شراء',
        'Delivery Note': 'إشعار تسليم',
        'Purchase Receipt': 'إيصال شراء',
        'Payment Entry': 'قيد دفع',
        'Journal Entry': 'قيد يومية',
        'Material Request': 'طلب مواد',
        'Stock Entry': 'قيد مخزون',
        'Customer': 'بيانات عميل',
        'Supplier': 'بيانات مورد',
        'Item': 'بيانات صنف',
        'Lead': 'عميل محتمل',
        'Opportunity': 'فرصة بيع',
        'Project': 'مشروع',
        'Task': 'مهمة',
        'Issue': 'مشكلة',
        'Timesheet': 'جدول زمني',
        'Expense Claim': 'مطالبة مصروفات',
        'Leave Application': 'طلب إجازة',
        'Salary Slip': 'قسيمة راتب',
        'Employee': 'بيانات موظف',
        'Asset': 'أصل',
        'Maintenance Schedule': 'جدولة صيانة',
        'Work Order': 'أمر عمل',
        'Job Card': 'بطاقة عمل',
        'BOM': 'قائمة مواد',
        'Production Plan': 'خطة إنتاج'
    };

    const doc_name_ar = doctype_ar[frm.doctype] || frm.doctype;

    let message = `السلام عليكم ورحمة الله وبركاته\n\nنرسل لكم ${doc_name_ar} رقم: ${frm.doc.name}\n\n`;

    // إضافة تفاصيل حسب نوع المستند
    if (frm.doc.customer) message += `العميل: ${frm.doc.customer}\n`;
    if (frm.doc.supplier) message += `المورد: ${frm.doc.supplier}\n`;
    if (frm.doc.posting_date) message += `التاريخ: ${frm.doc.posting_date}\n`;
    if (frm.doc.transaction_date) message += `التاريخ: ${frm.doc.transaction_date}\n`;
    if (frm.doc.grand_total) message += `المبلغ الإجمالي: ${format_currency(frm.doc.grand_total, frm.doc.currency || 'YER')}\n`;
    if (frm.doc.total_amount) message += `المبلغ الإجمالي: ${format_currency(frm.doc.total_amount, frm.doc.currency || 'YER')}\n`;
    if (frm.doc.status) message += `الحالة: ${frm.doc.status}\n`;

    message += `\nشكراً لكم\nفريق ${frappe.boot.sitename || 'العمل'}`;

    return message;
}

function sendWhatsAppDocument(frm, values) {
    console.log('📤 إرسال مستند عبر WhatsApp...');

    frappe.show_progress(__('جاري الإرسال...'), 0, 100, __('جاري تحضير المستند'));

    frappe.call({
        method: 'whatsapp.whatsapp.api.send_document_via_whatsapp',
        args: {
            doctype: frm.doctype,
            docname: frm.doc.name,
            sender_account: values.sender_account,
            recipient_type: values.recipient_type,
            phone_number: values.phone_number,
            customer: values.customer,
            supplier: values.supplier,
            contact: values.contact,
            send_type: values.send_type,
            message: values.message,
            print_format: values.print_format
        },
        callback: function(r) {
            frappe.hide_progress();
            if (r.message && r.message.success) {
                frappe.show_alert({
                    message: __('تم إرسال المستند بنجاح عبر WhatsApp'),
                    indicator: 'green'
                });

                // إضافة تعليق في المستند
                const recipient = r.message.recipient || values.phone_number || values.customer || values.supplier;
                frm.add_comment('Comment', __('تم إرسال المستند عبر WhatsApp إلى {0}', [recipient]));

                console.log('✅ تم الإرسال بنجاح');
            } else {
                frappe.msgprint({
                    title: __('خطأ في الإرسال'),
                    message: r.message ? r.message.error : __('حدث خطأ غير متوقع'),
                    indicator: 'red'
                });
                console.error('❌ فشل في الإرسال:', r.message);
            }
        },
        error: function(r) {
            frappe.hide_progress();
            frappe.msgprint({
                title: __('خطأ في الإرسال'),
                message: __('فشل في إرسال المستند. يرجى المحاولة مرة أخرى.'),
                indicator: 'red'
            });
            console.error('❌ خطأ في الإرسال:', r);
        }
    });
}

function sendDocumentQuick(frm, type) {
    console.log(`📤 إرسال سريع: ${type}`);

    // محاولة الحصول على رقم هاتف من المستند
    let defaultPhone = getDefaultPhone(frm);
    if (defaultPhone === '+967') {
        // إذا لم يوجد رقم في المستند، محاولة الحصول من العميل/المورد
        if (frm.doc.customer || frm.doc.supplier) {
            defaultPhone = 'سيتم البحث عن الرقم تلقائياً';
        }
    }

    const phone = prompt(`أدخل رقم الهاتف:\n(أو اتركه فارغاً للبحث التلقائي)`, defaultPhone);
    if (phone === null) return; // المستخدم ألغى العملية

    frappe.show_progress(__('جاري الإرسال...'), 50, 100);

    let send_type, message, recipient_type = 'رقم هاتف';
    let customer = null, supplier = null;

    // تحديد نوع الإرسال والمستقبل
    if (phone === '' || phone === 'سيتم البحث عن الرقم تلقائياً') {
        if (frm.doc.customer) {
            recipient_type = 'عميل';
            customer = frm.doc.customer;
        } else if (frm.doc.supplier) {
            recipient_type = 'مورد';
            supplier = frm.doc.supplier;
        } else {
            frappe.hide_progress();
            frappe.msgprint(__('لم يتم العثور على رقم هاتف أو عميل/مورد'));
            return;
        }
    }

    if (type === 'PDF') {
        send_type = 'PDF فقط';
        message = `📄 ${frm.doctype} رقم: ${frm.doc.name}`;
    } else {
        send_type = 'رابط المستند';
        message = `🔗 رابط ${frm.doctype} رقم: ${frm.doc.name}`;
    }

    frappe.call({
        method: 'whatsapp.whatsapp.api.send_document_via_whatsapp',
        args: {
            doctype: frm.doctype,
            docname: frm.doc.name,
            sender_account: null,
            recipient_type: recipient_type,
            phone_number: phone !== 'سيتم البحث عن الرقم تلقائياً' ? phone : null,
            customer: customer,
            supplier: supplier,
            send_type: send_type,
            message: message
        },
        callback: function(r) {
            frappe.hide_progress();
            if (r.message && r.message.success) {
                frappe.show_alert({
                    message: __('تم الإرسال بنجاح إلى {0}', [r.message.recipient]),
                    indicator: 'green'
                });
                frm.add_comment('Comment', __('تم إرسال {0} عبر WhatsApp', [type]));
            } else {
                frappe.msgprint(__('فشل في الإرسال: {0}', [r.message ? r.message.error : 'خطأ غير معروف']));
            }
        },
        error: function() {
            frappe.hide_progress();
            frappe.msgprint(__('حدث خطأ في الإرسال'));
        }
    });
}

function sendMessageOnly(frm) {
    console.log('💬 إرسال رسالة فقط');

    const message = prompt('أدخل الرسالة:', getDefaultMessage(frm));
    if (!message) return;

    const phone = prompt('أدخل رقم الهاتف:', getDefaultPhone(frm));
    if (!phone) return;

    frappe.show_progress(__('جاري إرسال الرسالة...'), 50, 100);

    frappe.call({
        method: 'whatsapp.whatsapp.api.send_document_via_whatsapp',
        args: {
            doctype: frm.doctype,
            docname: frm.doc.name,
            sender_account: null,
            recipient_type: 'رقم هاتف',
            phone_number: phone,
            send_type: 'رسالة فقط',
            message: message
        },
        callback: function(r) {
            frappe.hide_progress();
            if (r.message && r.message.success) {
                frappe.show_alert({
                    message: __('تم إرسال الرسالة بنجاح'),
                    indicator: 'green'
                });
                frm.add_comment('Comment', __('تم إرسال رسالة WhatsApp إلى {0}', [phone]));
            } else {
                frappe.msgprint(__('فشل في إرسال الرسالة: {0}', [r.message ? r.message.error : 'خطأ غير معروف']));
            }
        },
        error: function() {
            frappe.hide_progress();
            frappe.msgprint(__('حدث خطأ في إرسال الرسالة'));
        }
    });
}

console.log('✅ تم تحميل WhatsApp Global Integration بنجاح');
