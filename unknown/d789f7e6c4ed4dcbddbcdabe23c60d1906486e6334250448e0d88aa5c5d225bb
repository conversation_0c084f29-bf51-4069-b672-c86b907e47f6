/**
 * WhatsApp Global Integration
 * إضافة أزرار WhatsApp لجميع المستندات
 */

console.log('🚀 تحميل WhatsApp Global Integration...');

// قائمة المستندات المدعومة
const WHATSAPP_SUPPORTED_DOCTYPES = [
    'Sales Invoice',
    'Purchase Invoice',
    'Quotation',
    'Sales Order',
    'Purchase Order',
    'Delivery Note',
    'Purchase Receipt',
    'Payment Entry',
    'Journal Entry',
    'Material Request',
    'Stock Entry',
    'Customer',
    'Supplier',
    'Item',
    'Lead',
    'Opportunity',
    'Project',
    'Task',
    'Issue',
    'Timesheet',
    'Expense Claim',
    'Leave Application',
    'Salary Slip',
    'Employee',
    'Asset',
    'Maintenance Schedule',
    'Work Order',
    'Job Card',
    'BOM',
    'Production Plan'
];

// إضافة أزرار WhatsApp عند تحميل أي صفحة
$(document).ready(function() {
    console.log('📄 تحميل WhatsApp للمستندات...');

    // مراقبة تغييرات الصفحة
    setTimeout(function() {
        addWhatsAppButtonsGlobal();
    }, 1000);

    // مراقبة تحديثات الصفحة
    setInterval(function() {
        addWhatsAppButtonsGlobal();
    }, 3000);
});

function addWhatsAppButtonsGlobal() {
    // التحقق من وجود نموذج مفتوح
    if (window.cur_frm && cur_frm.doc && cur_frm.doc.name && !cur_frm.doc.__islocal) {
        const doctype = cur_frm.doctype;
        const docname = cur_frm.doc.name;

        // التحقق من أن المستند مدعوم
        if (WHATSAPP_SUPPORTED_DOCTYPES.includes(doctype)) {
            console.log(`📱 إضافة أزرار WhatsApp لـ ${doctype}: ${docname}`);

            // التحقق من عدم وجود الأزرار مسبقاً
            if (!$('.whatsapp-btn-added').length) {
                addWhatsAppButtons(cur_frm);
            }
        }
    }
}

function addWhatsAppButtons(frm) {
    console.log(`✅ إضافة أزرار WhatsApp للمستند: ${frm.doctype} - ${frm.doc.name}`);

    // إضافة مجموعة أزرار WhatsApp
    const whatsappGroup = frm.add_custom_button(__('WhatsApp'), null, __('Actions'));

    // إضافة زر إرسال عبر WhatsApp (مفصل)
    frm.add_custom_button(__('📱 إرسال مفصل'), function() {
        showWhatsAppDialog(frm);
    }, __('WhatsApp')).addClass('btn-success whatsapp-btn-added');

    // إضافة زر إرسال PDF سريع
    frm.add_custom_button(__('📄 إرسال PDF'), function() {
        sendDocumentQuick(frm, 'PDF');
    }, __('WhatsApp')).addClass('btn-info whatsapp-btn-added');

    // إضافة زر إرسال رابط سريع
    frm.add_custom_button(__('🔗 إرسال رابط'), function() {
        sendDocumentQuick(frm, 'رابط');
    }, __('WhatsApp')).addClass('btn-warning whatsapp-btn-added');

    // إضافة زر إرسال رسالة فقط
    frm.add_custom_button(__('💬 رسالة فقط'), function() {
        sendMessageOnly(frm);
    }, __('WhatsApp')).addClass('btn-primary whatsapp-btn-added');

    console.log('✅ تم إضافة أزرار WhatsApp بنجاح');
}

function showWhatsAppDialog(frm) {
    console.log('📱 فتح نافذة WhatsApp...');

    const dialog = new frappe.ui.Dialog({
        title: __('إرسال {0} عبر WhatsApp', [__(frm.doctype)]),
        fields: [
            {
                fieldtype: 'Section Break',
                label: __('حساب المرسل')
            },
            {
                fieldname: 'sender_account',
                fieldtype: 'Link',
                label: __('حساب WhatsApp'),
                options: 'WhatsApp Sender Account',
                get_query: function() {
                    return {
                        filters: {
                            'status': 'Connected'
                        }
                    };
                }
            },
            {
                fieldtype: 'Section Break',
                label: __('تفاصيل المستقبل')
            },
            {
                fieldname: 'recipient_type',
                fieldtype: 'Select',
                label: __('نوع المستقبل'),
                options: 'رقم هاتف\nعميل\nمورد\nجهة اتصال',
                default: getDefaultRecipientType(frm),
                reqd: 1,
                onchange: function() {
                    updateRecipientFields(dialog, frm);
                }
            },
            {
                fieldname: 'phone_number',
                fieldtype: 'Data',
                label: __('رقم الهاتف'),
                description: __('مثال: +967xxxxxxxxx أو 967xxxxxxxxx'),
                default: getDefaultPhone(frm)
            },
            {
                fieldname: 'customer',
                fieldtype: 'Link',
                options: 'Customer',
                label: __('العميل'),
                hidden: 1
            },
            {
                fieldname: 'supplier',
                fieldtype: 'Link',
                options: 'Supplier',
                label: __('المورد'),
                hidden: 1
            },
            {
                fieldname: 'contact',
                fieldtype: 'Link',
                options: 'Contact',
                label: __('جهة الاتصال'),
                hidden: 1
            },
            {
                fieldtype: 'Section Break',
                label: __('تفاصيل الإرسال')
            },
            {
                fieldname: 'send_type',
                fieldtype: 'Select',
                label: __('نوع الإرسال'),
                options: 'PDF + رسالة\nPDF فقط\nرسالة فقط\nرابط المستند',
                default: 'PDF + رسالة',
                reqd: 1
            },
            {
                fieldname: 'message',
                fieldtype: 'Text',
                label: __('الرسالة'),
                default: getDefaultMessage(frm)
            },
            {
                fieldname: 'print_format',
                fieldtype: 'Link',
                options: 'Print Format',
                label: __('تنسيق الطباعة'),
                get_query: function() {
                    return {
                        filters: {
                            'doc_type': frm.doctype
                        }
                    };
                }
            }
        ],
        primary_action_label: __('إرسال'),
        primary_action: function(values) {
            sendWhatsAppDocument(frm, values);
            dialog.hide();
        }
    });

    // تحديد القيم الافتراضية
    setDefaultValues(dialog, frm);

    dialog.show();
}

function getDefaultRecipientType(frm) {
    if (frm.doc.customer) return 'عميل';
    if (frm.doc.supplier) return 'مورد';
    return 'رقم هاتف';
}

function getDefaultPhone(frm) {
    // محاولة الحصول على رقم هاتف من المستند
    if (frm.doc.mobile_no) return frm.doc.mobile_no;
    if (frm.doc.contact_mobile) return frm.doc.contact_mobile;
    if (frm.doc.phone) return frm.doc.phone;
    return '+967'; // رقم افتراضي لليمن
}

function updateRecipientFields(dialog, frm) {
    const recipient_type = dialog.get_value('recipient_type');

    // إخفاء جميع الحقول
    dialog.set_df_property('phone_number', 'hidden', 1);
    dialog.set_df_property('customer', 'hidden', 1);
    dialog.set_df_property('supplier', 'hidden', 1);
    dialog.set_df_property('contact', 'hidden', 1);

    // إظهار الحقل المناسب وتحديد القيمة الافتراضية
    switch(recipient_type) {
        case 'رقم هاتف':
            dialog.set_df_property('phone_number', 'hidden', 0);
            dialog.set_df_property('phone_number', 'reqd', 1);
            break;
        case 'عميل':
            dialog.set_df_property('customer', 'hidden', 0);
            dialog.set_df_property('customer', 'reqd', 1);
            if (frm.doc.customer) {
                dialog.set_value('customer', frm.doc.customer);
            }
            break;
        case 'مورد':
            dialog.set_df_property('supplier', 'hidden', 0);
            dialog.set_df_property('supplier', 'reqd', 1);
            if (frm.doc.supplier) {
                dialog.set_value('supplier', frm.doc.supplier);
            }
            break;
        case 'جهة اتصال':
            dialog.set_df_property('contact', 'hidden', 0);
            dialog.set_df_property('contact', 'reqd', 1);
            break;
    }
}

function setDefaultValues(dialog, frm) {
    // تحديد حساب WhatsApp الافتراضي
    frappe.call({
        method: 'whatsapp.whatsapp.api.get_default_sender_account',
        callback: function(r) {
            if (r.message) {
                dialog.set_value('sender_account', r.message);
            }
        }
    });

    // تحديث حقول المستقبل
    setTimeout(() => {
        updateRecipientFields(dialog, frm);
    }, 100);
}

function getDefaultMessage(frm) {
    const doctype_ar = {
        'Sales Invoice': 'فاتورة مبيعات',
        'Purchase Invoice': 'فاتورة مشتريات',
        'Quotation': 'عرض سعر',
        'Sales Order': 'أمر مبيعات',
        'Purchase Order': 'أمر شراء',
        'Delivery Note': 'إشعار تسليم',
        'Purchase Receipt': 'إيصال شراء',
        'Payment Entry': 'قيد دفع',
        'Journal Entry': 'قيد يومية',
        'Material Request': 'طلب مواد',
        'Stock Entry': 'قيد مخزون',
        'Customer': 'بيانات عميل',
        'Supplier': 'بيانات مورد',
        'Item': 'بيانات صنف',
        'Lead': 'عميل محتمل',
        'Opportunity': 'فرصة بيع',
        'Project': 'مشروع',
        'Task': 'مهمة',
        'Issue': 'مشكلة',
        'Timesheet': 'جدول زمني',
        'Expense Claim': 'مطالبة مصروفات',
        'Leave Application': 'طلب إجازة',
        'Salary Slip': 'قسيمة راتب',
        'Employee': 'بيانات موظف',
        'Asset': 'أصل',
        'Maintenance Schedule': 'جدولة صيانة',
        'Work Order': 'أمر عمل',
        'Job Card': 'بطاقة عمل',
        'BOM': 'قائمة مواد',
        'Production Plan': 'خطة إنتاج'
    };

    const doc_name_ar = doctype_ar[frm.doctype] || frm.doctype;

    let message = `السلام عليكم ورحمة الله وبركاته\n\nنرسل لكم ${doc_name_ar} رقم: ${frm.doc.name}\n\n`;

    // إضافة تفاصيل حسب نوع المستند
    if (frm.doc.customer) message += `العميل: ${frm.doc.customer}\n`;
    if (frm.doc.supplier) message += `المورد: ${frm.doc.supplier}\n`;
    if (frm.doc.posting_date) message += `التاريخ: ${frm.doc.posting_date}\n`;
    if (frm.doc.transaction_date) message += `التاريخ: ${frm.doc.transaction_date}\n`;
    if (frm.doc.grand_total) message += `المبلغ الإجمالي: ${format_currency(frm.doc.grand_total, frm.doc.currency || 'YER')}\n`;
    if (frm.doc.total_amount) message += `المبلغ الإجمالي: ${format_currency(frm.doc.total_amount, frm.doc.currency || 'YER')}\n`;
    if (frm.doc.status) message += `الحالة: ${frm.doc.status}\n`;

    message += `\nشكراً لكم\nفريق ${frappe.boot.sitename || 'العمل'}`;

    return message;
}

function sendWhatsAppDocument(frm, values) {
    console.log('📤 إرسال مستند عبر WhatsApp...');

    frappe.show_progress(__('جاري الإرسال...'), 0, 100, __('جاري تحضير المستند'));

    frappe.call({
        method: 'whatsapp.whatsapp.api.send_document_via_whatsapp',
        args: {
            doctype: frm.doctype,
            docname: frm.doc.name,
            sender_account: values.sender_account,
            recipient_type: values.recipient_type,
            phone_number: values.phone_number,
            customer: values.customer,
            supplier: values.supplier,
            contact: values.contact,
            send_type: values.send_type,
            message: values.message,
            print_format: values.print_format
        },
        callback: function(r) {
            frappe.hide_progress();
            if (r.message && r.message.success) {
                frappe.show_alert({
                    message: __('تم إرسال المستند بنجاح عبر WhatsApp'),
                    indicator: 'green'
                });

                // إضافة تعليق في المستند
                const recipient = r.message.recipient || values.phone_number || values.customer || values.supplier;
                frm.add_comment('Comment', __('تم إرسال المستند عبر WhatsApp إلى {0}', [recipient]));

                console.log('✅ تم الإرسال بنجاح');
            } else {
                frappe.msgprint({
                    title: __('خطأ في الإرسال'),
                    message: r.message ? r.message.error : __('حدث خطأ غير متوقع'),
                    indicator: 'red'
                });
                console.error('❌ فشل في الإرسال:', r.message);
            }
        },
        error: function(r) {
            frappe.hide_progress();
            frappe.msgprint({
                title: __('خطأ في الإرسال'),
                message: __('فشل في إرسال المستند. يرجى المحاولة مرة أخرى.'),
                indicator: 'red'
            });
            console.error('❌ خطأ في الإرسال:', r);
        }
    });
}

function sendDocumentQuick(frm, type) {
    console.log(`📤 إرسال سريع: ${type}`);

    // محاولة الحصول على رقم هاتف من المستند
    let defaultPhone = getDefaultPhone(frm);
    if (defaultPhone === '+967') {
        // إذا لم يوجد رقم في المستند، محاولة الحصول من العميل/المورد
        if (frm.doc.customer || frm.doc.supplier) {
            defaultPhone = 'سيتم البحث عن الرقم تلقائياً';
        }
    }

    const phone = prompt(`أدخل رقم الهاتف:\n(أو اتركه فارغاً للبحث التلقائي)`, defaultPhone);
    if (phone === null) return; // المستخدم ألغى العملية

    frappe.show_progress(__('جاري الإرسال...'), 50, 100);

    let send_type, message, recipient_type = 'رقم هاتف';
    let customer = null, supplier = null;

    // تحديد نوع الإرسال والمستقبل
    if (phone === '' || phone === 'سيتم البحث عن الرقم تلقائياً') {
        if (frm.doc.customer) {
            recipient_type = 'عميل';
            customer = frm.doc.customer;
        } else if (frm.doc.supplier) {
            recipient_type = 'مورد';
            supplier = frm.doc.supplier;
        } else {
            frappe.hide_progress();
            frappe.msgprint(__('لم يتم العثور على رقم هاتف أو عميل/مورد'));
            return;
        }
    }

    if (type === 'PDF') {
        send_type = 'PDF فقط';
        message = `📄 ${frm.doctype} رقم: ${frm.doc.name}`;
    } else {
        send_type = 'رابط المستند';
        message = `🔗 رابط ${frm.doctype} رقم: ${frm.doc.name}`;
    }

    frappe.call({
        method: 'whatsapp.whatsapp.api.send_document_via_whatsapp',
        args: {
            doctype: frm.doctype,
            docname: frm.doc.name,
            sender_account: null,
            recipient_type: recipient_type,
            phone_number: phone !== 'سيتم البحث عن الرقم تلقائياً' ? phone : null,
            customer: customer,
            supplier: supplier,
            send_type: send_type,
            message: message
        },
        callback: function(r) {
            frappe.hide_progress();
            if (r.message && r.message.success) {
                frappe.show_alert({
                    message: __('تم الإرسال بنجاح إلى {0}', [r.message.recipient]),
                    indicator: 'green'
                });
                frm.add_comment('Comment', __('تم إرسال {0} عبر WhatsApp', [type]));
            } else {
                frappe.msgprint(__('فشل في الإرسال: {0}', [r.message ? r.message.error : 'خطأ غير معروف']));
            }
        },
        error: function() {
            frappe.hide_progress();
            frappe.msgprint(__('حدث خطأ في الإرسال'));
        }
    });
}

function sendMessageOnly(frm) {
    console.log('💬 إرسال رسالة فقط');

    const message = prompt('أدخل الرسالة:', getDefaultMessage(frm));
    if (!message) return;

    const phone = prompt('أدخل رقم الهاتف:', getDefaultPhone(frm));
    if (!phone) return;

    frappe.show_progress(__('جاري إرسال الرسالة...'), 50, 100);

    frappe.call({
        method: 'whatsapp.whatsapp.api.send_document_via_whatsapp',
        args: {
            doctype: frm.doctype,
            docname: frm.doc.name,
            sender_account: null,
            recipient_type: 'رقم هاتف',
            phone_number: phone,
            send_type: 'رسالة فقط',
            message: message
        },
        callback: function(r) {
            frappe.hide_progress();
            if (r.message && r.message.success) {
                frappe.show_alert({
                    message: __('تم إرسال الرسالة بنجاح'),
                    indicator: 'green'
                });
                frm.add_comment('Comment', __('تم إرسال رسالة WhatsApp إلى {0}', [phone]));
            } else {
                frappe.msgprint(__('فشل في إرسال الرسالة: {0}', [r.message ? r.message.error : 'خطأ غير معروف']));
            }
        },
        error: function() {
            frappe.hide_progress();
            frappe.msgprint(__('حدث خطأ في إرسال الرسالة'));
        }
    });
}

console.log('✅ تم تحميل WhatsApp Global Integration بنجاح');
